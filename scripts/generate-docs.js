import { readdir, readFile, writeFile, mkdir } from 'fs/promises';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function generateDocsJson() {
  try {
    const docsPath = join(__dirname, '..', 'docs');
    const staticPath = join(__dirname, '..', 'static');
    
    // Ensure static directory exists
    await mkdir(staticPath, { recursive: true });
    
    // Read all files in docs directory
    const files = await readdir(docsPath);
    const markdownFiles = files.filter(file => file.endsWith('.md'));
    
    // Generate file list for the index
    const fileList = markdownFiles.map(file => ({
      id: file.replace('.md', '').toLowerCase().replace(/[^a-z0-9]/g, '-'),
      label: file.replace('.md', '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      filename: file
    }));
    
    // Read content of each file
    const filesWithContent = {};
    for (const file of markdownFiles) {
      const filePath = join(docsPath, file);
      const content = await readFile(filePath, 'utf-8');
      filesWithContent[file] = content;
    }
    
    // Write the index file
    await writeFile(
      join(staticPath, 'docs-index.json'),
      JSON.stringify(fileList, null, 2),
      'utf-8'
    );
    
    // Write individual file contents
    await writeFile(
      join(staticPath, 'docs-content.json'),
      JSON.stringify(filesWithContent, null, 2),
      'utf-8'
    );
    
    console.log(`Generated docs JSON files with ${markdownFiles.length} documents:`);
    markdownFiles.forEach(file => console.log(`  - ${file}`));
    
  } catch (error) {
    console.error('Error generating docs JSON:', error);
    process.exit(1);
  }
}

generateDocsJson(); 