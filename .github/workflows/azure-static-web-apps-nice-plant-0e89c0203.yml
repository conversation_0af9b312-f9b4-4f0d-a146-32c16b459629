name: EJP Test

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - main

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    permissions:
      id-token: write
      contents: read

    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci

      - name: Build SvelteKit static site
        run: npm run build
        env:
          VITE_API_URL: https://api-test.easyjobplanner.com

      - name: Copy staticwebapp.config.json to build directory
        run: cp staticwebapp.config.json build/staticwebapp.config.json

      - name: Get Id Token
        uses: actions/github-script@v6
        id: idtoken
        with:
          script: |
            return await core.getIDToken()
          result-encoding: string

      - name: Deploy to Azure Static Web Apps
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_NICE_PLANT_0E89C0203 }}
          action: "upload"
          app_location: "build"          # 👈 SvelteKit static adapter outputs here
          output_location: ""             # 👈 leave empty when uploading static dir
          github_id_token: ${{ steps.idtoken.outputs.result }}

  close_pull_request_job:
    if: github.event_name == 'pull_request' && github.event.action == 'closed'
    runs-on: ubuntu-latest
    name: Close Pull Request Job
    steps:
      - name: Close Pull Request
        id: closepullrequest
        uses: Azure/static-web-apps-deploy@v1
        with:
          action: "close"
