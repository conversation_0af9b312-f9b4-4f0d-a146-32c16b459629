{"name": "ejp-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "npm run generate:docs && vite build", "generate:docs": "node scripts/generate-docs.js", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "devDependencies": {"@erbelion/vite-plugin-sveltekit-purgecss": "^0.1.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@types/date-fns": "^2.5.3", "@types/node": "^22.15.21", "less": "^4.3.0", "purgecss": "^7.0.2", "svelte": "^5.0.0", "svelte-check": "^4.2.1", "typescript": "^5.0.0", "vite": "^6.2.6"}, "dependencies": {"date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lottie-web": "^5.12.2", "marked": "^15.0.12", "svelte-dnd-action": "^0.9.61", "svelte-lottie": "^0.0.0"}}