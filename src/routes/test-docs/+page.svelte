<script lang="ts">
  import { onMount } from 'svelte';
  import { getDocsList, getDocContent, type DocFile } from '$lib/api/docs';
  
  let docsList: DocFile[] = [];
  let selectedDoc: string = '';
  let docContent: string = '';
  let loading = false;
  let error = '';

  onMount(async () => {
    try {
      docsList = await getDocsList();
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load docs';
    }
  });

  async function loadDoc(filename: string) {
    if (!filename) return;
    
    loading = true;
    error = '';
    
    try {
      docContent = await getDocContent(filename);
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load document';
      docContent = '';
    } finally {
      loading = false;
    }
  }

  $: if (selectedDoc) {
    loadDoc(selectedDoc);
  }
</script>

<svelte:head>
  <title>Docs Test</title>
</svelte:head>

<div class="container">
  <h1>Documentation Test</h1>
  
  <div class="docs-interface">
    <div class="docs-sidebar">
      <h2>Available Docs</h2>
      {#if docsList.length > 0}
        <ul>
          {#each docsList as doc}
            <li>
              <button
                class="doc-button"
                class:active={selectedDoc === doc.filename}
                on:click={() => selectedDoc = doc.filename}
              >
                {doc.label}
              </button>
            </li>
          {/each}
        </ul>
      {:else}
        <p>Loading docs...</p>
      {/if}
    </div>
    
    <div class="docs-content">
      {#if error}
        <div class="error">
          <h3>Error</h3>
          <p>{error}</p>
        </div>
      {:else if loading}
        <div class="loading">
          <p>Loading document...</p>
        </div>
      {:else if docContent}
        <div class="document">
          <h3>{docsList.find(d => d.filename === selectedDoc)?.label || 'Document'}</h3>
          <pre class="doc-content">{docContent}</pre>
        </div>
      {:else}
        <div class="empty-state">
          <p>Select a document from the sidebar to view its content.</p>
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  h1 {
    margin-bottom: 2rem;
    color: #333;
  }

  .docs-interface {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    min-height: 600px;
  }

  .docs-sidebar {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
    background: #f9f9f9;
  }

  .docs-sidebar h2 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    color: #333;
  }

  .docs-sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .docs-sidebar li {
    margin-bottom: 0.5rem;
  }

  .doc-button {
    width: 100%;
    padding: 0.75rem;
    text-align: left;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .doc-button:hover {
    background: #f0f0f0;
    border-color: #007bff;
  }

  .doc-button.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
  }

  .docs-content {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1.5rem;
    background: white;
  }

  .error {
    color: #dc3545;
  }

  .loading {
    color: #6c757d;
  }

  .empty-state {
    color: #6c757d;
    text-align: center;
    padding: 2rem;
  }

  .document h3 {
    margin: 0 0 1rem 0;
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
  }

  .doc-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    max-height: 500px;
    overflow-y: auto;
  }

  @media (max-width: 768px) {
    .docs-interface {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .docs-sidebar {
      order: 2;
    }
    
    .docs-content {
      order: 1;
    }
  }
</style> 