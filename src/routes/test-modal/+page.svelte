<script lang="ts">
  import Modal from '$lib/components/Modal.svelte';
  
  let showBasicModal = false;
  let showContentModal = false;
  let showConfirmModal = false;
</script>

<svelte:head>
  <title>Modal Animation Test</title>
</svelte:head>

<div class="container">
  <h1>Modal Animation Test</h1>
  <p>Test the smooth modal animations with different modal types:</p>
  
  <div class="button-grid">
    <button 
      class="test-button" 
      on:click={() => showBasicModal = true}
    >
      Show Basic Modal
    </button>
    
    <button 
      class="test-button" 
      on:click={() => showContentModal = true}
    >
      Show Content Modal
    </button>
    
    <button 
      class="test-button" 
      on:click={() => showConfirmModal = true}
    >
      Show Confirm Modal
    </button>
  </div>
</div>

<!-- Basic Modal -->
<Modal bind:show={showBasicModal} title="Basic Modal">
  <p>This is a basic modal with smooth animations!</p>
  <p>The backdrop fades in and the modal scales in with a slight delay for a polished effect.</p>
</Modal>

<!-- Content Modal -->
<Modal bind:show={showContentModal} title="Content-Rich Modal">
  <div class="rich-content">
    <h3>Rich Content</h3>
    <p>This modal contains more complex content to demonstrate how the animations work with different content types.</p>
    
    <div class="feature-list">
      <h4>Animation Features:</h4>
      <ul>
        <li>✨ Backdrop fades in smoothly (300ms)</li>
        <li>🎭 Modal scales from 70% to 100% with elastic easing</li>
        <li>⏱️ 150ms delay on modal entrance for layered effect</li>
        <li>🎯 Transform origin set to center for balanced scaling</li>
        <li>🔧 Hardware acceleration with will-change properties</li>
      </ul>
    </div>
    
    <div class="sample-form">
      <h4>Sample Form:</h4>
      <input type="text" placeholder="Enter some text" />
      <textarea placeholder="Enter a longer message..."></textarea>
    </div>
  </div>
</Modal>

<!-- Confirm Modal -->
<Modal bind:show={showConfirmModal} title="Confirm Action">
  <p>Are you sure you want to perform this action?</p>
  <p>This action cannot be undone.</p>
  
  <div slot="footer" class="confirm-footer">
    <button class="cancel-btn" on:click={() => showConfirmModal = false}>
      Cancel
    </button>
    <button class="confirm-btn" on:click={() => showConfirmModal = false}>
      Confirm
    </button>
  </div>
</Modal>

<style>
  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  h1 {
    color: #333;
    margin-bottom: 1rem;
  }

  .button-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
  }

  .test-button {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }

  .test-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  }

  .test-button:active {
    transform: translateY(0);
  }

  .rich-content h3 {
    color: #667eea;
    margin-bottom: 1rem;
  }

  .feature-list {
    background: #f8f9ff;
    padding: 1rem;
    border-radius: 6px;
    margin: 1rem 0;
  }

  .feature-list h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
  }

  .feature-list ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .feature-list li {
    margin-bottom: 0.25rem;
  }

  .sample-form {
    margin-top: 1rem;
  }

  .sample-form h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
  }

  .sample-form input,
  .sample-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    font-family: inherit;
  }

  .sample-form textarea {
    resize: vertical;
    min-height: 80px;
  }

  .confirm-footer {
    display: flex;
    gap: 0.5rem;
  }

  .cancel-btn,
  .confirm-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .cancel-btn {
    background: #6c757d;
    color: white;
  }

  .cancel-btn:hover {
    background: #5a6268;
  }

  .confirm-btn {
    background: #dc3545;
    color: white;
  }

  .confirm-btn:hover {
    background: #c82333;
  }
</style> 