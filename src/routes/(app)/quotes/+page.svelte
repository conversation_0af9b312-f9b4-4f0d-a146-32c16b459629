<script lang="ts">
  import { onMount } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import Tabs from '$lib/components/Tabs.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { goto } from '$app/navigation';
  import {
    getQuotes,
    getQuoteStatuses,
    getQuoteTemplates,
    createQuoteTemplate,
    updateQuoteTemplate,
    deleteQuoteTemplate,
    type QuoteTemplate,
    type QuoteTemplateSection
  } from '$lib/api/quotes';
  import type { Quote, QuoteStatus } from '$lib/api/quotes';
  import { formatCurrency } from '$lib/config/currency';
  import StatCard from '$lib/components/StatCard.svelte';

  // Tab state
  let activeTab: 'quotes' | 'designer' = 'quotes';

  // Tab configuration
  const tabs = [
    { id: 'quotes', label: 'Quotes' },
    { id: 'designer', label: 'Quote Designer' }
  ];

  function handleTabChange(event: CustomEvent<{ tabId: string }>) {
    activeTab = event.detail.tabId as 'quotes' | 'designer';
  }

  // Quotes data
  let quotes: Quote[] = [];
  let quoteStatuses: QuoteStatus[] = [];
  let loading = true;
  let searchQuery = '';
  let statusFilter = 'All';

  // Template designer data
  let templates: QuoteTemplate[] = [];
  let isTemplateLoading = false;
  let selectedTemplate: QuoteTemplate | null = null;
  let isEditing = false;
  let isSaving = false;

  // Template form data
  let templateForm = {
    name: '',
    description: '',
    isDefault: false,
    sections: [] as QuoteTemplateSection[]
  };

  onMount(async () => {
    await loadData();
    await loadTemplates();
  });

  async function loadData() {
    loading = true;
    try {
      const [quotesData, statusesData] = await Promise.all([
        getQuotes(),
        getQuoteStatuses()
      ]);
      quotes = quotesData;
      quoteStatuses = statusesData;
    } catch (error) {
      console.error('Error loading quotes:', error);
      addToast({ message: 'Failed to load quotes', type: 'error' });
    } finally {
      loading = false;
    }
  }

  async function loadTemplates() {
    isTemplateLoading = true;
    try {
      templates = await getQuoteTemplates();
    } catch (error) {
      console.error('Error loading templates:', error);
      addToast({ message: 'Failed to load templates', type: 'error' });
    } finally {
      isTemplateLoading = false;
    }
  }

  // Filtered quotes based on search and status
  $: filteredQuotes = quotes.filter(quote => {
    const matchesSearch = quote.quoteNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         quote.customerName?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'All' || quote.status.name === statusFilter;

    return matchesSearch && matchesStatus;
  });

  function handleCreateQuote() {
    goto('/quotes/create');
  }

  function handleViewQuote(quote: Quote) {
    goto(`/quotes/${quote.id}`);
  }

  function getStatusColor(status: QuoteStatus): string {
    return status.color;
  }



  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  function getDraftQuotes(): Quote[] {
    return quotes.filter(quote => quote.status.name === 'Draft');
  }

  function getSentQuotes(): Quote[] {
    return quotes.filter(quote => quote.status.name === 'Sent');
  }

  function getAcceptedQuotes(): Quote[] {
    return quotes.filter(quote => quote.status.name === 'Accepted');
  }

  function getExpiredQuotes(): Quote[] {
    const today = new Date();
    return quotes.filter(quote =>
      quote.status.name !== 'Accepted' &&
      quote.status.name !== 'Converted' &&
      new Date(quote.expiryDate) < today
    );
  }

  function getTotalQuoteValue(): number {
    return quotes
      .filter(quote => quote.status.name === 'Accepted')
      .reduce((total, quote) => total + quote.totalAmount, 0);
  }

  // Template designer functions
  function startNewTemplate() {
    selectedTemplate = null;
    isEditing = true;
    resetForm();
  }

  function editTemplate(template: QuoteTemplate) {
    selectedTemplate = template;
    isEditing = true;
    populateForm(template);
  }

  function resetForm() {
    templateForm = {
      name: '',
      description: '',
      isDefault: false,
      sections: []
    };
  }

  function populateForm(template: QuoteTemplate) {
    templateForm = {
      name: template.name,
      description: template.description || '',
      isDefault: template.isDefault,
      sections: template.sections || []
    };
  }

  function cancelEdit() {
    isEditing = false;
    selectedTemplate = null;
    resetForm();
  }

  async function saveTemplate() {
    if (!templateForm.name.trim()) {
      addToast({ message: 'Template name is required', type: 'error' });
      return;
    }

    isSaving = true;
    try {
      const templateData = {
        name: templateForm.name,
        description: templateForm.description,
        isDefault: templateForm.isDefault,
        sections: templateForm.sections
      };

      if (selectedTemplate) {
        await updateQuoteTemplate(selectedTemplate.id, templateData);
        addToast({ message: 'Template updated successfully', type: 'success' });
      } else {
        await createQuoteTemplate(templateData);
        addToast({ message: 'Template created successfully', type: 'success' });
      }

      await loadTemplates();
      cancelEdit();
    } catch (error) {
      console.error('Error saving template:', error);
      addToast({ message: 'Failed to save template', type: 'error' });
    } finally {
      isSaving = false;
    }
  }

  async function deleteTemplate(template: QuoteTemplate) {
    if (!confirm(`Are you sure you want to delete the template "${template.name}"?`)) {
      return;
    }

    try {
      await deleteQuoteTemplate(template.id);
      addToast({ message: 'Template deleted successfully', type: 'success' });
      await loadTemplates();
    } catch (error) {
      console.error('Error deleting template:', error);
      addToast({ message: 'Failed to delete template', type: 'error' });
    }
  }
</script>

<svelte:head>
  <title>Quotes</title>
</svelte:head>

<div class="container">
  <PageHeader title="Quotes">
    <svelte:fragment slot="actions">
      <Button on:click={handleCreateQuote} variant="primary" type="button">
        Create Quote
      </Button>
    </svelte:fragment>
  </PageHeader>

  <!-- Tab Navigation -->
  <Tabs {tabs} {activeTab} on:change={handleTabChange} />

  <main>
    {#if activeTab === 'quotes'}
      {#if loading}
        <div class="loading-container">
          <LoadingSpinner />
          <p>Loading quotes...</p>
        </div>
      {:else}
      <!-- Stats -->
      <div class="stats">
        <StatCard title="Total Quote Value" value={formatCurrency(getTotalQuoteValue())} />
        <StatCard title="Total Quotes" value={quotes.length} />
        <StatCard title="Accepted" value={getAcceptedQuotes().length} />
        <StatCard title="Sent" value={getSentQuotes().length} />
        <StatCard title="Expired" value={getExpiredQuotes().length} valueClass="overdue" />
        <StatCard title="Drafts" value={getDraftQuotes().length} />
      </div>

      <!-- Filters and Search -->
      <div class="controls">
        <div class="search-section">
          <input
            type="text"
            placeholder="Search quotes..."
            bind:value={searchQuery}
            class="search-input"
          />
        </div>

        <div class="filter-section">
          <label for="status-filter">Status:</label>
          <select id="status-filter" bind:value={statusFilter}>
            <option value="All">All</option>
            {#each quoteStatuses as status}
              <option value={status.name}>{status.name}</option>
            {/each}
          </select>
        </div>
      </div>

      <!-- Quotes List -->
      {#if filteredQuotes.length === 0}
        <div class="empty-state">
          <h3>No quotes found</h3>
          <p>
            {#if searchQuery || statusFilter !== 'All'}
              Try adjusting your search or filters.
            {:else}
              Get started by creating your first quote.
            {/if}
          </p>
          {#if !searchQuery && statusFilter === 'All'}
            <Button on:click={handleCreateQuote} variant="primary">
              Create Quote
            </Button>
          {/if}
        </div>
      {:else}
        <div class="quotes-table">
          <div class="table-header">
            <div class="header-cell">Quote #</div>
            <div class="header-cell">Customer</div>
            <div class="header-cell">Issue Date</div>
            <div class="header-cell">Expiry Date</div>
            <div class="header-cell">Amount</div>
            <div class="header-cell">Status</div>
            <div class="header-cell">Actions</div>
          </div>

          {#each filteredQuotes as quote (quote.id)}
            <div class="table-row" on:click={() => handleViewQuote(quote)} role="button" tabindex="0">
              <div class="table-cell">
                <span class="quote-number">{quote.quoteNumber}</span>
              </div>
              <div class="table-cell">
                <span class="customer-name">{quote.customerName || 'Unknown Customer'}</span>
              </div>
              <div class="table-cell">
                <span class="date">{formatDate(quote.issueDate)}</span>
              </div>
              <div class="table-cell">
                <span class="date" class:expired={new Date(quote.expiryDate) < new Date() && quote.status.name !== 'Accepted'}>
                  {formatDate(quote.expiryDate)}
                </span>
              </div>
              <div class="table-cell">
                <span class="amount">{formatCurrency(quote.totalAmount)}</span>
              </div>
              <div class="table-cell">
                <span class="status-badge" style="background-color: {getStatusColor(quote.status)}">
                  {quote.status.name}
                </span>
              </div>
              <div class="table-cell">
                <Button
                  variant="secondary"
                  size="small"
                  on:click={(e) => { e.stopPropagation(); handleViewQuote(quote); }}
                >
                  View
                </Button>
              </div>
            </div>
          {/each}
        </div>
      {/if}
      {/if}
    {:else if activeTab === 'designer'}
      <!-- Template Designer Content -->
      {#if isTemplateLoading}
        <div class="loading-container">
          <LoadingSpinner />
          <p>Loading templates...</p>
        </div>
      {:else}
        <div class="designer-container">
          {#if !isEditing}
            <!-- Template List View -->
            <div class="templates-section">
              <div class="section-header">
                <h2>Quote Templates</h2>
                <Button variant="primary" on:click={startNewTemplate}>
                  Create New Template
                </Button>
              </div>

              {#if templates.length === 0}
                <div class="empty-state">
                  <h3>No templates found</h3>
                  <p>Create your first quote template to get started.</p>
                  <Button variant="primary" on:click={startNewTemplate}>
                    Create Template
                  </Button>
                </div>
              {:else}
                <div class="templates-grid">
                  {#each templates as template (template.id)}
                    <div class="template-card">
                      <div class="template-header">
                        <h3>{template.name}</h3>
                        {#if template.isDefault}
                          <span class="default-badge">Default</span>
                        {/if}
                      </div>
                      {#if template.description}
                        <p class="template-description">{template.description}</p>
                      {/if}
                      <div class="template-actions">
                        <Button variant="secondary" size="small" on:click={() => editTemplate(template)}>
                          Edit
                        </Button>
                        <Button variant="tertiary" size="small" on:click={() => deleteTemplate(template)}>
                          Delete
                        </Button>
                      </div>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          {:else}
            <!-- Template Editor View -->
            <div class="template-editor">
              <div class="editor-header">
                <h2>{selectedTemplate ? 'Edit Template' : 'Create New Template'}</h2>
                <div class="editor-actions">
                  <Button variant="tertiary" on:click={cancelEdit}>Cancel</Button>
                  <Button variant="primary" on:click={saveTemplate} disabled={isSaving}>
                    {isSaving ? 'Saving...' : 'Save Template'}
                  </Button>
                </div>
              </div>

              <div class="editor-form">
                <div class="form-group">
                  <label for="template-name">Template Name *</label>
                  <input
                    id="template-name"
                    type="text"
                    bind:value={templateForm.name}
                    placeholder="Enter template name"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="template-description">Description</label>
                  <textarea
                    id="template-description"
                    bind:value={templateForm.description}
                    placeholder="Enter template description (optional)"
                    rows="3"
                  ></textarea>
                </div>

                <div class="form-group">
                  <label>
                    <input
                      type="checkbox"
                      bind:checked={templateForm.isDefault}
                    />
                    Set as default template
                  </label>
                </div>
              </div>
            </div>
          {/if}
        </div>
      {/if}
    {/if}
  </main>
</div>

<style lang="less">


  main {
    padding: 1rem 2rem;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;

    .search-section {
      flex: 1;
      max-width: 400px;

      .search-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      label {
        font-weight: 500;
        color: var(--black);
      }

      select {
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        min-width: 120px;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    p {
      margin: 0 0 2rem 0;
      color: var(--grey);
    }
  }

  .quotes-table {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;

    .table-header {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr auto;
      background: var(--bg);
      border-bottom: 1px solid var(--border);

      .header-cell {
        padding: 1rem;
        font-weight: 600;
        color: var(--black);
        font-size: 0.9rem;
      }
    }

    .table-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr auto;
      border-bottom: 1px solid var(--border);
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: var(--bg);
      }

      &:last-child {
        border-bottom: none;
      }

      .table-cell {
        padding: 1rem;
        display: flex;
        align-items: center;
        font-size: 0.9rem;

        .quote-number {
          font-weight: 600;
          color: var(--primary);
        }

        .customer-name {
          color: var(--black);
        }

        .date {
          color: var(--grey);

          &.expired {
            color: #EF4444;
            font-weight: 500;
          }
        }

        .amount {
          font-weight: 600;
          color: var(--black);
        }

        .status-badge {
          font-size: 0.7rem;
          font-weight: 500;
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: var(--br);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  // Template Designer Styles
  .designer-container {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;

      h2 {
        margin: 0;
        color: var(--black);
      }
    }

    .templates-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1.5rem;
    }

    .template-card {
      background: white;
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 1.5rem;
      transition: box-shadow 0.2s;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .template-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;

        h3 {
          margin: 0;
          color: var(--black);
          font-size: 1.1rem;
        }

        .default-badge {
          background: var(--primary);
          color: white;
          font-size: 0.7rem;
          padding: 0.25rem 0.5rem;
          border-radius: var(--br);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }

      .template-description {
        color: var(--grey);
        margin: 0 0 1rem 0;
        font-size: 0.9rem;
        line-height: 1.4;
      }

      .template-actions {
        display: flex;
        gap: 0.5rem;
      }
    }

    .template-editor {
      .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border);

        h2 {
          margin: 0;
          color: var(--black);
        }

        .editor-actions {
          display: flex;
          gap: 1rem;
        }
      }

      .editor-form {
        max-width: 600px;
      }
    }
  }

  @media (max-width: 768px) {
    .controls {
      flex-direction: column;
      align-items: stretch;
    }

    .quotes-table {
      .table-header,
      .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .header-cell,
      .table-cell {
        padding: 0.5rem 1rem;
      }

      .table-row {
        padding: 1rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        margin-bottom: 1rem;
        background: white;

        .table-cell {
          display: flex;
          justify-content: space-between;
          padding: 0.25rem 0;

          &::before {
            content: attr(data-label);
            font-weight: 600;
            color: var(--grey);
          }
        }
      }
    }
  }
</style>
