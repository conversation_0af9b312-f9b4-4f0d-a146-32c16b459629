<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import Button from '$lib/components/Button.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import {
    getQuoteById,
    updateQuote,
    deleteQuote,
    convertQuoteToInvoice,
    type Quote
  } from '$lib/api/quotes';
  import { customers } from '$lib/stores/customerStore';
  import { formatCurrency } from '$lib/config/currency';

  let loading = true;
  let quote: Quote | null = null;
  let quoteId: string;

  $: quoteId = $page.params.id;

  onMount(async () => {
    await loadQuote();
  });

  async function loadQuote() {
    loading = true;
    try {
      quote = await getQuoteById(quoteId);
      if (!quote) {
        addToast({ message: 'Quote not found', type: 'error' });
        goto('/quotes');
      }
    } catch (error) {
      console.error('Error loading quote:', error);
      addToast({ message: 'Failed to load quote', type: 'error' });
      goto('/quotes');
    } finally {
      loading = false;
    }
  }

  async function handleStatusUpdate(newStatus: string) {
    if (!quote) return;

    try {
      const updatedQuote = await updateQuote(quote.id, {
        status: {
          id: Date.now().toString(),
          name: newStatus as any,
          color: getStatusColor(newStatus)
        }
      });
      quote = updatedQuote;
      addToast({ message: 'Quote status updated', type: 'success' });
    } catch (error) {
      console.error('Error updating quote status:', error);
      addToast({ message: 'Failed to update quote status', type: 'error' });
    }
  }

  async function handleConvertToInvoice() {
    if (!quote) return;

    try {
      const invoiceId = await convertQuoteToInvoice(quote.id);
      addToast({ message: 'Quote converted to invoice successfully', type: 'success' });
      goto(`/invoices/${invoiceId}`);
    } catch (error) {
      console.error('Error converting quote to invoice:', error);
      addToast({ message: 'Failed to convert quote to invoice', type: 'error' });
    }
  }

  async function handleDelete() {
    if (!quote) return;

    if (window.confirm(`Are you sure you want to delete quote ${quote.quoteNumber}?`)) {
      try {
        await deleteQuote(quote.id);
        addToast({ message: 'Quote deleted successfully', type: 'success' });
        goto('/quotes');
      } catch (error) {
        console.error('Error deleting quote:', error);
        addToast({ message: 'Failed to delete quote', type: 'error' });
      }
    }
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'Draft': return '#6B7280';
      case 'Sent': return '#3B82F6';
      case 'Accepted': return '#10B981';
      case 'Rejected': return '#EF4444';
      case 'Expired': return '#F59E0B';
      default: return '#6B7280';
    }
  }

  function getCustomerName(customerId: string): string {
    const customer = $customers.find(c => c.id === customerId);
    return customer ? (customer.companyName || customer.fullName) : 'Unknown Customer';
  }



  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
</script>

<svelte:head>
  <title>{quote ? `Quote ${quote.quoteNumber}` : 'Quote Details'}</title>
</svelte:head>

<div class="container">
  {#if loading}
    <div class="loading-container">
      <LoadingSpinner />
      <p>Loading quote...</p>
    </div>
  {:else if quote}
    <PageHeader title="Quote {quote.quoteNumber}">
      <svelte:fragment slot="actions">
        <Button variant="tertiary" on:click={() => goto('/quotes')}>
          Back to Quotes
        </Button>
        <Button variant="secondary" on:click={() => quote && goto(`/quotes/${quote.id}/edit`)}>
          Edit Quote
        </Button>
        {#if quote.status.name === 'Accepted'}
          <Button variant="primary" on:click={handleConvertToInvoice}>
            Convert to Invoice
          </Button>
        {/if}
        <Button variant="tertiary" on:click={handleDelete}>
          Delete
        </Button>
      </svelte:fragment>
    </PageHeader>

    <main>
      <div class="quote-details">
        <!-- Quote Header -->
        <div class="quote-header">
          <div class="quote-info">
            <h2>Quote #{quote.quoteNumber}</h2>
            <div class="quote-meta">
              <div class="meta-item">
                <span class="label">Customer:</span>
                <span class="value">{getCustomerName(quote.customerId)}</span>
              </div>
              <div class="meta-item">
                <span class="label">Issue Date:</span>
                <span class="value">{formatDate(quote.issueDate)}</span>
              </div>
              <div class="meta-item">
                <span class="label">Valid Until:</span>
                <span class="value">{formatDate(quote.expiryDate || quote.issueDate)}</span>
              </div>
              <div class="meta-item">
                <span class="label">Status:</span>
                <span class="status-badge" style="background-color: {quote.status.color}">
                  {quote.status.name}
                </span>
              </div>
            </div>
          </div>

          <div class="quote-actions">
            <div class="status-controls">
              <label for="status">Update Status:</label>
              <select
                id="status"
                value={quote.status.name}
                on:change={(e) => handleStatusUpdate((e.target as HTMLSelectElement).value)}
              >
                <option value="Draft">Draft</option>
                <option value="Sent">Sent</option>
                <option value="Accepted">Accepted</option>
                <option value="Rejected">Rejected</option>
                <option value="Expired">Expired</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Quote Sections -->
        <div class="quote-content">
          {#each quote.sections as section}
            <div class="quote-section">
              <h3>{section.title}</h3>

              {#if section.type === 'text'}
                <div class="section-content">
                  {section.content}
                </div>
              {:else if section.type === 'lineItems'}
                <div class="line-items">
                  <div class="items-table">
                    <div class="items-header">
                      <div class="item-cell">Description</div>
                      <div class="item-cell">Quantity</div>
                      <div class="item-cell">Unit Price</div>
                      <div class="item-cell">Total</div>
                    </div>

                    {#each section.lineItems || [] as item}
                      <div class="items-row">
                        <div class="item-cell">
                          <div class="item-description">
                            <strong>{item.description}</strong>
                            {#if item.additionalInfo}
                              <div class="additional-info">{item.additionalInfo}</div>
                            {/if}
                          </div>
                        </div>
                        <div class="item-cell">{item.quantity}</div>
                        <div class="item-cell">{formatCurrency(item.unitPrice)}</div>
                        <div class="item-cell">{formatCurrency(item.quantity * item.unitPrice)}</div>
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}
            </div>
          {/each}
        </div>

        <!-- Quote Totals -->
        <div class="quote-totals">
          <div class="totals-section">
            <div class="totals-row">
              <span class="label">Subtotal:</span>
              <span class="value">{formatCurrency(quote.subtotal)}</span>
            </div>
            <div class="totals-row">
              <span class="label">Tax:</span>
              <span class="value">{formatCurrency(quote.taxAmount)}</span>
            </div>
            <div class="totals-row total">
              <span class="label">Total:</span>
              <span class="value">{formatCurrency(quote.totalAmount)}</span>
            </div>
          </div>
        </div>

        <!-- Quote Notes -->
        {#if quote.notes}
          <div class="quote-notes">
            <h3>Notes</h3>
            <p>{quote.notes}</p>
          </div>
        {/if}

        <!-- Quote Terms -->
        {#if quote.terms}
          <div class="quote-terms">
            <h3>Terms & Conditions</h3>
            <p>{quote.terms}</p>
          </div>
        {/if}
      </div>
    </main>
  {:else}
    <div class="error-container">
      <h2>Quote Not Found</h2>
      <p>The requested quote could not be found.</p>
      <Button on:click={() => goto('/quotes')}>Back to Quotes</Button>
    </div>
  {/if}
</div>

<style lang="less">
  main {
    padding: 1rem 2rem;
  }

  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .quote-details {
    max-width: 1000px;
    margin: 0 auto;
  }

  .quote-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 2rem;
    margin-bottom: 2rem;

    .quote-info {
      h2 {
        margin: 0 0 1rem 0;
        color: var(--primary);
      }

      .quote-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;

        .meta-item {
          .label {
            font-weight: 600;
            color: var(--grey);
            margin-right: 0.5rem;
          }

          .value {
            color: var(--text);
          }

          .status-badge {
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
          }
        }
      }
    }

    .quote-actions {
      .status-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        label {
          font-weight: 500;
          color: var(--text);
        }

        select {
          padding: 0.5rem;
          border: 1px solid var(--border);
          border-radius: var(--br);
          background: white;
        }
      }
    }
  }

  .quote-content {
    .quote-section {
      background: white;
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 2rem;
      margin-bottom: 1.5rem;

      h3 {
        margin: 0 0 1rem 0;
        color: var(--primary);
        border-bottom: 1px solid var(--border);
        padding-bottom: 0.5rem;
      }

      .section-content {
        line-height: 1.6;
        color: var(--text);
        white-space: pre-line;
      }
    }
  }

  .line-items {
    .items-table {
      border: 1px solid var(--border);
      border-radius: var(--br);
      overflow: hidden;
    }

    .items-header {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr;
      background: var(--bg);
      font-weight: 600;
      color: var(--grey);

      .item-cell {
        padding: 1rem;
        border-right: 1px solid var(--border);

        &:last-child {
          border-right: none;
        }
      }
    }

    .items-row {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr;
      border-top: 1px solid var(--border);

      .item-cell {
        padding: 1rem;
        border-right: 1px solid var(--border);
        display: flex;
        align-items: center;

        &:last-child {
          border-right: none;
        }
      }

      .item-description {
        .additional-info {
          font-size: 0.875rem;
          color: var(--grey);
          margin-top: 0.25rem;
        }
      }
    }
  }

  .quote-totals {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 2rem;
    margin-bottom: 1.5rem;

    .totals-section {
      max-width: 300px;
      margin-left: auto;

      .totals-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;

        &.total {
          font-weight: 700;
          font-size: 1.125rem;
          margin-top: 1rem;
          padding-top: 1rem;
          border-top: 1px solid var(--border);
        }

        .label {
          color: var(--grey);
        }

        .value {
          color: var(--text);
        }
      }
    }
  }

  .quote-notes, .quote-terms {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 2rem;
    margin-bottom: 1.5rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--primary);
    }

    p {
      margin: 0;
      line-height: 1.6;
      color: var(--text);
      white-space: pre-line;
    }
  }

  @media (max-width: 768px) {
    .quote-header {
      flex-direction: column;
      gap: 1rem;
    }

    .items-header, .items-row {
      grid-template-columns: 1fr;

      .item-cell {
        border-right: none;
        border-bottom: 1px solid var(--border);

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
</style>