<script context="module" lang="ts">
  // Define types directly in the component for now
  export interface CustomerGridItem {
    id: string; // Or number, depending on your API
    name: string;
    email?: string;
    phone?: string;
    // Add other properties that come from your API for the grid
  }
</script>

<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { customerStore } from '$lib/stores/customerStore'; // Placeholder, might still cause issues if store is not functional
  import Modal from '$lib/components/Modal.svelte';
  import ConfirmDelete from '$lib/components/ConfirmDelete.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import Grid, { type HeaderConfig, type SortState, type SearchFieldConfig } from '$lib/components/Grid.svelte';
  import { user } from '$lib/stores/auth';
  import { api, ApiError } from '$lib/utils/api';
  import type { Contact } from '$lib/api/contacts';

  let customers: Contact[] = [];
  let loading = true;
  let error: string | null = null;
  let showDeleteModal = false;
  let customerToDelete: Contact | null = null;
  let showCreateModal = false;

  let totalCustomerItems = 0;

  // --- Grid State ---
  let gridHeaders: HeaderConfig[] = [
    { key: 'fullName', text: 'Name', sortable: true },
    { key: 'email', text: 'Email', sortable: true },
    { key: 'phone', text: 'Phone', sortable: true },
    { key: 'actions', text: 'Actions', sortable: false },
  ];

  let currentSort: SortState = { key: '', direction: '' };

  let searchFields: SearchFieldConfig[] = [
    { displayName: 'Search', queryKey: 'query', currentQuery: '' },
  ];

  let itemsPerPageOptions = [10, 25, 50];
  let itemsPerPage = itemsPerPageOptions[0];
  let currentPage = 1;
  // totalItems is already defined as totalCustomerItems
  // --- End Grid State ---

  // Redirect to login if not logged in
  onMount(() => {
    const unsubscribe = user.subscribe(value => {
      if (!value) {
        goto('/login');
      }
    });

    // Fetch customers when component mounts
    // Pass initial grid parameters
    fetchCustomers(currentPage, itemsPerPage, {}, currentSort);

    return unsubscribe;
  });

  // Update fetchCustomers to accept pagination, search, and sort parameters
  async function fetchCustomers(
    page: number,
    limit: number,
    searchQueries: Record<string, string>,
    sortState: SortState
  ) {
    loading = true;
    error = null;
    try {
      let response: any[];
      
      // Check if there's a search query
      const searchQuery = searchQueries.query;
      if (searchQuery && searchQuery.trim()) {
        // Use search endpoint
        response = await api.get<any[]>(`/customers/search?query=${encodeURIComponent(searchQuery.trim())}`);
      } else {
        // Use regular customers endpoint
        response = await api.get<any[]>('/Customers');
      }
      
      // Check if the response is an array before assigning
      if (Array.isArray(response)) {
        // Transform API response to Contact format
        customers = response.map((customer: any) => ({
          id: customer.id,
          fullName: customer.name || 'Unknown Customer',
          companyName: customer.companyName || '',
          emails: customer.emails || [{ email: customer.email || '', type: 'Work', isPrimary: true }],
          phones: customer.phones || [{ phone: customer.phone || '', type: 'Work', isPrimary: true }],
          addresses: customer.addresses || [],
          status: customer.status || 'Customer',
          notes: customer.notes || [],
          checklists: customer.checklists || [],
          communicationTimeline: customer.communicationTimeline || [],
          createdAt: customer.createdAt || new Date().toISOString(),
          updatedAt: customer.updatedAt || new Date().toISOString(),
          // Keep original fields for compatibility
          name: customer.name || 'Unknown Customer',
          email: customer.email || '',
          phone: customer.phone || ''
        }));
        // TODO: Get total items from API response if available, otherwise use current data length
        totalCustomerItems = customers.length; // This needs to be the total count from the backend
      } else {
        // Handle the case where the expected data is not in the response
        console.error('API response was not an array:', response);
        customers = [];
        totalCustomerItems = 0;
        error = 'Received unexpected data format from the server.'; // Set a user-friendly error
      }
    } catch (err) {
      if (err instanceof ApiError) {
        error = `Failed to fetch customers: ${err.message}`;
        console.error('API Error:', err.status, err.message);
      } else {
        error = 'An unexpected error occurred while fetching customers.';
        console.error('Fetch customers error:', err);
      }
    } finally {
      loading = false;
    }
  }

  // --- Grid Handler Functions ---
  function handleHeaderClick(headerKey: string) {
    // Implement sorting logic
    let direction: SortState['direction'] = 'ascending';
    if (currentSort.key === headerKey) {
      direction = currentSort.direction === 'ascending' ? 'descending' : '';
    }
    currentSort = { key: direction === '' ? '' : headerKey, direction };
    fetchCustomers(currentPage, itemsPerPage, getSearchQueries(), currentSort);
  }

  function handleSearch(searchQueries: Record<string, string>) {
    // Update searchFields state
    searchFields = searchFields.map(field => ({
        ...field,
        currentQuery: searchQueries[field.queryKey] || ''
    }));
    currentPage = 1; // Reset to first page on search
    fetchCustomers(currentPage, itemsPerPage, searchQueries, currentSort);
  }

  function getSearchQueries(): Record<string, string> {
      const queries: Record<string, string> = {};
      searchFields.forEach(field => {
        if (field.currentQuery && field.currentQuery.trim() !== '') {
          queries[field.queryKey] = field.currentQuery;
        }
      });
      return queries;
  }

  function handleItemsPerPageChange(newItemsPerPage: number) {
    itemsPerPage = newItemsPerPage;
    currentPage = 1; // Reset to first page on items per page change
    fetchCustomers(currentPage, itemsPerPage, getSearchQueries(), currentSort);
  }

  function handlePageChange(newPage: number) {
    currentPage = newPage;
    fetchCustomers(currentPage, itemsPerPage, getSearchQueries(), currentSort);
  }
  // --- End Grid Handler Functions ---

  function viewCustomerDetails(customerId: string) {
    customerStore.set({ id: customerId, name: '', email: '', phone: '' }); // Basic info for now
    goto(`/customers/${customerId}`);
  }

  // Function to set the customer to be deleted and show the modal
  function initiateDelete(customer: Contact) {
    customerToDelete = customer;
    showDeleteModal = true;
  }

  async function confirmDelete() {
    if (!customerToDelete) return;
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/customers/${customerToDelete.id}`, { method: 'DELETE' });
      // if (!response.ok) throw new Error('Failed to delete customer');
      console.log('Simulating delete for:', customerToDelete.fullName);
      addToast({ message: `Customer "${customerToDelete.fullName}" deleted successfully. (Simulated)`, type: 'success' });

      // Optimistically remove from list or reload
      // customers = customers.filter(c => c.id !== customerToDelete!.id);
      // totalCustomerItems--; // Decrement total count
      // OR reload to get fresh data and counts:
      fetchCustomers(currentPage, itemsPerPage, getSearchQueries(), currentSort);

    } catch (e: any) {
      addToast({ message: e.message || 'Error deleting customer.', type: 'error' });
    } finally {
      showDeleteModal = false;
      customerToDelete = null;
    }
  }

  // Customer creation modal state
  interface CustomerFormData {
    name: string;
    email: string;
    phone: string;
  }

  let customerFormData: CustomerFormData = {
    name: '',
    email: '',
    phone: ''
  };

  let customerFormSubmitted = false;
  let customerFormErrors: Record<string, string> = {};
  let customerFormLoading = false;



  function addCustomer() {
    showCreateModal = true;
  }

  function closeCreateModal() {
    showCreateModal = false;
    customerFormData = { name: '', email: '', phone: '' };
    customerFormErrors = {};
    customerFormSubmitted = false;
    customerFormLoading = false;
  }

  function validateCustomerForm(): boolean {
    customerFormErrors = {};

    if (!customerFormData.name.trim()) {
      customerFormErrors.name = 'Customer name is required';
    }

    if (!customerFormData.email.trim()) {
      customerFormErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customerFormData.email)) {
      customerFormErrors.email = 'Please enter a valid email address';
    }

    if (!customerFormData.phone.trim()) {
      customerFormErrors.phone = 'Phone number is required';
    }

    return Object.keys(customerFormErrors).length === 0;
  }

  async function handleCreateCustomer() {
    customerFormSubmitted = true;

    if (!validateCustomerForm()) {
      return;
    }

    customerFormLoading = true;

    try {
      const customerData = {
        ...customerFormData
      };

      const response = await api.post<CustomerGridItem>('/Customers', customerData);

      addToast({
        message: `Customer "${customerFormData.name}" created successfully!`,
        type: 'success'
      });

      // Refresh the customer list, including grid state
      fetchCustomers(currentPage, itemsPerPage, getSearchQueries(), currentSort);

      // Close modal
      closeCreateModal();

      // Redirect to the customer details page using the ID from the response
      goto(`/customers/${response.id}`);
    } catch (err) {
      if (err instanceof ApiError) {
        addToast({
          message: `Failed to create customer: ${err.message}`,
          type: 'error'
        });
        console.error('API Error:', err.status, err.message);
      } else {
        addToast({
          message: 'An unexpected error occurred while creating the customer.',
          type: 'error'
        });
        console.error('Create customer error:', err);
      }
    } finally {
      customerFormLoading = false;
    }
  }

  function editCustomer(customer: Contact) {
    if (customer && customer.id) {
      // TODO: Implement edit customer functionality
      console.log('Edit customer:', customer.id);
      addToast({ message: 'Edit functionality not yet implemented.', type: 'info' });
      // You would typically navigate to an edit page or open an edit modal here
      // goto(`/customers/${customer.id}/edit`);
    }
  }

</script>

<svelte:head>
  <title>Customers</title>
</svelte:head>

<div class="customers-page">
  <PageHeader title="Customers">
    <svelte:fragment slot="actions">
      <Button on:click={addCustomer} variant="primary" type="button">Add Customer</Button>
    </svelte:fragment>
  </PageHeader>

  <main>

  {#if loading}
    <LoadingSpinner message="Loading customers..." />
  {:else if error}
    <p class="error-message">{error} <Button on:click={() => fetchCustomers(currentPage, itemsPerPage, getSearchQueries(), currentSort)} type="button">Retry</Button></p>
  {:else if totalCustomerItems === 0}
    <div class="empty-state">
      <h3>No customers found</h3>
      <p>Get started by adding your first customer.</p>
      <Button on:click={addCustomer} variant="primary">Add Customer</Button>
    </div>
  {:else}
    <div class="customers-grid-container"> <!-- Use a container div for potential grid styling -->
      <Grid
        headers={gridHeaders}
        dataRows={customers}
        emptyMessage="No customers found."
        currentSort={currentSort}
        onHeaderClick={handleHeaderClick}
        searchFields={searchFields}
        itemsPerPageOptions={itemsPerPageOptions}
        itemsPerPage={itemsPerPage}
        currentPage={currentPage}
        totalItems={totalCustomerItems}
        onSearch={handleSearch}
        onItemsPerPageChange={handleItemsPerPageChange}
        onPageChange={handlePageChange}
      >
        <!-- {#snippet cell({ row, headerKey, value })} --> <!-- Using {#snippet} for Svelte 5+ -->
        <!-- {#if headerKey === 'actions'} -->
        <!-- <div class="grid-actions"> -->
        <!-- <Button variant="tertiary" size="sm" on:click={() => viewCustomerDetails(row.id)}>View</Button> -->
        <!-- <Button variant="tertiary" size="sm" on:click={() => editCustomer(row)}>Edit</Button> -->
        <!-- <Button variant="tertiary" size="sm" on:click={() => initiateDelete(row)}>Delete</Button> -->
        <!-- </div> -->
        <!-- {:else} -->
        <!-- {value} -->
        <!-- {/if} -->
        <!-- {/snippet} -->

        <!-- Fallback slot for older Svelte versions if {#snippet} causes issues -->
        <svelte:fragment slot="cell" let:row let:headerKey let:value>
          {#if headerKey === 'actions'}
            <div class="grid-actions">
              <Button variant="tertiary" size="small" on:click={() => viewCustomerDetails(row.id)}>View</Button>
              <Button variant="tertiary" size="small" on:click={() => editCustomer(row as Contact)}>Edit</Button>
              <Button variant="tertiary" size="small" on:click={() => initiateDelete(row as Contact)}>Delete</Button>
            </div>
          {:else}
            {value}
          {/if}
        </svelte:fragment>
      </Grid>
    </div>
  {/if}
  </main>
</div>

<!-- Delete Modal - Always rendered, visibility controlled by Modal component -->
<Modal title="Confirm Delete" show={showDeleteModal} on:close={() => showDeleteModal = false}>
  {#if customerToDelete}
    <ConfirmDelete
      itemName={customerToDelete.fullName}
      on:confirm={confirmDelete}
      on:cancel={() => showDeleteModal = false}
    />
  {/if}
</Modal>

<!-- Create Customer Modal - Always rendered, visibility controlled by Modal component -->
<Modal title="Add New Customer" show={showCreateModal} on:close={closeCreateModal}>
  <form on:submit|preventDefault={handleCreateCustomer} class="customer-form">
    <div class="form-group">
      <label for="customerName">Customer Name *</label>
      <input
        type="text"
        id="customerName"
        bind:value={customerFormData.name}
        class:error={customerFormSubmitted && customerFormErrors.name}
        placeholder="Enter customer name"
        disabled={customerFormLoading}
      />
      {#if customerFormSubmitted && customerFormErrors.name}
        <div class="error-message">{customerFormErrors.name}</div>
      {/if}
    </div>

    <div class="form-group">
      <label for="customerEmail">Email *</label>
      <input
        type="email"
        id="customerEmail"
        bind:value={customerFormData.email}
        class:error={customerFormSubmitted && customerFormErrors.email}
        placeholder="Enter email address"
        disabled={customerFormLoading}
      />
      {#if customerFormSubmitted && customerFormErrors.email}
        <div class="error-message">{customerFormErrors.email}</div>
      {/if}
    </div>

    <div class="form-group">
      <label for="customerPhone">Phone *</label>
      <input
        type="tel"
        id="customerPhone"
        bind:value={customerFormData.phone}
        class:error={customerFormSubmitted && customerFormErrors.phone}
        placeholder="Enter phone number"
        disabled={customerFormLoading}
      />
      {#if customerFormSubmitted && customerFormErrors.phone}
        <div class="error-message">{customerFormErrors.phone}</div>
      {/if}
    </div>
  </form>

  <svelte:fragment slot="footer">
    <Button type="button" variant="secondary" on:click={closeCreateModal} disabled={customerFormLoading}>
      Cancel
    </Button>
    <Button type="button" variant="primary" on:click={handleCreateCustomer} disabled={customerFormLoading}>
      {#if customerFormLoading}
        Creating...
      {:else}
        Create Customer
      {/if}
    </Button>
  </svelte:fragment>
</Modal>

<!-- Add styles for the grid container and actions -->
<style lang="less">
  .customers-grid-container {
    /* Add any necessary styling for the grid container */
    margin-top: 20px; /* Example spacing */
  }

  .grid-actions {
    display: flex;
    gap: 5px;
  }

  /* Add other styles as needed, e.g., for form groups, error messages, etc. */
</style>
