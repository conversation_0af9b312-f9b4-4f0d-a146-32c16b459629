<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import type { Contact } from '$lib/api/contacts';

  export let customer: Contact;

  const dispatch = createEventDispatcher();

  function handleEdit() {
    dispatch('edit', customer);
  }

  function handleDelete() {
    dispatch('delete', customer);
  }

  function handleSelect() {
    dispatch('select', customer);
  }

  function handleViewDetails() {
    window.location.href = `/customers/${customer.id}`;
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'Customer': return '#10B981';
      case 'Lead': return '#3B82F6';
      case 'Archived': return '#6B7280';
      default: return '#6B7280';
    }
  }

  function getPrimaryEmail(): string {
    const primaryEmail = customer.emails?.find(email => email.isPrimary);
    return primaryEmail?.email || customer.emails?.[0]?.email || customer.email || 'No email';
  }

  function getPrimaryPhone(): string {
    const primaryPhone = customer.phones?.find(phone => phone.isPrimary);
    return primaryPhone?.phone || customer.phones?.[0]?.phone || customer.phone || 'No phone';
  }

  function getPrimaryAddress(): string {
    const primaryAddress = customer.addresses?.find(addr => addr.isPrimary);
    if (!primaryAddress) return 'No address';
    return `${primaryAddress.city}, ${primaryAddress.state}`;
  }

  function getDisplayName(): string {
    return customer.fullName || customer.name || 'Unknown Customer';
  }
</script>

<div class="customer-card" on:click={handleSelect} on:keydown role="button" tabindex="0">
  <div class="customer-header">
    <div class="customer-info">
      <h3 class="customer-name">{getDisplayName()}</h3>
      {#if customer.companyName}
        <p class="company-name">{customer.companyName}</p>
      {/if}
    </div>
    {#if customer.status}
      <div class="status-badge" style="background-color: {getStatusColor(customer.status)}">
        {customer.status}
      </div>
    {/if}
  </div>

  <div class="customer-details">
    <div class="detail-item">
      <span class="label">Email:</span>
      <span class="value">{getPrimaryEmail()}</span>
    </div>
    
    <div class="detail-item">
      <span class="label">Phone:</span>
      <span class="value">{getPrimaryPhone()}</span>
    </div>
    
    <div class="detail-item">
      <span class="label">Location:</span>
      <span class="value">{getPrimaryAddress()}</span>
    </div>
  </div>

  {#if customer.notes && customer.notes.length > 0}
    <div class="notes-preview">
      <span class="label">Latest Note:</span>
      <p class="note-text">{customer.notes[customer.notes.length - 1].content}</p>
    </div>
  {/if}

  <div class="customer-actions">
    <Button 
      variant="primary" 
      size="small" 
      on:click={(e) => { e.stopPropagation(); handleViewDetails(); }}
    >
      View Details
    </Button>
    <Button 
      variant="secondary" 
      size="small" 
      on:click={(e) => { e.stopPropagation(); handleEdit(); }}
    >
      Edit
    </Button>
    <Button 
      variant="tertiary" 
      size="small" 
      on:click={(e) => { e.stopPropagation(); handleDelete(); }}
    >
      Delete
    </Button>
  </div>
</div>

<style lang="less">
  .customer-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;
    cursor: pointer;
    transition: box-shadow 0.2s, transform 0.1s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    &:focus {
      outline: 2px solid var(--primary);
      outline-offset: -2px;
    }
  }

  .customer-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    .customer-info {
      flex: 1;

      .customer-name {
        margin: 0 0 0.25rem 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--black);
      }

      .company-name {
        margin: 0;
        font-size: 0.9rem;
        color: var(--grey);
        font-style: italic;
      }
    }

    .status-badge {
      font-size: 0.7rem;
      font-weight: 500;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: var(--br);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .customer-details {
    margin-bottom: 1rem;

    .detail-item {
      display: flex;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;

      .label {
        font-weight: 500;
        color: var(--grey);
        min-width: 70px;
      }

      .value {
        color: var(--black);
        flex: 1;
      }
    }
  }

  .notes-preview {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--bg);
    border-radius: var(--br);
    border-left: 3px solid var(--primary);

    .label {
      font-size: 0.8rem;
      font-weight: 500;
      color: var(--grey);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .note-text {
      margin: 0.5rem 0 0 0;
      font-size: 0.9rem;
      color: var(--black);
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .customer-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid var(--border);
  }
</style>
