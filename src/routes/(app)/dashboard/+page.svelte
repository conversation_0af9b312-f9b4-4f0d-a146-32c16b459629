<script lang="ts">
  import { onMount } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import StatCard from '$lib/components/StatCard.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { goto } from '$app/navigation';
  import { formatCurrency } from '$lib/config/currency';
  
  // Import icons
  import IconNotification from '$lib/components/icons/IconNotification.svelte';
  import IconTimeline from '$lib/components/icons/IconTimeline.svelte';
  import IconJob from '$lib/components/icons/IconJob.svelte';
  import IconCheck from '$lib/components/icons/IconCheck.svelte';
  
  // API imports
  import { getInvoices, getInvoiceStatuses, getStatusDisplay, type ApiInvoice } from '$lib/api/invoices';
  import { getQuotes, getQuoteStatuses } from '$lib/api/quotes';
  import { getJobs, getJobStatuses } from '$lib/api/jobs';
  import { getCalendarEvents } from '$lib/api/calendar';
  import { staff, staffStore } from '$lib/stores/staffStore';
  import { contacts, contactStore } from '$lib/stores/customerStore';
  
  import type { Quote } from '$lib/api/quotes';
  import type { Job } from '$lib/api/jobs';
  import type { CalendarEvent } from '$lib/api/calendar';

  // Loading states
  let loading = true;
  let invoicesLoading = true;
  let quotesLoading = true;
  let jobsLoading = true;
  let calendarLoading = true;

  // Data
  let invoices: ApiInvoice[] = [];
  let quotes: Quote[] = [];
  let jobs: Job[] = [];
  let upcomingEvents: CalendarEvent[] = [];

  // Dashboard stats
  let dashboardStats = {
    totalRevenue: 0,
    monthlyRevenue: 0,
    totalInvoices: 0,
    paidInvoices: 0,
    overdueInvoices: 0,
    totalQuotes: 0,
    acceptedQuotes: 0,
    pendingQuotes: 0,
    totalJobs: 0,
    completedJobs: 0,
    activeJobs: 0,
    totalStaff: 0,
    activeStaff: 0,
    totalContacts: 0,
    customers: 0,
    leads: 0,
    upcomingAppointments: 0
  };

  onMount(async () => {
    await loadAllData();
  });

  async function loadAllData() {
    loading = true;
    try {
      // Load all data in parallel
      await Promise.all([
        loadInvoices(),
        loadQuotes(),
        loadJobs(),
        loadCalendarEvents(),
        loadStaff(),
        loadContacts()
      ]);
      
      calculateStats();
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      addToast({ message: 'Failed to load dashboard data', type: 'error' });
    } finally {
      loading = false;
    }
  }

  async function loadInvoices() {
    try {
      invoicesLoading = true;
      invoices = await getInvoices();
    } catch (error) {
      console.error('Error loading invoices:', error);
    } finally {
      invoicesLoading = false;
    }
  }

  async function loadQuotes() {
    try {
      quotesLoading = true;
      quotes = await getQuotes();
    } catch (error) {
      console.error('Error loading quotes:', error);
    } finally {
      quotesLoading = false;
    }
  }

  async function loadJobs() {
    try {
      jobsLoading = true;
      jobs = await getJobs();
    } catch (error) {
      console.error('Error loading jobs:', error);
    } finally {
      jobsLoading = false;
    }
  }

  async function loadCalendarEvents() {
    try {
      calendarLoading = true;
      const today = new Date();
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
      upcomingEvents = await getCalendarEvents(
        today.toISOString().split('T')[0],
        nextWeek.toISOString().split('T')[0]
      );
    } catch (error) {
      console.error('Error loading calendar events:', error);
    } finally {
      calendarLoading = false;
    }
  }

  async function loadStaff() {
    try {
      await staffStore.loadStaff();
    } catch (error) {
      console.error('Error loading staff:', error);
    }
  }

  async function loadContacts() {
    try {
      await contactStore.loadContacts();
    } catch (error) {
      console.error('Error loading contacts:', error);
    }
  }

  function calculateStats() {
    // Invoice stats
    const paidInvoices = invoices.filter(inv => getStatusDisplay(inv.status).name === 'Paid');
    const overdueInvoices = invoices.filter(inv => {
      const today = new Date();
      const statusDisplay = getStatusDisplay(inv.status);
      return statusDisplay.name !== 'Paid' && 
             statusDisplay.name !== 'Cancelled' && 
             new Date(inv.dueDate) < today;
    });

    dashboardStats.totalRevenue = paidInvoices.reduce((sum, inv) => {
      const invoiceTotal = inv.invoiceLines.reduce((lineSum, line) => lineSum + line.total, 0);
      return sum + invoiceTotal;
    }, 0);
    dashboardStats.totalInvoices = invoices.length;
    dashboardStats.paidInvoices = paidInvoices.length;
    dashboardStats.overdueInvoices = overdueInvoices.length;

    // Monthly revenue (current month)
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    dashboardStats.monthlyRevenue = paidInvoices
      .filter(inv => {
        const invDate = new Date(inv.issueDate);
        return invDate.getMonth() === currentMonth && invDate.getFullYear() === currentYear;
      })
      .reduce((sum, inv) => {
        const invoiceTotal = inv.invoiceLines.reduce((lineSum, line) => lineSum + line.total, 0);
        return sum + invoiceTotal;
      }, 0);

    // Quote stats
    const acceptedQuotes = quotes.filter(quote => quote.status.name === 'Accepted');
    const pendingQuotes = quotes.filter(quote => 
      quote.status.name === 'Sent' || quote.status.name === 'Draft'
    );

    dashboardStats.totalQuotes = quotes.length;
    dashboardStats.acceptedQuotes = acceptedQuotes.length;
    dashboardStats.pendingQuotes = pendingQuotes.length;

    // Job stats
    const completedJobs = jobs.filter(job => job.status.isCompleted);
    const activeJobs = jobs.filter(job => 
      !job.status.isCompleted && job.status.name !== 'Cancelled'
    );

    dashboardStats.totalJobs = jobs.length;
    dashboardStats.completedJobs = completedJobs.length;
    dashboardStats.activeJobs = activeJobs.length;

    // Staff stats
    dashboardStats.totalStaff = $staff.length;
    dashboardStats.activeStaff = $staff.filter(s => s.isActive).length;

    // Contact stats
    dashboardStats.totalContacts = $contacts.length;
    dashboardStats.customers = $contacts.filter(c => c.status === 'Customer').length;
    dashboardStats.leads = $contacts.filter(c => c.status === 'Lead').length;

    // Calendar stats
    dashboardStats.upcomingAppointments = upcomingEvents.length;
  }

  function getRecentInvoices() {
    return invoices
      .sort((a, b) => new Date(b.issueDate).getTime() - new Date(a.issueDate).getTime())
      .slice(0, 5);
  }

  function getRecentQuotes() {
    return quotes
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);
  }

  function getUpcomingJobs() {
    return jobs
      .filter(job => job.scheduledDateTime && new Date(job.scheduledDateTime) > new Date())
      .sort((a, b) => new Date(a.scheduledDateTime!).getTime() - new Date(b.scheduledDateTime!).getTime())
      .slice(0, 5);
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  function formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  function getStatusColor(status: any): string {
    if (typeof status === 'number') {
      return getStatusDisplay(status).color;
    }
    return status.color || '#6B7280';
  }

  function getInvoiceTotal(invoice: ApiInvoice): number {
    return invoice.invoiceLines.reduce((sum, line) => sum + line.total, 0);
  }

  // Quick actions
  function createInvoice() {
    goto('/invoices/new');
  }

  function createQuote() {
    goto('/quotes/create');
  }

  function createJob() {
    goto('/jobs/create');
  }

  function addContact() {
    goto('/contacts');
  }

  function viewCalendar() {
    goto('/calendar');
  }
</script>

<svelte:head>
  <title>Dashboard</title>
</svelte:head>

<div class="container">
  <PageHeader title="Dashboard">
    <svelte:fragment slot="actions">
      <Button variant="primary" on:click={createInvoice}>
        Create Invoice
      </Button>
    </svelte:fragment>
  </PageHeader>

  <main class="dashboard-content">
    {#if loading}
      <div class="loading-container">
        <LoadingSpinner />
        <p>Loading dashboard...</p>
      </div>
    {:else}
      
      <!-- Welcome Section -->
      <div class="welcome-section">
        <div class="welcome-card">
          <h2>Welcome back! 👋</h2>
          <p>Here's what's happening with your business today.</p>
          <div class="quick-actions">
            <Button variant="primary" size="small" on:click={createInvoice}>New Invoice</Button>
            <Button variant="primary" size="small" on:click={createQuote}>New Quote</Button>
            <Button variant="primary" size="small" on:click={createJob}>New Job</Button>
            <Button variant="primary" size="small" on:click={viewCalendar}>View Calendar</Button>
          </div>
        </div>
      </div>

      <!-- Financial Overview -->
      <div class="section">
        <h3 class="section-title">💰 Financial Overview</h3>
        <div class="stats-grid">
          <StatCard title="Total Revenue" value={formatCurrency(dashboardStats.totalRevenue)} />
          <StatCard title="Monthly Revenue" value={formatCurrency(dashboardStats.monthlyRevenue)} />
          <StatCard title="Total Invoices" value={dashboardStats.totalInvoices} />
          <StatCard title="Paid Invoices" value={dashboardStats.paidInvoices} />
          <StatCard title="Overdue Invoices" value={dashboardStats.overdueInvoices} valueClass="overdue" />
        </div>
      </div>

      <!-- Sales & Quotes -->
      <div class="section">
        <h3 class="section-title">📊 Sales & Quotes</h3>
        <div class="stats-grid">
          <StatCard title="Total Quotes" value={dashboardStats.totalQuotes} />
          <StatCard title="Accepted Quotes" value={dashboardStats.acceptedQuotes} />
          <StatCard title="Pending Quotes" value={dashboardStats.pendingQuotes} />
          <StatCard title="Quote Value" value={formatCurrency(quotes.filter(q => q.status.name === 'Accepted').reduce((sum, q) => sum + q.totalAmount, 0))} />
        </div>
      </div>

      <!-- Operations -->
      <div class="section">
        <h3 class="section-title">🔧 Operations</h3>
        <div class="stats-grid">
          <StatCard title="Total Jobs" value={dashboardStats.totalJobs} />
          <StatCard title="Active Jobs" value={dashboardStats.activeJobs} />
          <StatCard title="Completed Jobs" value={dashboardStats.completedJobs} />
          <StatCard title="Upcoming Appointments" value={dashboardStats.upcomingAppointments} />
        </div>
      </div>

      <!-- Team & Contacts -->
      <div class="section">
        <h3 class="section-title">👥 Team & Contacts</h3>
        <div class="stats-grid">
          <StatCard title="Total Staff" value={dashboardStats.totalStaff} />
          <StatCard title="Active Staff" value={dashboardStats.activeStaff} />
          <StatCard title="Total Contacts" value={dashboardStats.totalContacts} />
          <StatCard title="Customers" value={dashboardStats.customers} />
          <StatCard title="Leads" value={dashboardStats.leads} />
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="activity-section">
        <div class="activity-grid">
          
          <!-- Recent Invoices -->
          <div class="activity-card">
            <div class="activity-header">
              <h4>📄 Recent Invoices</h4>
              <Button variant="tertiary" size="small" on:click={() => goto('/invoices')}>View All</Button>
            </div>
            <div class="activity-list">
              {#each getRecentInvoices() as invoice}
                <div class="activity-item" on:click={() => goto(`/invoices/${invoice.id}`)} role="button" tabindex="0">
                  <div class="activity-info">
                    <span class="activity-title">{invoice.invoiceNumber || 'Draft'}</span>
                    <span class="activity-subtitle">Customer</span>
                  </div>
                  <div class="activity-meta">
                    <span class="activity-amount">{formatCurrency(getInvoiceTotal(invoice))}</span>
                    <span class="activity-status" style="color: {getStatusColor(invoice.status)}">{getStatusDisplay(invoice.status).name}</span>
                  </div>
                </div>
              {:else}
                <div class="empty-activity">No recent invoices</div>
              {/each}
            </div>
          </div>

          <!-- Recent Quotes -->
          <div class="activity-card">
            <div class="activity-header">
              <h4>💼 Recent Quotes</h4>
              <Button variant="tertiary" size="small" on:click={() => goto('/quotes')}>View All</Button>
            </div>
            <div class="activity-list">
              {#each getRecentQuotes() as quote}
                <div class="activity-item" on:click={() => goto(`/quotes/${quote.id}`)} role="button" tabindex="0">
                  <div class="activity-info">
                    <span class="activity-title">{quote.quoteNumber}</span>
                    <span class="activity-subtitle">{quote.customerName}</span>
                  </div>
                  <div class="activity-meta">
                    <span class="activity-amount">{formatCurrency(quote.totalAmount)}</span>
                    <span class="activity-status" style="color: {getStatusColor(quote.status)}">{quote.status.name}</span>
                  </div>
                </div>
              {:else}
                <div class="empty-activity">No recent quotes</div>
              {/each}
            </div>
          </div>

          <!-- Upcoming Jobs -->
          <div class="activity-card">
            <div class="activity-header">
              <h4>🔧 Upcoming Jobs</h4>
              <Button variant="tertiary" size="small" on:click={() => goto('/jobs')}>View All</Button>
            </div>
            <div class="activity-list">
              {#each getUpcomingJobs() as job}
                <div class="activity-item" on:click={() => goto(`/jobs/${job.id}`)} role="button" tabindex="0">
                  <div class="activity-info">
                    <span class="activity-title">{job.title}</span>
                    <span class="activity-subtitle">{job.customerName}</span>
                  </div>
                  <div class="activity-meta">
                    <span class="activity-date">{formatDateTime(job.scheduledDateTime!)}</span>
                    <span class="activity-status" style="color: {getStatusColor(job.status)}">{job.status.name}</span>
                  </div>
                </div>
              {:else}
                <div class="empty-activity">No upcoming jobs</div>
              {/each}
            </div>
          </div>

          <!-- Upcoming Events -->
          <div class="activity-card">
            <div class="activity-header">
              <h4>📅 This Week's Schedule</h4>
              <Button variant="tertiary" size="small" on:click={viewCalendar}>View Calendar</Button>
            </div>
            <div class="activity-list">
              {#each upcomingEvents.slice(0, 5) as event}
                <div class="activity-item">
                  <div class="activity-info">
                    <span class="activity-title">{event.title}</span>
                    <span class="activity-subtitle">{event.customerName}</span>
                  </div>
                  <div class="activity-meta">
                    <span class="activity-date">{formatDateTime(event.startDateTime)}</span>
                    <span class="activity-status" style="color: {event.color || '#6B7280'}">{event.status}</span>
                  </div>
                </div>
              {:else}
                <div class="empty-activity">No upcoming events</div>
              {/each}
            </div>
          </div>

        </div>
      </div>

      <!-- Performance Insights -->
      <div class="insights-section">
        <h3 class="section-title">📈 Performance Insights</h3>
        <div class="insights-grid">
          
          <div class="insight-card">
            <h4>💡 Business Health</h4>
            <div class="insight-content">
              <div class="insight-metric">
                <span class="metric-label">Invoice Collection Rate</span>
                <span class="metric-value">
                  {dashboardStats.totalInvoices > 0 ? Math.round((dashboardStats.paidInvoices / dashboardStats.totalInvoices) * 100) : 0}%
                </span>
              </div>
              <div class="insight-metric">
                <span class="metric-label">Quote Conversion Rate</span>
                <span class="metric-value">
                  {dashboardStats.totalQuotes > 0 ? Math.round((dashboardStats.acceptedQuotes / dashboardStats.totalQuotes) * 100) : 0}%
                </span>
              </div>
              <div class="insight-metric">
                <span class="metric-label">Job Completion Rate</span>
                <span class="metric-value">
                  {dashboardStats.totalJobs > 0 ? Math.round((dashboardStats.completedJobs / dashboardStats.totalJobs) * 100) : 0}%
                </span>
              </div>
            </div>
          </div>

          <div class="insight-card">
            <h4>⚠️ Attention Needed</h4>
            <div class="insight-content">
              {#if dashboardStats.overdueInvoices > 0}
                <div class="alert-item overdue">
                  <svelte:component this={IconNotification} size="24" className="alert-icon" />
                  <span>{dashboardStats.overdueInvoices} overdue invoice{dashboardStats.overdueInvoices > 1 ? 's' : ''}</span>
                </div>
              {/if}
              {#if dashboardStats.pendingQuotes > 0}
                <div class="alert-item pending">
                  <svelte:component this={IconTimeline} size="24" className="alert-icon" />
                  <span>{dashboardStats.pendingQuotes} pending quote{dashboardStats.pendingQuotes > 1 ? 's' : ''}</span>
                </div>
              {/if}
              {#if dashboardStats.activeJobs > 5}
                <div class="alert-item busy">
                  <svelte:component this={IconJob} size="24" className="alert-icon" />
                  <span>High job volume: {dashboardStats.activeJobs} active jobs</span>
                </div>
              {/if}
              {#if dashboardStats.overdueInvoices === 0 && dashboardStats.pendingQuotes === 0 && dashboardStats.activeJobs <= 5}
                <div class="alert-item success">
                  <svelte:component this={IconCheck} size="24" className="alert-icon" />
                  <span>Everything looks good!</span>
                </div>
              {/if}
            </div>
          </div>

          <div class="insight-card">
            <h4>🎯 Quick Stats</h4>
            <div class="insight-content">
              <div class="quick-stat">
                <span class="stat-icon">💰</span>
                <div>
                  <div class="stat-value">{formatCurrency(dashboardStats.totalRevenue / Math.max(dashboardStats.paidInvoices, 1))}</div>
                  <div class="stat-label">Avg Invoice Value</div>
                </div>
              </div>
              <div class="quick-stat">
                <span class="stat-icon">📊</span>
                <div>
                  <div class="stat-value">{formatCurrency(quotes.filter(q => q.status.name === 'Accepted').reduce((sum, q) => sum + q.totalAmount, 0) / Math.max(dashboardStats.acceptedQuotes, 1))}</div>
                  <div class="stat-label">Avg Quote Value</div>
                </div>
              </div>
              <div class="quick-stat">
                <span class="stat-icon">👥</span>
                <div>
                  <div class="stat-value">{Math.round(dashboardStats.totalJobs / Math.max(dashboardStats.activeStaff, 1))}</div>
                  <div class="stat-label">Jobs per Staff</div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>

    {/if}
  </main>
</div>

<style lang="less">
  .dashboard-content {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin: 0 auto;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .welcome-section {
    .welcome-card {
      background: linear-gradient(135deg, var(--primary) 0%, #1e40af 100%);
      color: white;
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 8px 32px rgba(37, 99, 235, 0.3);

      h2 {
        margin: 0 0 0.5rem 0;
        font-size: 1.75rem;
        font-weight: 700;
      }

      p {
        margin: 0 0 1.5rem 0;
        opacity: 0.9;
        font-size: 1.1rem;
      }

      .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
      }
    }
  }

  .section {
    .section-title {
      margin: 0 0 1.5rem 0;
      color: var(--black);
      font-size: 1.25rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
    }
  }

  .activity-section {
    .activity-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }

    .activity-card {
      background: white;
      border: 1px solid var(--border);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .activity-header {
        padding: 1.5rem 1.5rem 1rem 1.5rem;
        border-bottom: 1px solid var(--border);
        display: flex;
        justify-content: space-between;
        align-items: center;

        h4 {
          margin: 0;
          color: var(--black);
          font-size: 1rem;
          font-weight: 600;
        }
      }

      .activity-list {
        padding: 0;
      }

      .activity-item {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f3f4f6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        .activity-info {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;

          .activity-title {
            font-weight: 500;
            color: var(--black);
          }

          .activity-subtitle {
            font-size: 0.85rem;
            color: var(--grey);
          }
        }

        .activity-meta {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 0.25rem;

          .activity-amount {
            font-weight: 600;
            color: var(--primary);
          }

          .activity-date {
            font-size: 0.8rem;
            color: var(--grey);
          }

          .activity-status {
            font-size: 0.8rem;
            font-weight: 500;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            background: rgba(107, 114, 128, 0.1);
          }
        }
      }

      .empty-activity {
        padding: 2rem 1.5rem;
        text-align: center;
        color: var(--grey);
        font-style: italic;
      }
    }
  }

  .insights-section {
    .insights-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }

    .insight-card {
      background: white;
      border: 1px solid var(--border);
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      h4 {
        margin: 0 0 1rem 0;
        color: var(--black);
        font-size: 1rem;
        font-weight: 600;
      }

      .insight-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .insight-metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;

        .metric-label {
          color: var(--grey);
          font-size: 0.9rem;
        }

        .metric-value {
          font-weight: 600;
          color: var(--primary);
          font-size: 1.1rem;
        }
      }

      .alert-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        border-radius: 8px;
        font-size: 0.9rem;

        .alert-icon {
          font-size: 1.2rem;
        }

        &.overdue {
          background: rgba(239, 68, 68, 0.1);
          color: #dc2626;
        }

        &.pending {
          background: rgba(245, 158, 11, 0.1);
          color: #d97706;
        }

        &.busy {
          background: rgba(249, 115, 22, 0.1);
          color: #ea580c;
        }

        &.success {
          background: rgba(16, 185, 129, 0.1);
          color: #059669;
        }
      }

      .quick-stat {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;

        .stat-icon {
          font-size: 1.5rem;
        }

        .stat-value {
          font-weight: 600;
          color: var(--black);
          font-size: 1.1rem;
        }

        .stat-label {
          color: var(--grey);
          font-size: 0.8rem;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .dashboard-content {
      padding: 1rem;
    }

    .welcome-section .welcome-card {
      padding: 1.5rem;

      h2 {
        font-size: 1.5rem;
      }

      .quick-actions {
        flex-direction: column;
      }
    }

    .section .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
    }

    .activity-section .activity-grid {
      grid-template-columns: 1fr;
    }

    .insights-section .insights-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
