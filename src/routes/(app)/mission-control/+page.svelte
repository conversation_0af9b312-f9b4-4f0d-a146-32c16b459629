<script lang="ts">
	import { onMount } from 'svelte';
	import PageHeader from '$lib/components/PageHeader.svelte';
	import Grid from '$lib/components/Grid.svelte';
	import { user } from '$lib/stores/auth'; // To get the token for authenticated requests

	type Tenant = {
		id: string;
		name: string;
		status: string; // e.g., Active, Inactive, Suspended
		// Add other relevant tenant properties here
	};

	let tenants: Tenant[] = [];
	let isLoading = true;
	let error: string | null = null;
	let currentSort: { key: string; direction: 'ascending' | 'descending' } = { key: 'name', direction: 'ascending' }; // Fixed: matches SortState interface

	const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://app-ejp-api-test-gpfsbbe0gdgahkhp.uksouth-01.azurewebsites.net';

	async function fetchTenants() {
		isLoading = true;
		error = null;
		const token = $user?.token; // Get token from auth store

		if (!token) {
			error = 'Authentication token not found. Please log in.';
			isLoading = false;
			return;
		}

		if (!API_BASE_URL) {
			error = 'API base URL not configured. Please check environment variables.';
			isLoading = false;
			console.error('VITE_API_BASE_URL is not defined');
			return;
		}

		try {
			console.log('Fetching tenants from:', `${API_BASE_URL}/Tenants`);
			const response = await fetch(`${API_BASE_URL}/Tenants`, {
				headers: {
					'Authorization': `Bearer ${token}`,
					'Content-Type': 'application/json'
				}
			});
			
			console.log('Response status:', response.status);
			console.log('Response headers:', Object.fromEntries(response.headers.entries()));
			
			if (!response.ok) {
				const errorData = await response.json().catch(() => ({ message: 'Failed to fetch tenants' }));
				throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
			}
			tenants = await response.json();
		} catch (e: any) {
			error = e.message || 'An unexpected error occurred.';
			console.error('Error fetching tenants:', e);
		} finally {
			isLoading = false;
		}
	}

	onMount(() => {
		fetchTenants();
	});

	const headers = [
		{ text: 'Tenant Name', key: 'name', sortable: true },
		{ text: 'Status', key: 'status', sortable: true },
		{ text: 'ID', key: 'id', sortable: false } // ID might not need to be sortable by default
	];

	function handleHeaderClick(key: string) {
		if (currentSort.key === key) {
			currentSort.direction = currentSort.direction === 'ascending' ? 'descending' : 'ascending';
		} else {
			currentSort.key = key;
			currentSort.direction = 'ascending';
		}
		// Re-sort data (Svelte reactivity will update the grid)
		tenants = [...tenants].sort((a, b) => {
			const valA = a[key as keyof Tenant];
			const valB = b[key as keyof Tenant];
			if (valA < valB) return currentSort.direction === 'ascending' ? -1 : 1;
			if (valA > valB) return currentSort.direction === 'ascending' ? 1 : -1;
			return 0;
		});
	}

</script>

<svelte:head>
	<title>Mission Control - Tenants</title>
</svelte:head>

<div class="container">
	<PageHeader title="Mission Control - Tenant Overview" />
	<main>
		{#if isLoading}
			<p>Loading tenants...</p>
		{:else if error}
			<div class="error-message">
				<p>Error loading tenants: {error}</p>
				<button on:click={fetchTenants}>Retry</button>
			</div>
		{:else}
			<Grid
				headers={headers}
				dataRows={tenants}
				emptyMessage="No tenants found."
				onHeaderClick={handleHeaderClick}
				currentSort={currentSort}
			/>
		{/if}
	</main>
</div>

<style>
	.container {
		padding: 20px;
	}
	main {
		margin-top: 20px;
	}
	.error-message {
		color: var(--destructive-foreground, red);
		background-color: var(--destructive, #ffebee);
		border: 1px solid var(--destructive-foreground, red);
		padding: 15px;
		border-radius: 4px;
		margin-bottom: 15px;
	}
	.error-message button {
		margin-top: 10px;
		padding: 8px 12px;
		background-color: var(--primary);
		color: var(--primary-foreground);
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}
	.error-message button:hover {
		background-color: hsl(var(--primary-hs) var(--primary-l) / 0.9);
	}
</style>
