<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { goto, beforeNavigate } from '$app/navigation';
  import { page } from '$app/stores';
  import { browser } from '$app/environment';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import Button from '$lib/components/Button.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { contactStore, customers } from '$lib/stores/customerStore';
  import CustomerSelect from '$lib/components/CustomerSelect.svelte';
  import LineItems from '$lib/components/page/LineItems.svelte';
  import {
    getInvoiceById,
    saveInvoice,
    getProducts,
    getInvoiceTemplates,
    getInvoiceStatuses,
    calculateApiInvoiceTotals,
    calculateLineItemTotals,
    createEmptyApiLineItem,
    getStatusDisplay,
    getStatusNumber,
    type ApiInvoice,
    type ApiInvoiceLine,
    type Product,
    type InvoiceTemplate,
    type InvoiceStatus
  } from '$lib/api/invoices';
  import { getUninvoicedJobs, type Job } from '$lib/api/jobs';
  import { getUninvoicedQuotes, type Quote } from '$lib/api/quotes';
  import type { Contact } from '$lib/api/contacts';
  import { generateInvoicePDF, type InvoiceFormData as PDFInvoiceFormData } from '$lib/utils/pdfGenerator';

  // Get invoice ID from URL
  $: invoiceId = $page.params.invoiceId;
  $: isNewInvoice = invoiceId === 'new';

  // State variables
  let isLoading = true;
  let isSaving = false;
  let invoice: ApiInvoice | null = null;
  let products: Product[] = [];
  let invoiceTemplates: InvoiceTemplate[] = [];
  let invoiceStatuses: InvoiceStatus[] = [];
  let uninvoicedJobs: Job[] = [];
  let uninvoicedQuotes: Quote[] = [];
  let isLoadingData = true;
  let isLoadingUninvoiced = false;
  let customerSearch = '';
  let showCustomerDropdown = false;
  let highlightedIndex = -1;
  let formChanged = false;
  let initialFormState: string;
  let showUninvoicedSection = false;
  
  // UI state for line item toggles
  let showProductSelect: Record<number, boolean> = {};
  let showAdditionalInfo: Record<number, boolean> = {};

  // Multi-job selection state
  let selectedJobIds: Set<string> = new Set();
  let selectedQuoteIds: Set<string> = new Set();

  // Custom header fields - separate from invoice lines
  let customFields: Record<string, string>[] = [];

  // Form data - working directly with API format
  let formData: ApiInvoice = {
    status: 0, // Draft
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    notes: '',
    paymentTerms: 'Payment due within 30 days.',
    invoiceLines: [createEmptyApiLineItem()]
  };

  // Additional UI fields
  let customerId = '';
  let templateId = '';
  let discountAmount = 0;

  // Form validation
  let errors: Record<string, string> = {};
  let formSubmitted = false;

  let taxMode: 0 | 1 = 0; // Default to Tax Exclusive

  // Helper functions
  function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  function updateFormChanged() {
    if (!initialFormState) return;
    const currentFormState = JSON.stringify({ formData, customFields, discountAmount });
    formChanged = currentFormState !== initialFormState;
  }

  function calculateTotals() {
    // Calculate line totals and update each line
    const updatedLines = formData.invoiceLines.map(line => calculateLineItemTotals(line));
    
    formData = {
      ...formData,
      invoiceLines: updatedLines
    };
  }

  function addLineItem() {
    const newLineNumber = formData.invoiceLines.length + 1;
    formData = {
      ...formData,
      invoiceLines: [...formData.invoiceLines, createEmptyApiLineItem(newLineNumber)]
    };
    calculateTotals();
  }

  function removeLineItem(index: number) {
    formData = {
      ...formData,
      invoiceLines: formData.invoiceLines.filter((_, i) => i !== index)
    };
    
    // Clean up UI state for removed item
    const newShowProductSelect: Record<number, boolean> = {};
    Object.keys(showProductSelect).forEach(key => {
      const idx = parseInt(key);
      if (idx < index) {
        newShowProductSelect[idx] = showProductSelect[idx];
      } else if (idx > index) {
        newShowProductSelect[idx - 1] = showProductSelect[idx];
      }
    });
    showProductSelect = newShowProductSelect;
    
    calculateTotals();
  }

  function updateLineItem(index: number, lineItem: ApiInvoiceLine) {
    const updatedLines = [...formData.invoiceLines];
    updatedLines[index] = lineItem;
    formData = {
      ...formData,
      invoiceLines: updatedLines
    };
    updateFormChanged();
  }

  function addCustomHeaderField() {
    customFields = [...customFields, { label: '', value: '' }];
    updateFormChanged();
  }

  function removeCustomHeaderField(index: number) {
    customFields = customFields.filter((_, i) => i !== index);
    updateFormChanged();
  }

  function selectProduct(lineItemIndex: number, product: Product) {
    const updatedLines = [...formData.invoiceLines];
    updatedLines[lineItemIndex] = {
      ...updatedLines[lineItemIndex],
      description: product.description,
      unitPrice: product.price,
      taxRate: product.taxRate / 100 // Convert percentage to decimal
    };

    formData = {
      ...formData,
      invoiceLines: updatedLines
    };

    calculateTotals();
  }

  function validateForm(): boolean {
    errors = {};

    if (!customerId) {
      errors.customerId = 'Please select a customer';
    }

    if (!formData.issueDate) {
      errors.issueDate = 'Issue date is required';
    }

    if (!formData.dueDate) {
      errors.dueDate = 'Due date is required';
    }

    // Validate each line item
    formData.invoiceLines.forEach((item, index) => {
      if (!item.description) {
        errors[`invoiceLines[${index}].description`] = 'Description is required';
      }

      if (item.quantity <= 0) {
        errors[`invoiceLines[${index}].quantity`] = 'Quantity must be greater than 0';
      }

      if (item.unitPrice < 0) {
        errors[`invoiceLines[${index}].unitPrice`] = 'Unit price cannot be negative';
      }
    });

    // Validate custom fields
    customFields.forEach((field, index) => {
      if (field.label && !field.value) {
        errors[`customFields[${index}].value`] = 'Field value is required when label is provided';
      }
    });

    return Object.keys(errors).length === 0;
  }

  async function loadData() {
    isLoadingData = true;
    try {
      // Load contacts/customers
      await contactStore.loadContacts();

      // Load other data in parallel
      const [productsData, templatesData, statusesData] = await Promise.all([
        getProducts(),
        getInvoiceTemplates(),
        getInvoiceStatuses()
      ]);

      products = productsData;
      invoiceTemplates = templatesData;
      invoiceStatuses = statusesData;

      // If editing existing invoice, load it
      if (!isNewInvoice) {
        invoice = await getInvoiceById(invoiceId);
        if (invoice) {
          // Set form data from loaded invoice
          formData = { ...invoice };
          
          // Find and set customer if available
          // Note: We'll need to store customer info in the invoice or look it up separately
          // For now, we'll leave customer selection empty for existing invoices
        } else {
          addToast({ message: 'Invoice not found', type: 'error' });
          goto('/invoices');
          return;
        }
      }

      // Set default template
      if (invoiceTemplates.length > 0 && !templateId) {
        const defaultTemplate = invoiceTemplates.find(t => t.isDefault);
        if (defaultTemplate) {
          templateId = defaultTemplate.id;
        }
      }

    } catch (error) {
      console.error('Error loading data:', error);
      addToast({ message: 'Failed to load data', type: 'error' });
    } finally {
      isLoadingData = false;
      isLoading = false;
    }
  }

  function selectCustomer(customer: Contact) {
    customerId = customer.id;
    customerSearch = customer.companyName || customer.fullName;
  }

  async function loadUninvoicedItems(customerId: string) {
    if (!customerId) {
      uninvoicedJobs = [];
      uninvoicedQuotes = [];
      return;
    }

    isLoadingUninvoiced = true;
    try {
      const [jobs, quotes] = await Promise.all([
        getUninvoicedJobs(customerId),
        getUninvoicedQuotes(customerId)
      ]);

      uninvoicedJobs = jobs;
      uninvoicedQuotes = quotes;

      // Show the section if there are uninvoiced items
      showUninvoicedSection = jobs.length > 0 || quotes.length > 0;
    } catch (error) {
      console.error('Error loading uninvoiced items:', error);
      addToast({ message: 'Failed to load uninvoiced items', type: 'error' });
    } finally {
      isLoadingUninvoiced = false;
    }
  }

  async function handleSubmit() {
    formSubmitted = true;
    calculateTotals();

    if (!validateForm()) {
      addToast({
        message: 'Please fix the errors in the form before submitting',
        type: 'error'
      });
      return;
    }

    isSaving = true;

    try {
      // Include discount amount and custom fields in the form data before saving
      const dataToSave = {
        ...formData,
        discountAmount,
        customFields // Include custom fields if the API supports them
      };

      const savedInvoice = await saveInvoice(dataToSave);

      addToast({
        message: isNewInvoice ? 'Invoice created successfully' : 'Invoice updated successfully',
        type: 'success'
      });

      // Navigate to the invoice detail page
      goto(`/invoices/${savedInvoice.id}`);
    } catch (err) {
      console.error('Error saving invoice:', err);
      addToast({
        message: err instanceof Error ? err.message : 'An unknown error occurred',
        type: 'error'
      });
    } finally {
      isSaving = false;
    }
  }

  function cancelForm() {
    updateFormChanged();
    if (formChanged) {
      if (window.confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        goto('/invoices');
      }
    } else {
      goto('/invoices');
    }
  }

  // Get calculated totals for display
  $: totals = calculateApiInvoiceTotals(formData.invoiceLines);
  $: totalWithDiscount = totals.totalAmount - discountAmount;

  // Filter customers for search
  $: filteredCustomers = customerSearch 
    ? $customers.filter(customer =>
        customer.fullName.toLowerCase().includes(customerSearch.toLowerCase()) ||
        (customer.companyName && customer.companyName.toLowerCase().includes(customerSearch.toLowerCase())) ||
        customer.emails.some(email => email.email.toLowerCase().includes(customerSearch.toLowerCase()))
      )
    : $customers;

  // React to form data changes
  $: if (initialFormState) {
    updateFormChanged();
  }

  // Handle navigation confirmation
  beforeNavigate(({ cancel, to }) => {
    // Skip if we're saving or if the form hasn't changed
    if (isSaving) return;

    updateFormChanged();

    if (formChanged && to?.url.pathname !== window.location.pathname) {
      if (!window.confirm('You have unsaved changes. Are you sure you want to leave?')) {
        cancel();
      }
    }
  });

  function handleTaxModeChange(event: CustomEvent<{ taxMode: 0 | 1 }>) {
    taxMode = event.detail.taxMode;
    // Recalculate all totals when tax mode changes
    calculateTotals();
    updateFormChanged();
  }

  onMount(async () => {
    await loadData();

    // Set initial form state after data is loaded
    setTimeout(() => {
      initialFormState = JSON.stringify({ formData, customFields, discountAmount });
    }, 100);
  });
</script>

<svelte:head>
  <title>{isNewInvoice ? 'Create Invoice' : 'Edit Invoice'}</title>
</svelte:head>

<div class="container">
  <PageHeader title={isNewInvoice ? 'Create Invoice' : 'Edit Invoice'}>
    <svelte:fragment slot="actions">
      <div class="save-status-container">
        {#if formChanged}
          <div class="save-status unsaved">
            <div class="status-dot"></div>
            Unsaved changes
          </div>
        {:else}
          <div class="save-status saved">
            <div class="status-dot"></div>
            All changes saved
          </div>
        {/if}
      </div>
    </svelte:fragment>
  </PageHeader>

  {#if isLoadingData || isLoading}
    <div class="loading-container">
      <LoadingSpinner />
      <p>Loading invoice data...</p>
    </div>
  {:else}
    <form on:submit|preventDefault={handleSubmit} class="invoice-form">
      <div class="invoice-editor">
        <main class="editor-content">
          <!-- Customer Information -->
          <div class="form-section">
            <h2>Customer Information</h2>
            <CustomerSelect
              bind:customerId={customerId}
              bind:customerSearch={customerSearch}
              hasError={formSubmitted && !!errors.customerId}
              errorMessage={formSubmitted && errors.customerId ? errors.customerId : ''}
              on:selectcustomer={(event) => loadUninvoicedItems(event.detail)}
            />
          </div>

          <!-- Invoice Header -->
          <div class="form-section">
            <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
              <div class="layout-column">
                <div class="form-group">
                  <label for="sellerDetails">Seller Details</label>
                  <textarea id="sellerDetails">123 Fake Street, 
London, 
UK, 
W12 3AB</textarea>
                </div>
              </div>

              <div class="layout-column">
                <div class="form-group horizontal">
                  <label for="invoiceNumber">Invoice Number</label>
                  <input
                    type="text"
                    id="invoiceNumber"
                    bind:value={formData.invoiceNumber}
                    placeholder="Auto-generated"
                    readonly
                  />
                </div>
                
                <div class="form-group horizontal">
                  <label for="issueDate">Issue Date</label>
                  <input
                    type="date"
                    id="issueDate"
                    bind:value={formData.issueDate}
                    class:error={formSubmitted && errors.issueDate}
                  />
                  {#if formSubmitted && errors.issueDate}
                    <div class="error-message">{errors.issueDate}</div>
                  {/if}
                </div>

                <div class="form-group horizontal">
                  <label for="dueDate">Due Date</label>
                  <input
                    type="date"
                    id="dueDate"
                    bind:value={formData.dueDate}
                    class:error={formSubmitted && errors.dueDate}
                  />
                  {#if formSubmitted && errors.dueDate}
                    <div class="error-message">{errors.dueDate}</div>
                  {/if}
                </div>

                <!-- Custom Header Fields -->
                {#if customFields.length > 0}
                  {#each customFields as field, index}
                    <div class="form-group horizontal">
                      <input
                        type="text"
                        id="fieldLabel{index}"
                        bind:value={field.label}
                        placeholder="Field label"
                        class:error={formSubmitted && errors[`customFields[${index}].label`]}
                      />
                      
                      <input
                        type="text"
                        id="fieldValue{index}"
                        bind:value={field.value}
                        placeholder="Field value"
                      />
                      
                      {#if formSubmitted && errors[`customFields[${index}].label`]}
                        <div class="error-message">{errors[`customFields[${index}].label`]}</div>
                      {/if}
                      
                      <Button
                        variant="tertiary"
                        size="small"
                        on:click={() => removeCustomHeaderField(index)}
                      >
                        Remove
                      </Button>
                    </div>
                  {/each}
                {/if}

                <div>
                  <Button variant="secondary" size="small" on:click={addCustomHeaderField}>
                    Add Custom Field
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <!-- Invoice Items -->
          <div class="form-section">
            <div class="layout-row">
              <div class="layout-column">
                <LineItems
                  bind:lineItems={formData.invoiceLines}
                  mode="edit"
                  currency="£"
                  {formSubmitted}
                  {errors}
                  taxMode={taxMode}
                  on:updateLineItem={(event) => updateLineItem(event.detail.index, event.detail.lineItem)}
                  on:addLineItem={addLineItem}
                  on:removeLineItem={(event) => removeLineItem(event.detail.index)}
                  on:calculateTotals={calculateTotals}
                  on:taxModeChange={handleTaxModeChange}
                />

                <div class="invoice-totals">
                  <div class="totals-row">
                    <div class="totals-label">Subtotal:</div>
                    <div class="totals-value">£{totals.subtotal.toFixed(2)}</div>
                  </div>

                  <div class="totals-row">
                    <div class="totals-label">
                      <label for="discountAmount">Discount:</label>
                    </div>
                    <div class="totals-value">
                      <input
                        type="number"
                        id="discountAmount"
                        bind:value={discountAmount}
                        min="0"
                        step="0.01"
                        class="discount-input"
                      />
                    </div>
                  </div>

                  <div class="totals-row">
                    <div class="totals-label">Tax:</div>
                    <div class="totals-value">£{totals.taxAmount.toFixed(2)}</div>
                  </div>

                  <div class="totals-row total">
                    <div class="totals-label">Total:</div>
                    <div class="totals-value">£{totalWithDiscount.toFixed(2)}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="form-section">
            <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
              <div class="layout-column">
                <div class="form-group">
                  <label for="notes">Notes</label>
                  <textarea id="notes" bind:value={formData.notes} rows="3" placeholder="Notes to the customer"></textarea>
                </div>
              </div>
              <div class="layout-column">
                <div class="form-group">
                  <label for="terms">Terms and Conditions</label>
                  <textarea id="terms" bind:value={formData.paymentTerms} rows="3"></textarea>
                </div>
              </div>          
            </div>
          </div>
        </main>

        <div class="properties-panel">
          <div class="property-section">
            <div class="form-actions">
              <Button variant="tertiary" size="small" on:click={cancelForm} disabled={isSaving}>Cancel</Button>
              <Button type="submit" disabled={isSaving}>
                {#if isSaving}
                  Saving...
                {:else}
                  {isNewInvoice ? 'Save' : 'Update'}
                {/if}
              </Button>
            </div>

            <div class="form-group">
              <label for="status">Status</label>
              <select id="status" bind:value={formData.status}>
                {#each invoiceStatuses as status}
                  <option value={getStatusNumber(status.name)}>{status.name}</option>
                {/each}
              </select>
            </div>

            <div class="form-group">
              <label for="template">Template</label>
              <select id="template" bind:value={templateId}>
                <option value="">Default Template</option>
                {#each invoiceTemplates as template}
                  <option value={template.id}>{template.name}</option>
                {/each}
              </select>
            </div>
            
          </div>
        </div>
      </div>
    </form>
  {/if}
</div>

<style lang="less">
  .invoice-editor {
    display: grid;
    grid-template-columns: 1fr 300px;
    flex: 1;
    overflow: hidden;

    .editor-content {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .properties-panel {
      background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
      border-left: 1px solid var(--border);
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      box-shadow: inset -2px 0 8px rgba(0, 0, 0, 0.05);
      
      .property-section {
        margin-bottom: 2rem;
        border-radius: 12px;
        padding: 1.5rem;
      }
    }

    .layout-row {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      
      .layout-column {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        
        .form-group.horizontal {
          flex-direction: row;
          margin-bottom: 0;
          
          &>*:first-child {
            width: 130px;
            flex-shrink: 0;
            margin-bottom: 0;
            box-sizing: border-box;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }

  .save-status-container {
    display: flex;
    align-items: center;
    margin-right: 15px;
  }

  .save-status {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;

    &.saved {
      color: var(--green);
      background-color: var(--green-fade);
    }

    &.unsaved {
      color: var(--orange, #f59e0b);
      background-color: var(--orange-fade, rgba(245, 158, 11, 0.1));
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }

    &.saved .status-dot {
      background-color: var(--green);
    }

    &.unsaved .status-dot {
      background-color: var(--orange, #f59e0b);
    }
  }

  .form-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;

    h2 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 18px;
      color: var(--primary);
      border-bottom: 1px solid var(--border);
      padding-bottom: 10px;
    }
  }

  .invoice-totals {
    margin-left: auto;
    width: 300px;
    border-top: 1px solid var(--border);
    padding-top: 15px;
  }

  .totals-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    &.total {
      font-weight: 700;
      font-size: 18px;
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid var(--border);
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 20px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .discount-input {
    width: 100px;
    padding: 8px;
    border: 1px solid var(--border);
    border-radius: 4px;
    text-align: right;

    &:focus {
      outline: none;
      border-color: var(--primary);
    }
  }

  .form-group {
    margin-bottom: 1rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: var(--black);
    }

    input, textarea, select {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
      font-size: 1rem;
      transition: border-color 0.2s;

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px var(--primary-light);
      }

      &.error {
        border-color: var(--red);
      }
    }

    textarea {
      resize: vertical;
      min-height: 100px;
    }
  }

  .error-message {
    color: var(--red);
    font-size: 0.875rem;
    margin-top: 0.25rem;
  }

  @media (max-width: 768px) {
    .invoice-editor {
      grid-template-columns: 1fr;
    }

    .item-cell {
      padding: 0.5rem;
    }
  }
</style> 