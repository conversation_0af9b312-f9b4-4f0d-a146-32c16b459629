<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { getInvoiceById, getStatusDisplay, type ApiInvoice } from '$lib/api/invoices';
  import { formatCurrency } from '$lib/config/currency';
  import { generateInvoicePDFFromLayout } from '$lib/utils/pdfGenerator';
  import Button from '$lib/components/Button.svelte';
  import LineItems from '$lib/components/page/LineItems.svelte';
  import { sendInvoiceEmail, getEmailHistoryForInvoice, type EmailHistoryItem, type EmailAttachment } from '$lib/api/emailService';
  import { customers } from '$lib/stores/customerStore';
  import Modal from '$lib/components/Modal.svelte';

  let invoiceId: string | null = null;
  let invoiceData: ApiInvoice | null = null;
  let isLoading = true;
  let error: string | null = null;
  let isSendingEmail = false;
  let emailHistory: EmailHistoryItem[] = [];
  let showEmailModal = false;
  let emailAddress = '';
  let emailSubject = '';
  let emailMessage = '';
  let taxMode: 0 | 1 = 0; // Default to Tax Exclusive

  onMount(async () => {
    invoiceId = $page.params.invoiceId;
    if (invoiceId) {
      await fetchInvoiceDetails(invoiceId);
    } else {
      error = 'Invoice ID not provided.';
      isLoading = false;
    }
  });

  async function fetchInvoiceDetails(id: string) {
    isLoading = true;
    error = null;
    try {
      invoiceData = await getInvoiceById(id);
      if (!invoiceData) {
        throw new Error('Invoice not found');
      }
      
      // Load email history for this invoice
      emailHistory = await getEmailHistoryForInvoice(id);
      
      // Pre-populate email address if customer has one
      // Note: ApiInvoice doesn't have customerEmail or customerId, so we'll skip this for now
      
      // Set default email subject
      const invoiceTotal = invoiceData.invoiceLines.reduce((sum, line) => sum + line.total, 0);
      emailSubject = `Invoice ${invoiceData.invoiceNumber || 'Draft'} - ${formatCurrency(invoiceTotal)}`;
      
    } catch (err) {
      console.error('Error fetching invoice details:', err);
      error = err instanceof Error ? err.message : 'An unknown error occurred';
      addToast({
        type: 'error',
        message: `Failed to load invoice: ${error}`
      });
    } finally {
      isLoading = false;
    }
  }

  async function handleExportPDF() {
    if (!invoiceData) return;

    try {
      await generateInvoicePDFFromLayout(invoiceData);
      addToast({
        message: 'PDF exported successfully',
        type: 'success'
      });
    } catch (error) {
      console.error('Error exporting PDF:', error);
      addToast({
        message: 'Failed to export PDF',
        type: 'error'
      });
    }
  }

  async function handleSendEmail() {
    if (!invoiceData || !emailAddress.trim()) {
      addToast({
        message: 'Please enter a valid email address',
        type: 'error'
      });
      return;
    }

    isSendingEmail = true;

    try {
      // Generate PDF as attachment
      const pdfBlob = await generateInvoicePDFAsBlob(invoiceData);
      const pdfBase64 = await blobToBase64(pdfBlob);
      
      const pdfAttachment: EmailAttachment = {
        filename: `${invoiceData.invoiceNumber}.pdf`,
        content: pdfBase64,
        contentType: 'application/pdf'
      };

      // Send the email
      const invoiceTotal = invoiceData.invoiceLines.reduce((sum, line) => sum + line.total, 0);
      const response = await sendInvoiceEmail(
        invoiceData.id || '',
        emailAddress,
        'Customer', // ApiInvoice doesn't have customerName
        invoiceData.invoiceNumber?.toString() || 'Draft',
        invoiceTotal,
        pdfAttachment
      );

      if (response.success) {
        addToast({
          message: 'Invoice email sent successfully',
          type: 'success'
        });
        
        // Refresh email history
        emailHistory = await getEmailHistoryForInvoice(invoiceData.id || '');
        
        // Close modal
        showEmailModal = false;
      } else {
        throw new Error(response.error || 'Failed to send email');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      addToast({
        message: error instanceof Error ? error.message : 'Failed to send email',
        type: 'error'
      });
    } finally {
      isSendingEmail = false;
    }
  }

  function openEmailModal() {
    // ApiInvoice doesn't have customer info, so we'll just open the modal
    showEmailModal = true;
  }

  function closeEmailModal() {
    showEmailModal = false;
  }

  // Helper function to generate PDF as blob
  async function generateInvoicePDFAsBlob(invoice: ApiInvoice): Promise<Blob> {
    // This is a simplified version - in a real implementation, you'd generate the actual PDF blob
    // For now, we'll create a mock PDF content
    const pdfContent = `Mock PDF content for invoice ${invoice.invoiceNumber || 'Draft'}`;
    return new Blob([pdfContent], { type: 'application/pdf' });
  }

  // Helper function to convert blob to base64
  function blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 content
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  function getEmailStatusColor(status: string): string {
    switch (status) {
      case 'sent': return '#10b981';
      case 'failed': return '#ef4444';
      case 'pending': return '#f59e0b';
      default: return '#6b7280';
    }
  }

</script>

<div class="container">
  <PageHeader title="Invoice Details">
    <svelte:fragment slot="actions">
      {#if invoiceData}
        <Button variant="secondary" on:click={() => goto(`/invoices/${invoiceData?.id}/edit`)}>
          Edit Invoice
        </Button>
        <Button variant="secondary" on:click={handleExportPDF}>
          Export PDF
        </Button>
        <Button variant="primary" on:click={openEmailModal}>
          Send Email
        </Button>
      {/if}
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if isLoading}
      <LoadingSpinner message="Loading invoice details..." />
    {:else if error}
      <div class="error-message">
        <p>Error: {error}</p>
      </div>
    {:else if invoiceData}
      <div class="a4-canvas">
        <!-- Row 1: Invoice Header -->
        <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
          <div class="layout-column">
            <div class="invoice-title">
              <h1>INVOICE</h1>
              <div class="invoice-number">#{invoiceData.invoiceNumber || 'Draft'}</div>
            </div>
          </div>
          <div class="layout-column">
            <div class="invoice-status-section">
              <div class="status-badge" style="background-color: {getStatusDisplay(invoiceData.status).color}20; color: {getStatusDisplay(invoiceData.status).color};">
                {getStatusDisplay(invoiceData.status).name}
              </div>
            </div>
          </div>
        </div>

        <!-- Row 2: Company and Customer Info -->
        <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
          <div class="layout-column">
            <div class="company-details">
              <h3>From:</h3>
              <div class="company-info">
                <strong>Your Company Name</strong><br>
                123 Business Street<br>
                City, State 12345<br>
                Phone: (*************<br>
                Email: <EMAIL>
              </div>
            </div>
          </div>
          <div class="layout-column">
            <div class="customer-details">
              <h3>Bill To:</h3>
              <div class="customer-info">
                <strong>Customer Name</strong><br>
                Customer Address<br>
                Email: N/A
              </div>
            </div>
          </div>
        </div>

        <!-- Row 3: Invoice Details -->
        <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
          <div class="layout-column">
            <div class="invoice-dates">
              <div class="date-field">
                <strong>Issue Date:</strong> {invoiceData.issueDate ? new Date(invoiceData.issueDate).toLocaleDateString() : 'N/A'}
              </div>
              <div class="date-field">
                <strong>Due Date:</strong> {invoiceData.dueDate ? new Date(invoiceData.dueDate).toLocaleDateString() : 'N/A'}
              </div>
            </div>
          </div>
          <div class="layout-column">
            <div class="invoice-total">
              <div class="total-amount">
                <strong>Total Amount:</strong> {formatCurrency(invoiceData.invoiceLines.reduce((sum, line) => sum + line.total, 0))}
              </div>
            </div>
          </div>
        </div>

        <!-- Row 4: Line Items -->
        <div class="layout-row" style="grid-template-columns: 1fr;">
          <div class="layout-column">
            
              {#if invoiceData.invoiceLines && invoiceData.invoiceLines.length > 0}
                <LineItems
                  lineItems={invoiceData.invoiceLines}
                  mode="readonly"
                  currency="£"
                  taxMode={taxMode}
                />
              {:else}
                <p>No line items found.</p>
              {/if}
            
          </div>
        </div>

        <!-- Row 5: Notes and Terms -->
        {#if invoiceData.notes || invoiceData.paymentTerms}
          <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
            <div class="layout-column">
              {#if invoiceData.notes}
                <div class="notes-section">
                  <h3>Notes</h3>
                  <p>{invoiceData.notes}</p>
                </div>
              {/if}
            </div>
            <div class="layout-column">
              {#if invoiceData.paymentTerms}
                <div class="terms-section">
                  <h3>Payment Terms</h3>
                  <p>{invoiceData.paymentTerms}</p>
                </div>
              {/if}
            </div>
          </div>
        {/if}

        <!-- Email History Section -->
        {#if emailHistory.length > 0}
          <div class="email-history-section">
            <h3>Email History</h3>
            <div class="email-history-list">
              {#each emailHistory as email}
                <div class="email-history-item">
                  <div class="email-info">
                    <div class="email-header">
                      <span class="email-to">{email.to}</span>
                      <span class="email-status" style="color: {getEmailStatusColor(email.status)};">
                        {email.status}
                      </span>
                    </div>
                    <div class="email-subject">{email.subject}</div>
                    <div class="email-date">{new Date(email.sentAt).toLocaleString()}</div>
                    {#if email.error}
                      <div class="email-error">{email.error}</div>
                    {/if}
                  </div>
                </div>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    {:else}
      <p>Invoice not found.</p>
    {/if}
  </main>
</div>

<!-- Email Modal using Modal component -->
<Modal bind:show={showEmailModal} title="Send Invoice Email" on:close={closeEmailModal}>
  <div class="modal-body">
    <div class="form-group">
      <label for="email-address">Email Address</label>
      <input
        id="email-address"
        type="email"
        bind:value={emailAddress}
        placeholder="<EMAIL>"
        required
      />
    </div>
    
    <div class="form-group">
      <label for="email-subject">Subject</label>
      <input
        id="email-subject"
        type="text"
        bind:value={emailSubject}
        placeholder="Invoice subject"
        required
      />
    </div>
    
    <div class="form-group">
      <label for="email-message">Additional Message (Optional)</label>
      <textarea
        id="email-message"
        bind:value={emailMessage}
        placeholder="Add a personal message to include with the invoice..."
        rows="4"
      ></textarea>
    </div>
    
    <div class="email-preview">
      <h4>Email Preview</h4>
      <p>The invoice will be sent as a PDF attachment with a professional email template.</p>
      {#if invoiceData}
        <div class="preview-details">
          <p><strong>Invoice:</strong> {invoiceData.invoiceNumber}</p>
          <p><strong>Amount:</strong> {formatCurrency(invoiceData.invoiceLines.reduce((sum, line) => sum + line.total, 0))}</p>
          <p><strong>Customer:</strong> Customer</p>
        </div>
      {/if}
    </div>
  </div>

  <svelte:fragment slot="footer">
    <Button variant="secondary" on:click={closeEmailModal} disabled={isSendingEmail}>
      Cancel
    </Button>
    <Button 
      variant="primary" 
      on:click={handleSendEmail} 
      disabled={isSendingEmail || !emailAddress.trim()}
    >
      {#if isSendingEmail}
        Sending...
      {:else}
        Send Email
      {/if}
    </Button>
  </svelte:fragment>
</Modal>

<style lang="less">
  main {
    padding: 1rem 2rem;
  }

  .error-message {
    text-align: center;
    padding: 2rem;
    color: var(--danger);
  }

  .a4-canvas {
    width: 794px;
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
    min-height: 1123px;
    padding: 40px;
    box-sizing: border-box;
  }

  .layout-row {
    border-bottom: 1px dashed #d1d5db;
    position: relative;
    margin-bottom: 2rem;

    &:last-child {
      border-bottom: none;
    }

    display: grid;
    gap: 1rem;
    min-height: 60px;
  }

  .layout-column {
    background: white;
    position: relative;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 4px;
  }

  .invoice-title {
    h1 {
      margin: 0;
      font-size: 2rem;
      font-weight: bold;
      color: var(--primary);
    }

    .invoice-number {
      font-size: 1.2rem;
      color: var(--grey);
      margin-top: 0.5rem;
    }
  }

  .invoice-status-section {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;

    .status-badge {
      padding: 0.5rem 1rem;
      border-radius: var(--br);
      font-weight: 600;
      font-size: 0.875rem;
    }
  }

  .company-details, .customer-details {
    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
      font-size: 1.125rem;
    }
  }

  .company-info, .customer-info {
    line-height: 1.6;
    color: var(--grey);
  }

  .invoice-dates {
    .date-field {
      margin-bottom: 0.5rem;
      color: var(--grey);
    }
  }

  .invoice-total {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .total-amount {
      font-size: 1.2rem;
      color: var(--black);
    }
  }

  .line-items-section {
    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
      font-size: 1.125rem;
    }
  }

  .notes-section, .terms-section {
    h3 {
      margin: 0 0 0.5rem 0;
      color: var(--black);
    }

    p {
      margin: 0;
      color: var(--grey);
      line-height: 1.5;
    }
  }

  .email-history-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border);

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }
  }

  .email-history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .email-history-item {
    background: var(--bg-light);
    border-radius: var(--br);
    padding: 1rem;
    border-left: 4px solid var(--primary);

    .email-info {
      .email-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .email-to {
          font-weight: 600;
          color: var(--black);
        }

        .email-status {
          font-size: 0.875rem;
          font-weight: 600;
          text-transform: uppercase;
        }
      }

      .email-subject {
        color: var(--grey);
        margin-bottom: 0.25rem;
      }

      .email-date {
        font-size: 0.875rem;
        color: var(--grey-light);
      }

      .email-error {
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: var(--danger-light);
        color: var(--danger);
        border-radius: 4px;
        font-size: 0.875rem;
      }
    }
  }

  .modal-body .form-group {
     margin-bottom: 1.5rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--black);
      }

      input, textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 1rem;
        transition: border-color 0.2s;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 3px var(--primary-light);
        }
      }

      textarea {
        resize: vertical;
        min-height: 100px;
      }
  }

  .modal-body .email-preview {
      background: var(--bg-light);
      padding: 1rem;
      border-radius: var(--br);
      margin-top: 1rem;

      h4 {
        margin: 0 0 0.5rem 0;
        color: var(--black);
      }

      p {
        margin: 0 0 1rem 0;
        color: var(--grey);
        font-size: 0.875rem;
      }

      .preview-details {
        background: white;
        padding: 1rem;
        border-radius: 4px;
        border: 1px solid var(--border);

        p {
          margin: 0.25rem 0;
          font-size: 0.875rem;
        }
      }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      margin: 0;
      color: var(--grey);
      font-size: 1.25rem;
    }
  }

  .totals-section {
    background-color: var(--bg);
    border-radius: 8px;
    padding: 1.5rem;
  }

</style>