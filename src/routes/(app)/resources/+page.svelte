<script lang="ts">
  import { onMount } from 'svelte';
  import { 
    getResources, 
    createResource, 
    updateResource, 
    deleteResource,
    checkResourceAvailability,
    type Resource,
    type MaintenanceEntry
  } from '$lib/api/jobs';
  import Button from '$lib/components/Button.svelte';
  import Modal from '$lib/components/Modal.svelte';
  import ConfirmDelete from '$lib/components/ConfirmDelete.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import Grid from '$lib/components/Grid.svelte';
  import { addToast } from '$lib/stores/toastStore';

  let resources: Resource[] = [];
  let showCreateModal = false;
  let showDeleteModal = false;
  let showMaintenanceModal = false;
  let editingResource: Resource | null = null;
  let resourceToDelete: Resource | null = null;
  let selectedResource: Resource | null = null;
  let loading = false;
  let filterType = 'All';
  let searchTerm = '';

  // Grid and Sorting
  const gridHeaders = [
    { text: 'Name', key: 'name', sortable: true },
    { text: 'Type', key: 'type', sortable: true },
    { text: 'Cost', key: 'cost', sortable: false }, // Not directly sortable from Resource fields
    { text: 'Location', key: 'location', sortable: true },
    { text: 'Available', key: 'isAvailable', sortable: true },
    { text: 'Actions', key: 'actions', sortable: false }
  ];
  let currentSort = { key: 'name', direction: 'ascending' as 'ascending' | 'descending' | '' };

  // Form data
  let formData = {
    name: '',
    type: 'Equipment' as 'Equipment' | 'Vehicle' | 'Tool' | 'Material',
    description: '',
    costPerHour: 0,
    costPerUnit: 0,
    isAvailable: true,
    location: ''
  };

  // Maintenance form data
  let maintenanceData = {
    date: new Date().toISOString().split('T')[0],
    type: 'Routine' as 'Routine' | 'Repair' | 'Inspection',
    description: '',
    cost: 0,
    performedBy: ''
  };

  onMount(async () => {
    await loadResources();
  });

  async function loadResources() {
    loading = true;
    try {
      resources = await getResources();
    } catch (error) {
      console.error('Error loading resources:', error);
      addToast({ type: 'error', message: 'Error loading resources' });
    } finally {
      loading = false;
    }
  }

  function handleHeaderClick(headerKey: string) {
    if (gridHeaders.find(h => h.key === headerKey && !h.sortable)) return;
    if (currentSort.key === headerKey) {
      currentSort.direction = currentSort.direction === 'ascending' ? 'descending' : 'ascending';
    } else {
      currentSort.key = headerKey;
      currentSort.direction = 'ascending';
    }
    // Trigger Svelte reactivity if direct property assignment isn't enough (usually it is)
    // currentSort = { ...currentSort }; 
  }

  function openCreateModal() {
    resetForm();
    showCreateModal = true;
    editingResource = null;
  }

  function openEditModal(resource: Resource) {
    formData = {
      name: resource.name,
      type: resource.type,
      description: resource.description || '',
      costPerHour: resource.costPerHour || 0,
      costPerUnit: resource.costPerUnit || 0,
      isAvailable: resource.isAvailable,
      location: resource.location || ''
    };
    editingResource = resource;
    showCreateModal = true;
  }

  function resetForm() {
    formData = {
      name: '',
      type: 'Equipment',
      description: '',
      costPerHour: 0,
      costPerUnit: 0,
      isAvailable: true,
      location: ''
    };
  }

  async function handleSubmit() {
    loading = true;
    try {
      const resourceData = {
        ...formData,
        maintenanceSchedule: editingResource?.maintenanceSchedule || [],
        createdAt: editingResource?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      if (editingResource) {
        await updateResource(editingResource.id, resourceData);
        addToast({ type: 'success', message: 'Resource updated successfully' });
      } else {
        await createResource(resourceData);
        addToast({ type: 'success', message: 'Resource created successfully' });
      }

      await loadResources();
      showCreateModal = false;
      resetForm();
    } catch (error) {
      console.error('Error saving resource:', error);
      addToast({ type: 'error', message: 'Error saving resource' });
    } finally {
      loading = false;
    }
  }

  function initiateDelete(resource: Resource) {
    resourceToDelete = resource;
    showDeleteModal = true;
  }

  async function confirmDelete() {
    if (resourceToDelete) {
      loading = true;
      try {
        await deleteResource(resourceToDelete.id);
        await loadResources();
        addToast({ type: 'success', message: 'Resource deleted successfully' });
      } catch (error) {
        console.error('Error deleting resource:', error);
        addToast({ type: 'error', message: 'Error deleting resource' });
      } finally {
        loading = false;
        showDeleteModal = false;
        resourceToDelete = null;
      }
    }
  }

  function openMaintenanceModal(resource: Resource) {
    selectedResource = resource;
    resetMaintenanceForm();
    showMaintenanceModal = true;
  }

  function resetMaintenanceForm() {
    maintenanceData = {
      date: new Date().toISOString().split('T')[0],
      type: 'Routine',
      description: '',
      cost: 0,
      performedBy: ''
    };
  }

  async function addMaintenanceEntry() {
    if (!selectedResource) return;

    const newEntry: MaintenanceEntry = {
      id: Date.now().toString(),
      ...maintenanceData
    };

    const updatedResource = {
      ...selectedResource,
      maintenanceSchedule: [...(selectedResource.maintenanceSchedule || []), newEntry],
      updatedAt: new Date().toISOString()
    };

    try {
      await updateResource(selectedResource.id, updatedResource);
      await loadResources();
      addToast({ type: 'success', message: 'Maintenance entry added successfully' });
      showMaintenanceModal = false;
    } catch (error) {
      console.error('Error adding maintenance entry:', error);
      addToast({ type: 'error', message: 'Error adding maintenance entry' });
    }
  }

  function getTypeColor(type: string): string {
    switch (type) {
      case 'Equipment': return 'bg-blue-100 text-blue-800';
      case 'Vehicle': return 'bg-green-100 text-green-800';
      case 'Tool': return 'bg-yellow-100 text-yellow-800';
      case 'Material': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  function getCostDisplay(resource: Resource): string {
    if (resource.costPerHour && resource.costPerUnit) {
      return `$${resource.costPerHour}/hr, $${resource.costPerUnit}/unit`;
    } else if (resource.costPerHour) {
      return `$${resource.costPerHour}/hour`;
    } else if (resource.costPerUnit) {
      return `$${resource.costPerUnit}/unit`;
    }
    return 'No cost set';
  }

  // Reactive statement for filtered and sorted resources
  $: sortedAndFilteredResources = (() => {
    let tempResources = resources.filter(resource => {
      const matchesType = filterType === 'All' || resource.type === filterType;
      const matchesSearch = resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (resource.description || '').toLowerCase().includes(searchTerm.toLowerCase());
      return matchesType && matchesSearch;
    });

    if (currentSort.key) {
      tempResources.sort((a, b) => {
        const key = currentSort.key as keyof Resource;
        let valA = a[key];
        let valB = b[key];

        if (key === 'isAvailable' && typeof valA === 'boolean' && typeof valB === 'boolean') {
            if (valA === valB) return 0;
            // true (Available) should come before false (Unavailable) for asc
            if (currentSort.direction === 'ascending') {
                return valA ? -1 : 1; 
            } else {
                return valA ? 1 : -1;
            }
        }
        
        if (typeof valA === 'string' && typeof valB === 'string') {
          valA = valA.toLowerCase();
          valB = valB.toLowerCase();
        } else if (typeof valA === 'number' && typeof valB === 'number') {
          // numbers are fine for direct comparison
        } else if (valA === null || valA === undefined) {
          return currentSort.direction === 'ascending' ? 1 : -1; // nulls/undefined last for asc
        } else if (valB === null || valB === undefined) {
          return currentSort.direction === 'ascending' ? -1 : 1; // nulls/undefined last for asc
        } else {
          // Fallback for mixed types or other types - convert to string for comparison
          valA = String(valA).toLowerCase();
          valB = String(valB).toLowerCase();
        }

        if (valA < valB) {
          return currentSort.direction === 'ascending' ? -1 : 1;
        }
        if (valA > valB) {
          return currentSort.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }
    return tempResources;
  })();

  function getTypeBadgeClass(type: string): string {
    switch (type) {
      case 'Equipment': return 'bg-blue-100 text-blue-800';
      case 'Vehicle': return 'bg-green-100 text-green-800';
      case 'Tool': return 'bg-yellow-100 text-yellow-800';
      case 'Material': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }
</script>

<div class="container">

<PageHeader title="Resources">
  <div slot="actions">
    <Button variant="primary" on:click={openCreateModal}>
      Add Resource
    </Button>
  </div>
</PageHeader>

<main>
<!-- Filters -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
  <div class="flex flex-col sm:flex-row gap-4">
    <div class="flex-1">
      <input
        type="text"
        placeholder="Search resources..."
        bind:value={searchTerm}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div>
      <select
        bind:value={filterType}
        class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="All">All Types</option>
        <option value="Equipment">Equipment</option>
        <option value="Vehicle">Vehicles</option>
        <option value="Tool">Tools</option>
        <option value="Material">Materials</option>
      </select>
    </div>
  </div>
</div>

{#if loading}
  <LoadingSpinner />
{:else}
  <!-- Use Grid component -->
  <Grid
    headers={gridHeaders}
    dataRows={sortedAndFilteredResources}
    emptyMessage="No resources found matching your criteria."
    {currentSort}
    onHeaderClick={handleHeaderClick}
  >
    <svelte:fragment slot="cell" let:row let:headerKey let:value>
      {#if headerKey === 'type'}
        <span class="px-2 py-0.5 text-xs font-medium rounded-full {getTypeBadgeClass((row as Resource).type)}">
          {(row as Resource).type}
        </span>
      {:else if headerKey === 'cost'}
        {getCostDisplay(row as Resource)}
      {:else if headerKey === 'isAvailable'}
        <div class="flex items-center">
          <div class="w-2.5 h-2.5 rounded-full {(row as Resource).isAvailable ? 'bg-green-500' : 'bg-red-500'}" 
               title={(row as Resource).isAvailable ? 'Available' : 'Unavailable'}></div>
          <span class="ml-2 text-sm">{(row as Resource).isAvailable ? 'Available' : 'Unavailable'}</span>
        </div>
      {:else if headerKey === 'actions'}
        <div class="flex space-x-1 md:space-x-2">
          <Button variant="secondary" size="small" on:click={() => openEditModal(row as Resource)} title="Edit Resource">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" /></svg>
            <span class="hidden sm:inline ml-1">Edit</span>
          </Button>
          <Button variant="tertiary" size="small" on:click={() => openMaintenanceModal(row as Resource)} title="Manage Maintenance">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
            <span class="hidden sm:inline ml-1">Maint.</span>
          </Button>
          <Button variant="danger" size="small" on:click={() => initiateDelete(row as Resource)} title="Delete Resource">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg>
            <span class="hidden sm:inline ml-1">Delete</span>
          </Button>
        </div>
      {:else if row[headerKey] !== null && row[headerKey] !== undefined}
        {value}
      {:else}
        <span class="text-gray-400">N/A</span>
      {/if}
    </svelte:fragment>
  </Grid>

  {#if sortedAndFilteredResources.length === 0 && !loading}
    <!-- This specific empty message for 'no results' is now handled by Grid's emptyMessage prop -->
    <!-- However, if you need a different message when resources array is empty initially vs. no filter results, keep a similar block -->
  {/if}
{/if}

</main>
</div>

<!-- Create/Edit Resource Modal -->
<Modal bind:show={showCreateModal} title={editingResource ? 'Edit Resource' : 'Create Resource'}>
  <form on:submit|preventDefault={handleSubmit} class="space-y-6">
    <!-- Basic Information -->
    <div class="grid gap-4 md:grid-cols-2">
      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
        <input 
          id="name" 
          type="text" 
          bind:value={formData.name} 
          required 
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div>
        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type *</label>
        <select 
          id="type" 
          bind:value={formData.type}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="Equipment">Equipment</option>
          <option value="Vehicle">Vehicle</option>
          <option value="Tool">Tool</option>
          <option value="Material">Material</option>
        </select>
      </div>
    </div>

    <div>
      <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
      <textarea 
        id="description" 
        bind:value={formData.description}
        rows="3"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      ></textarea>
    </div>

    <!-- Cost Information -->
    <div class="grid gap-4 md:grid-cols-2">
      <div>
        <label for="costPerHour" class="block text-sm font-medium text-gray-700 mb-1">Cost Per Hour ($)</label>
        <input 
          id="costPerHour" 
          type="number" 
          step="0.01" 
          bind:value={formData.costPerHour}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div>
        <label for="costPerUnit" class="block text-sm font-medium text-gray-700 mb-1">Cost Per Unit ($)</label>
        <input 
          id="costPerUnit" 
          type="number" 
          step="0.01" 
          bind:value={formData.costPerUnit}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>

    <div class="grid gap-4 md:grid-cols-2">
      <div>
        <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
        <input 
          id="location" 
          type="text" 
          bind:value={formData.location}
          placeholder="e.g., Main Office, Storage Room"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div class="flex items-center">
        <label class="flex items-center gap-2">
          <input 
            type="checkbox" 
            bind:checked={formData.isAvailable}
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span class="text-sm font-medium text-gray-700">Available</span>
        </label>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-end gap-2">
      <Button type="button" variant="secondary" on:click={() => showCreateModal = false}>
        Cancel
      </Button>
      <Button type="submit" variant="primary" disabled={loading}>
        {loading ? 'Saving...' : editingResource ? 'Update' : 'Create'}
      </Button>
    </div>
  </form>
</Modal>

<!-- Maintenance Modal -->
<Modal bind:show={showMaintenanceModal} title="Add Maintenance Entry">
  <form on:submit|preventDefault={addMaintenanceEntry} class="space-y-6">
    <div class="grid gap-4 md:grid-cols-2">
      <div>
        <label for="maintenanceDate" class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
        <input 
          id="maintenanceDate" 
          type="date" 
          bind:value={maintenanceData.date} 
          required 
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div>
        <label for="maintenanceType" class="block text-sm font-medium text-gray-700 mb-1">Type *</label>
        <select 
          id="maintenanceType" 
          bind:value={maintenanceData.type}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="Routine">Routine</option>
          <option value="Repair">Repair</option>
          <option value="Inspection">Inspection</option>
        </select>
      </div>
    </div>

    <div>
      <label for="maintenanceDescription" class="block text-sm font-medium text-gray-700 mb-1">Description *</label>
      <textarea 
        id="maintenanceDescription" 
        bind:value={maintenanceData.description}
        rows="3"
        required
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      ></textarea>
    </div>

    <div class="grid gap-4 md:grid-cols-2">
      <div>
        <label for="maintenanceCost" class="block text-sm font-medium text-gray-700 mb-1">Cost ($)</label>
        <input 
          id="maintenanceCost" 
          type="number" 
          step="0.01" 
          bind:value={maintenanceData.cost}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div>
        <label for="performedBy" class="block text-sm font-medium text-gray-700 mb-1">Performed By</label>
        <input 
          id="performedBy" 
          type="text" 
          bind:value={maintenanceData.performedBy}
          placeholder="Name or company"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-end gap-2">
      <Button type="button" variant="secondary" on:click={() => showMaintenanceModal = false}>
        Cancel
      </Button>
      <Button type="submit" variant="primary">
        Add Entry
      </Button>
    </div>
  </form>
</Modal>

<!-- Delete Confirmation Modal -->
<ConfirmDelete
  bind:show={showDeleteModal}
  title="Delete Resource"
  message="Are you sure you want to delete this resource? This action cannot be undone."
  on:confirm={confirmDelete}
/>

<style>
  /* Removed old .grid styles as Grid.svelte handles its own styling */
</style>