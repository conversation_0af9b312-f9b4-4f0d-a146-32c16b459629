<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { addToast } from '$lib/stores/toastStore';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import Tabs from '$lib/components/Tabs.svelte';
  import { user } from '$lib/stores/auth';
  import { getStaffById, updateStaff, deleteStaff, type Staff } from '$lib/api/staff';
  import { getJobsByStaff, type Job } from '$lib/api/jobs';
  import { getCalendarEvents, type CalendarEvent } from '$lib/api/calendar';
  import { formatCurrency } from '$lib/config/currency';

  let staff: Staff | null = null;
  let loading = true;
  let saving = false;
  let deleting = false;
  let error: string | null = null;
  let staffId: string;

  // Tab management
  let activeTab = 'details';

  // Data for additional tabs
  let staffJobs: Job[] = [];
  let staffEvents: CalendarEvent[] = [];
  let loadingJobs = false;
  let loadingEvents = false;

  // Tab configuration
  const tabs = [
    { id: 'details', label: 'Details' },
    { id: 'schedule', label: 'Schedule' },
    { id: 'jobs', label: 'Jobs' },
    { id: 'availability', label: 'Availability' },
    { id: 'performance', label: 'Performance' }
  ];

  // Redirect to login if not logged in
  onMount(() => {
    const unsubscribe = user.subscribe(value => {
      if (!value) {
        goto('/login');
      }
    });

    // Get staff ID from URL params
    staffId = $page.params.id;
    if (staffId) {
      fetchStaff();
    } else {
      error = 'No staff ID provided';
      loading = false;
    }

    return unsubscribe;
  });

  async function fetchStaff() {
    loading = true;
    error = null;
    try {
      staff = await getStaffById(staffId);
      if (staff) {
        // Load additional staff data
        await loadStaffData();
      } else {
        error = 'Staff member not found';
      }
    } catch (err) {
      error = 'Failed to fetch staff details';
      console.error('Fetch staff error:', err);
    } finally {
      loading = false;
    }
  }

  async function loadStaffData() {
    // Load all staff-related data in parallel
    await Promise.all([
      loadStaffJobs(),
      loadStaffEvents()
    ]);
  }

  async function loadStaffJobs() {
    if (!staffId) return;
    
    loadingJobs = true;
    try {
      staffJobs = await getJobsByStaff(staffId);
    } catch (err) {
      console.error('Error loading staff jobs:', err);
      addToast({ message: 'Failed to load staff jobs', type: 'error' });
    } finally {
      loadingJobs = false;
    }
  }

  async function loadStaffEvents() {
    if (!staffId) return;
    
    loadingEvents = true;
    try {
      // Get events for a wide date range
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(); // 30 days ago
      const endDate = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(); // 90 days from now
      
      const allEvents = await getCalendarEvents(startDate, endDate);
      // Filter events for this staff member
      staffEvents = allEvents.filter(event => 
        event.assignedStaff.some(assignment => assignment.staffId === staffId)
      );
    } catch (err) {
      console.error('Error loading staff events:', err);
      addToast({ message: 'Failed to load staff events', type: 'error' });
    } finally {
      loadingEvents = false;
    }
  }

  async function handleToggleActive() {
    if (!staff) return;

    saving = true;
    try {
      const updatedStaff = await updateStaff(staffId, { isActive: !staff.isActive });
      staff = updatedStaff;
      addToast({ 
        message: `Staff member ${staff.isActive ? 'activated' : 'deactivated'} successfully`, 
        type: 'success' 
      });
    } catch (err) {
      console.error('Error updating staff status:', err);
      addToast({ message: 'Failed to update staff status', type: 'error' });
    } finally {
      saving = false;
    }
  }

  async function handleDelete() {
    if (!staff) return;

    if (!confirm(`Are you sure you want to delete staff member "${staff.fullName}"? This action cannot be undone.`)) {
      return;
    }

    deleting = true;
    try {
      await deleteStaff(staffId);
      addToast({ message: 'Staff member deleted successfully', type: 'success' });
      goto('/staff');
    } catch (err) {
      console.error('Error deleting staff:', err);
      addToast({ message: 'Failed to delete staff member', type: 'error' });
    } finally {
      deleting = false;
    }
  }

  function handleBack() {
    goto('/staff');
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  function formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  function formatTime(dateString: string): string {
    return new Date(dateString).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
  }

  function getDayName(dayKey: string): string {
    const days: Record<string, string> = {
      monday: 'Monday',
      tuesday: 'Tuesday', 
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday'
    };
    return days[dayKey] || dayKey;
  }

  // Calculate performance metrics
  $: performanceMetrics = staff ? {
    totalJobs: staffJobs.length,
    completedJobs: staffJobs.filter(job => job.status.isCompleted).length,
    upcomingJobs: staffJobs.filter(job => !job.status.isCompleted && job.scheduledDateTime && new Date(job.scheduledDateTime) > new Date()).length,
    totalHours: staffJobs.reduce((sum, job) => sum + (job.actualDuration || job.estimatedDuration || 0), 0) / 60,
    totalRevenue: staffJobs.reduce((sum, job) => sum + (job.actualCost?.totalCost || job.estimatedCost?.totalCost || 0), 0),
    averageJobRating: 4.5 // This would come from actual job ratings
  } : null;

  // Filter events by time period
  $: upcomingEvents = staffEvents.filter(event => new Date(event.startDateTime) > new Date()).sort((a, b) => new Date(a.startDateTime).getTime() - new Date(b.startDateTime).getTime());
  $: pastEvents = staffEvents.filter(event => new Date(event.endDateTime) < new Date()).sort((a, b) => new Date(b.startDateTime).getTime() - new Date(a.startDateTime).getTime());
</script>

<svelte:head>
  <title>{staff ? `${staff.fullName} - Staff Details` : 'Staff Details'}</title>
</svelte:head>

<div class="container">
  <PageHeader title={staff ? `${staff.fullName}` : 'Staff Details'}>
    <svelte:fragment slot="actions">
      <Button on:click={handleBack} variant="secondary" type="button">Back to Staff</Button>
      {#if staff}
        <Button
          on:click={handleToggleActive}
          variant={staff.isActive ? "danger" : "primary"}
          type="button"
          disabled={saving}
        >
          {saving ? 'Updating...' : (staff.isActive ? 'Deactivate' : 'Activate')}
        </Button>
        <Button on:click={() => goto(`/staff/${staffId}/edit`)} variant="primary" type="button">
          Edit Staff
        </Button>
        <Button
          on:click={handleDelete}
          variant="danger"
          type="button"
          disabled={deleting}
        >
          {deleting ? 'Deleting...' : 'Delete Staff'}
        </Button>
      {/if}
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if loading}
      <LoadingSpinner message="Loading staff details..." />
    {:else if error}
      <div class="error-container">
        <p class="error-message">{error}</p>
        <Button on:click={fetchStaff} type="button">Retry</Button>
      </div>
    {:else if staff}
      <div class="staff-details">
        <!-- Tabs Navigation -->
        <Tabs {tabs} bind:activeTab />

        <!-- Tab Content -->
        <div class="tab-content">
          {#if activeTab === 'details'}
            <div class="details-section">
              <h2>Staff Information</h2>

              <div class="staff-overview">
                <div class="overview-card">
                  <div class="staff-header">
                    <div class="staff-info">
                      <h3>{staff.fullName}</h3>
                      <p class="position">{staff.position}</p>
                      {#if staff.department}
                        <p class="department">{staff.department}</p>
                      {/if}
                      <span class="status-badge" class:active={staff.isActive} class:inactive={!staff.isActive}>
                        {staff.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>

                  <div class="contact-details">
                    <div class="detail-row">
                      <span class="label">Email:</span>
                      <span class="value">{staff.email}</span>
                    </div>
                    <div class="detail-row">
                      <span class="label">Phone:</span>
                      <span class="value">{staff.phone}</span>
                    </div>
                    <div class="detail-row">
                      <span class="label">Hire Date:</span>
                      <span class="value">{formatDate(staff.hireDate)}</span>
                    </div>
                    {#if staff.wageInfo}
                      <div class="detail-row">
                        <span class="label">Wage:</span>
                        <span class="value">
                          {formatCurrency(staff.wageInfo.rate)}
                          {staff.wageInfo.type === 'Hourly' ? '/hour' : 
                           staff.wageInfo.type === 'Salary' ? '/year' : '/job'}
                        </span>
                      </div>
                    {/if}
                  </div>

                  {#if staff.skills && staff.skills.length > 0}
                    <div class="skills-section">
                      <h4>Skills</h4>
                      <div class="skills-list">
                        {#each staff.skills as skill}
                          <span class="skill-badge">{skill}</span>
                        {/each}
                      </div>
                    </div>
                  {/if}

                  {#if staff.notes}
                    <div class="notes-section">
                      <h4>Notes</h4>
                      <p>{staff.notes}</p>
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          {:else if activeTab === 'schedule'}
            <div class="schedule-section">
              <h2>Schedule & Calendar</h2>
              
              {#if loadingEvents}
                <LoadingSpinner message="Loading schedule..." />
              {:else}
                <div class="schedule-overview">
                  <div class="upcoming-section">
                    <h3>Upcoming Appointments ({upcomingEvents.length})</h3>
                    {#if upcomingEvents.length === 0}
                      <div class="empty-state">
                        <p>No upcoming appointments scheduled.</p>
                      </div>
                    {:else}
                      <div class="events-list">
                        {#each upcomingEvents.slice(0, 10) as event}
                          <div class="event-card">
                            <div class="event-time">
                              <div class="date">{formatDate(event.startDateTime)}</div>
                              <div class="time">{formatTime(event.startDateTime)} - {formatTime(event.endDateTime)}</div>
                            </div>
                            <div class="event-details">
                              <h4>{event.title}</h4>
                              <p>{event.customerName}</p>
                              {#if event.jobAddress}
                                <p class="address">{event.jobAddress}</p>
                              {/if}
                            </div>
                            <div class="event-status">
                              <span class="priority-badge {event.priority.toLowerCase()}">{event.priority}</span>
                              <span class="status-badge">{event.status}</span>
                            </div>
                          </div>
                        {/each}
                      </div>
                    {/if}
                  </div>

                  <div class="recent-section">
                    <h3>Recent Appointments</h3>
                    {#if pastEvents.length === 0}
                      <div class="empty-state">
                        <p>No recent appointments found.</p>
                      </div>
                    {:else}
                      <div class="events-list">
                        {#each pastEvents.slice(0, 5) as event}
                          <div class="event-card past">
                            <div class="event-time">
                              <div class="date">{formatDate(event.startDateTime)}</div>
                              <div class="time">{formatTime(event.startDateTime)} - {formatTime(event.endDateTime)}</div>
                            </div>
                            <div class="event-details">
                              <h4>{event.title}</h4>
                              <p>{event.customerName}</p>
                            </div>
                            <div class="event-status">
                              <span class="status-badge completed">{event.status}</span>
                            </div>
                          </div>
                        {/each}
                      </div>
                    {/if}
                  </div>
                </div>
              {/if}
            </div>
          {:else if activeTab === 'jobs'}
            <div class="jobs-section">
              <div class="section-header">
                <h2>Assigned Jobs</h2>
                <Button on:click={() => goto(`/calendar?staffId=${staffId}`)} variant="primary" size="small">
                  Schedule New Job
                </Button>
              </div>
              
              {#if loadingJobs}
                <LoadingSpinner message="Loading jobs..." />
              {:else if staffJobs.length === 0}
                <div class="empty-state">
                  <p>No jobs assigned to this staff member.</p>
                  <Button on:click={() => goto(`/calendar?staffId=${staffId}`)} variant="primary">
                    Assign First Job
                  </Button>
                </div>
              {:else}
                <div class="data-table">
                  <div class="table-header">
                    <div class="header-cell">Job</div>
                    <div class="header-cell">Customer</div>
                    <div class="header-cell">Scheduled</div>
                    <div class="header-cell">Status</div>
                    <div class="header-cell">Duration</div>
                    <div class="header-cell">Actions</div>
                  </div>
                  {#each staffJobs.sort((a, b) => {
                    if (!a.scheduledDateTime) return 1;
                    if (!b.scheduledDateTime) return -1;
                    return new Date(b.scheduledDateTime).getTime() - new Date(a.scheduledDateTime).getTime();
                  }) as job}
                    <div class="table-row">
                      <div class="table-cell">
                        <div class="job-info">
                          <strong>{job.title}</strong>
                          {#if job.description}
                            <small>{job.description}</small>
                          {/if}
                        </div>
                      </div>
                      <div class="table-cell">
                        {job.customerName || 'Unknown Customer'}
                      </div>
                      <div class="table-cell">
                        {#if job.scheduledDateTime}
                          <div class="date-info">
                            <strong>{formatDate(job.scheduledDateTime)}</strong>
                            <small>{formatTime(job.scheduledDateTime)}</small>
                          </div>
                        {:else}
                          <span class="text-muted">Not scheduled</span>
                        {/if}
                      </div>
                      <div class="table-cell">
                        <span class="status-badge" style="background-color: {job.status.color}20; color: {job.status.color};">
                          {job.status.name}
                        </span>
                      </div>
                      <div class="table-cell">
                        {#if job.actualDuration}
                          {Math.floor(job.actualDuration / 60)}h {job.actualDuration % 60}m
                        {:else if job.estimatedDuration}
                          ~{Math.floor(job.estimatedDuration / 60)}h {job.estimatedDuration % 60}m
                        {:else}
                          -
                        {/if}
                      </div>
                      <div class="table-cell">
                        <div class="action-buttons">
                          <Button on:click={() => goto(`/jobs/${job.id}`)} variant="tertiary" size="small">
                            View
                          </Button>
                          <Button on:click={() => goto(`/calendar?jobId=${job.id}`)} variant="secondary" size="small">
                            Schedule
                          </Button>
                        </div>
                      </div>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          {:else if activeTab === 'availability'}
            <div class="availability-section">
              <h2>Availability Schedule</h2>
              
              {#if staff.availability}
                <div class="availability-grid">
                  {#each Object.entries(staff.availability) as [day, availability]}
                    {#if day !== 'timeOff'}
                      <div class="day-availability" class:available={availability.isAvailable} class:unavailable={!availability.isAvailable}>
                        <div class="day-header">
                          <h4>{getDayName(day)}</h4>
                          <span class="availability-status">
                            {availability.isAvailable ? 'Available' : 'Unavailable'}
                          </span>
                        </div>
                        {#if availability.isAvailable}
                          <div class="time-range">
                            {availability.startTime} - {availability.endTime}
                          </div>
                        {/if}
                      </div>
                    {/if}
                  {/each}
                </div>

                {#if staff.availability.timeOff && staff.availability.timeOff.length > 0}
                  <div class="time-off-section">
                    <h3>Time Off Requests</h3>
                    <div class="time-off-list">
                      {#each staff.availability.timeOff as timeOff}
                        <div class="time-off-card" class:approved={timeOff.status === 'Approved'} class:pending={timeOff.status === 'Pending'} class:rejected={timeOff.status === 'Rejected'}>
                          <div class="time-off-dates">
                            <strong>{formatDate(timeOff.startDate)} - {formatDate(timeOff.endDate)}</strong>
                          </div>
                          <div class="time-off-details">
                            <p><strong>Type:</strong> {timeOff.type}</p>
                            {#if timeOff.reason}
                              <p><strong>Reason:</strong> {timeOff.reason}</p>
                            {/if}
                          </div>
                          <div class="time-off-status">
                            <span class="status-badge {timeOff.status.toLowerCase()}">{timeOff.status}</span>
                          </div>
                        </div>
                      {/each}
                    </div>
                  </div>
                {/if}
              {/if}
            </div>
          {:else if activeTab === 'performance'}
            <div class="performance-section">
              <h2>Performance Metrics</h2>
              
              {#if performanceMetrics}
                <div class="metrics-grid">
                  <div class="metric-card">
                    <div class="metric-value">{performanceMetrics.totalJobs}</div>
                    <div class="metric-label">Total Jobs</div>
                  </div>
                  <div class="metric-card">
                    <div class="metric-value">{performanceMetrics.completedJobs}</div>
                    <div class="metric-label">Completed Jobs</div>
                  </div>
                  <div class="metric-card">
                    <div class="metric-value">{performanceMetrics.upcomingJobs}</div>
                    <div class="metric-label">Upcoming Jobs</div>
                  </div>
                  <div class="metric-card">
                    <div class="metric-value">{Math.round(performanceMetrics.totalHours)}</div>
                    <div class="metric-label">Total Hours</div>
                  </div>
                  <div class="metric-card">
                    <div class="metric-value">{formatCurrency(performanceMetrics.totalRevenue)}</div>
                    <div class="metric-label">Total Revenue</div>
                  </div>
                  <div class="metric-card">
                    <div class="metric-value">{performanceMetrics.averageJobRating.toFixed(1)}</div>
                    <div class="metric-label">Avg Rating</div>
                  </div>
                </div>

                <div class="performance-charts">
                  <div class="chart-placeholder">
                    <h3>Job Completion Rate</h3>
                    <div class="completion-rate">
                      <div class="rate-circle">
                        <span class="rate-percentage">
                          {performanceMetrics.totalJobs > 0 ? Math.round((performanceMetrics.completedJobs / performanceMetrics.totalJobs) * 100) : 0}%
                        </span>
                      </div>
                      <p>Jobs completed successfully</p>
                    </div>
                  </div>
                </div>
              {/if}
            </div>
          {/if}
        </div>
      </div>
    {:else}
      <p>Staff member not found.</p>
    {/if}
  </main>
</div>

<style lang="less">
  .staff-details {
    max-width: 1200px;
    margin: 0 auto;
  }

  .tab-content {
    margin-top: 1rem;
  }

  .details-section,
  .schedule-section,
  .jobs-section,
  .availability-section,
  .performance-section {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 2rem;
    margin-bottom: 2rem;

    h2 {
      margin: 0 0 1.5rem 0;
      font-size: 1.25rem;
      color: var(--text);
      border-bottom: 1px solid var(--border);
      padding-bottom: 0.75rem;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h2 {
      margin: 0;
      border: none;
      padding: 0;
    }
  }

  .error-container {
    text-align: center;
    padding: 2rem;

    .error-message {
      color: var(--error);
      margin-bottom: 1rem;
      font-size: 1rem;
    }
  }

  .staff-overview {
    .overview-card {
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 2rem;
    }

    .staff-header {
      margin-bottom: 1.5rem;

      .staff-info {
        h3 {
          margin: 0 0 0.5rem 0;
          font-size: 1.5rem;
          color: var(--text);
        }

        .position {
          font-size: 1.1rem;
          color: var(--primary);
          margin: 0 0 0.25rem 0;
          font-weight: 500;
        }

        .department {
          color: var(--grey);
          margin: 0 0 1rem 0;
        }
      }
    }

    .status-badge {
      padding: 0.5rem 1rem;
      border-radius: var(--br);
      font-size: 0.9rem;
      font-weight: 500;

      &.active {
        background: #10B98120;
        color: #10B981;
      }

      &.inactive {
        background: #EF444420;
        color: #EF4444;
      }
    }

    .contact-details {
      margin-bottom: 1.5rem;

      .detail-row {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--border);

        &:last-child {
          border-bottom: none;
        }

        .label {
          font-weight: 500;
          color: var(--grey);
        }

        .value {
          color: var(--text);
        }
      }
    }

    .skills-section {
      margin-bottom: 1.5rem;

      h4 {
        margin: 0 0 1rem 0;
        color: var(--text);
      }

      .skills-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .skill-badge {
          background: var(--primary-fade);
          color: var(--primary);
          padding: 0.25rem 0.75rem;
          border-radius: var(--br);
          font-size: 0.9rem;
          font-weight: 500;
        }
      }
    }

    .notes-section {
      h4 {
        margin: 0 0 1rem 0;
        color: var(--text);
      }

      p {
        color: var(--grey);
        line-height: 1.5;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--grey);

    p {
      margin-bottom: 1rem;
      font-size: 1rem;
    }
  }

  .schedule-overview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;

    .upcoming-section,
    .recent-section {
      h3 {
        margin: 0 0 1rem 0;
        color: var(--text);
        font-size: 1.1rem;
      }
    }
  }

  .events-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .event-card {
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 1rem;
      display: grid;
      grid-template-columns: auto 1fr auto;
      gap: 1rem;
      align-items: center;

      &.past {
        opacity: 0.7;
      }

      .event-time {
        .date {
          font-weight: 500;
          color: var(--text);
          font-size: 0.9rem;
        }

        .time {
          color: var(--grey);
          font-size: 0.8rem;
        }
      }

      .event-details {
        h4 {
          margin: 0 0 0.25rem 0;
          font-size: 1rem;
          color: var(--text);
        }

        p {
          margin: 0;
          color: var(--grey);
          font-size: 0.9rem;
        }

        .address {
          font-size: 0.8rem;
          color: var(--grey);
        }
      }

      .event-status {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-end;
      }
    }
  }

  .data-table {
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;
    margin-bottom: 1.5rem;

    .table-header {
      display: grid;
      grid-template-columns: 2fr 1.5fr 1.5fr 1fr 1fr 1.5fr;
      background: var(--bg);
      border-bottom: 1px solid var(--border);

      .header-cell {
        padding: 1rem;
        font-weight: 600;
        font-size: 0.9rem;
        color: var(--grey);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .table-row {
      display: grid;
      grid-template-columns: 2fr 1.5fr 1.5fr 1fr 1fr 1.5fr;
      border-bottom: 1px solid var(--border);
      transition: background-color 0.2s;

      &:hover {
        background: var(--bg);
      }

      &:last-child {
        border-bottom: none;
      }

      .table-cell {
        padding: 1rem;
        display: flex;
        align-items: center;
        font-size: 0.9rem;
      }
    }
  }

  .job-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    strong {
      color: var(--text);
    }

    small {
      color: var(--grey);
      font-size: 0.8rem;
    }
  }

  .date-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    strong {
      color: var(--text);
    }

    small {
      color: var(--grey);
      font-size: 0.8rem;
    }
  }

  .text-muted {
    color: var(--grey);
    font-style: italic;
  }

  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--br);
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;

    &.completed {
      background: #10B98120;
      color: #10B981;
    }
  }

  .priority-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--br);
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;

    &.low {
      background: #6B728020;
      color: #6B7280;
    }

    &.medium {
      background: #F59E0B20;
      color: #F59E0B;
    }

    &.high {
      background: #EF444420;
      color: #EF4444;
    }

    &.urgent {
      background: #DC262620;
      color: #DC2626;
    }
  }

  .action-buttons {
    display: flex;
    gap: 0.5rem;
  }

  .availability-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .day-availability {
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 1rem;

      &.available {
        border-color: #10B981;
        background: #10B98110;
      }

      &.unavailable {
        border-color: #EF4444;
        background: #EF444410;
      }

      .day-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        h4 {
          margin: 0;
          font-size: 1rem;
          color: var(--text);
        }

        .availability-status {
          font-size: 0.8rem;
          font-weight: 500;
          color: var(--grey);
        }
      }

      .time-range {
        font-weight: 500;
        color: var(--primary);
      }
    }
  }

  .time-off-section {
    h3 {
      margin: 0 0 1rem 0;
      color: var(--text);
    }

    .time-off-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .time-off-card {
        background: var(--bg);
        border: 1px solid var(--border);
        border-radius: var(--br);
        padding: 1rem;
        display: grid;
        grid-template-columns: auto 1fr auto;
        gap: 1rem;
        align-items: center;

        &.approved {
          border-color: #10B981;
          background: #10B98110;
        }

        &.pending {
          border-color: #F59E0B;
          background: #F59E0B10;
        }

        &.rejected {
          border-color: #EF4444;
          background: #EF444410;
        }

        .time-off-dates {
          strong {
            color: var(--text);
          }
        }

        .time-off-details {
          p {
            margin: 0 0 0.25rem 0;
            font-size: 0.9rem;
            color: var(--grey);

            strong {
              color: var(--text);
            }
          }
        }

        .time-off-status {
          .status-badge {
            &.approved {
              background: #10B98120;
              color: #10B981;
            }

            &.pending {
              background: #F59E0B20;
              color: #F59E0B;
            }

            &.rejected {
              background: #EF444420;
              color: #EF4444;
            }
          }
        }
      }
    }
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .metric-card {
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 1.5rem;
      text-align: center;

      .metric-value {
        font-size: 2rem;
        font-weight: 600;
        color: var(--primary);
        margin-bottom: 0.5rem;
      }

      .metric-label {
        font-size: 0.9rem;
        color: var(--grey);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }

  .performance-charts {
    .chart-placeholder {
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 2rem;
      text-align: center;

      h3 {
        margin: 0 0 1rem 0;
        color: var(--text);
      }

      .completion-rate {
        .rate-circle {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background: var(--primary-fade);
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 1rem;

          .rate-percentage {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
          }
        }

        p {
          color: var(--grey);
          margin: 0;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .schedule-overview {
      grid-template-columns: 1fr;
    }

    .data-table {
      .table-header,
      .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .header-cell,
      .table-cell {
        padding: 0.5rem;
      }
    }

    .availability-grid {
      grid-template-columns: 1fr;
    }

    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
</style> 