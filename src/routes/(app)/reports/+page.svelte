<script lang="ts">
  import { onMount } from 'svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import Button from '$lib/components/Button.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { formatCurrency } from '$lib/config/currency';
  import { getInvoices, getStatusDisplay, type ApiInvoice } from '$lib/api/invoices';
  import { getQuotes, type Quote } from '$lib/api/quotes';
  import { getJobs, type Job } from '$lib/api/jobs';
  import { customers } from '$lib/stores/customerStore';
  import { contactStore } from '$lib/stores/customerStore';
  import type { Contact } from '$lib/api/contacts';

  // State variables
  let isLoading = true;
  let invoices: ApiInvoice[] = [];
  let revenueData: RevenueData[] = [];
  let selectedDateRange = '30'; // days
  let selectedReportType = 'revenue';
  let startDate = '';
  let endDate = '';
  let isGeneratingReport = false;
  let isExportingCSV = false;

  // Revenue data interface
  interface RevenueData {
    date: string;
    totalInvoiced: number;
    totalPaid: number;
    invoiceCount: number;
    paidCount: number;
  }

  // Chart data
  let chartData: { labels: string[], datasets: any[] } = { labels: [], datasets: [] };

  onMount(async () => {
    await loadData();
    setDefaultDateRange();
    await generateRevenueReport();
  });

  async function loadData() {
    isLoading = true;
    try {
      await Promise.all([
        loadInvoices(),
        contactStore.loadContacts()
      ]);
    } catch (error) {
      console.error('Error loading data:', error);
      addToast({
        message: 'Failed to load report data',
        type: 'error'
      });
    } finally {
      isLoading = false;
    }
  }

  async function loadInvoices() {
    try {
      invoices = await getInvoices();
    } catch (error) {
      console.error('Error loading invoices:', error);
      throw error;
    }
  }

  function setDefaultDateRange() {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    endDate = today.toISOString().split('T')[0];
    startDate = thirtyDaysAgo.toISOString().split('T')[0];
  }

  async function generateRevenueReport() {
    if (!startDate || !endDate) {
      addToast({
        message: 'Please select a valid date range',
        type: 'error'
      });
      return;
    }

    isGeneratingReport = true;

    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      // Filter invoices by date range
      const filteredInvoices = invoices.filter(invoice => {
        const invoiceDate = new Date(invoice.issueDate);
        return invoiceDate >= start && invoiceDate <= end;
      });

      // Group invoices by date
      const revenueMap = new Map<string, RevenueData>();
      
      // Initialize all dates in range with zero values
      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().split('T')[0];
        revenueMap.set(dateStr, {
          date: dateStr,
          totalInvoiced: 0,
          totalPaid: 0,
          invoiceCount: 0,
          paidCount: 0
        });
      }

      // Aggregate invoice data
      filteredInvoices.forEach(invoice => {
        const dateStr = invoice.issueDate.split('T')[0];
        const existing = revenueMap.get(dateStr);
        
        if (existing) {
          const invoiceTotal = invoice.invoiceLines.reduce((sum, line) => sum + line.total, 0);
          existing.totalInvoiced += invoiceTotal;
          existing.invoiceCount += 1;
          
          if (getStatusDisplay(invoice.status).name === 'Paid') {
            existing.totalPaid += invoiceTotal; // For now, assume full payment
            existing.paidCount += 1;
          }
        }
      });

      revenueData = Array.from(revenueMap.values()).sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      // Prepare chart data
      chartData = {
        labels: revenueData.map(d => new Date(d.date).toLocaleDateString()),
        datasets: [
          {
            label: 'Total Invoiced',
            data: revenueData.map(d => d.totalInvoiced),
            borderColor: '#3b82f6',
            backgroundColor: '#3b82f620',
            tension: 0.1
          },
          {
            label: 'Total Paid',
            data: revenueData.map(d => d.totalPaid),
            borderColor: '#10b981',
            backgroundColor: '#10b98120',
            tension: 0.1
          }
        ]
      };

      addToast({
        message: 'Revenue report generated successfully',
        type: 'success'
      });

    } catch (error) {
      console.error('Error generating report:', error);
      addToast({
        message: 'Failed to generate revenue report',
        type: 'error'
      });
    } finally {
      isGeneratingReport = false;
    }
  }

  async function exportCustomersCSV() {
    isExportingCSV = true;

    try {
      const customerData = $customers.map(customer => ({
        'Customer ID': customer.id,
        'Full Name': customer.fullName,
        'Company Name': customer.companyName || '',
        'Primary Email': customer.emails.find(e => e.isPrimary)?.email || '',
        'Primary Phone': customer.phones.find(p => p.isPrimary)?.phone || '',
        'Status': customer.status,
        'Primary Address': (() => {
          const addr = customer.addresses.find(a => a.isPrimary);
          return addr ? `${addr.street}, ${addr.city}, ${addr.state} ${addr.zipCode}` : '';
        })(),
        'Created Date': new Date(customer.createdAt).toLocaleDateString(),
        'Total Invoices': invoices.filter(inv => {
          // Note: ApiInvoice doesn't have customerId, so this is a placeholder
          return false; // Return 0 for now since we can't match customers
        }).length,
        'Total Revenue': formatCurrency(
          invoices
            .filter(inv => {
              // Note: ApiInvoice doesn't have customerId, so this is a placeholder
              return false; // Return 0 for now since we can't match customers
            })
            .reduce((sum, inv) => sum + inv.invoiceLines.reduce((lineSum, line) => lineSum + line.total, 0), 0)
        )
      }));

      downloadCSV(customerData, 'customers-export');

      addToast({
        message: 'Customer data exported successfully',
        type: 'success'
      });

    } catch (error) {
      console.error('Error exporting CSV:', error);
      addToast({
        message: 'Failed to export customer data',
        type: 'error'
      });
    } finally {
      isExportingCSV = false;
    }
  }

  async function exportRevenueCSV() {
    if (revenueData.length === 0) {
      addToast({
        message: 'Please generate a revenue report first',
        type: 'error'
      });
      return;
    }

    isExportingCSV = true;

    try {
      const csvData = revenueData.map(data => ({
        'Date': new Date(data.date).toLocaleDateString(),
        'Total Invoiced': formatCurrency(data.totalInvoiced),
        'Total Paid': formatCurrency(data.totalPaid),
        'Invoice Count': data.invoiceCount,
        'Paid Count': data.paidCount,
        'Outstanding': formatCurrency(data.totalInvoiced - data.totalPaid)
      }));

      downloadCSV(csvData, 'revenue-report');

      addToast({
        message: 'Revenue report exported successfully',
        type: 'success'
      });

    } catch (error) {
      console.error('Error exporting revenue CSV:', error);
      addToast({
        message: 'Failed to export revenue report',
        type: 'error'
      });
    } finally {
      isExportingCSV = false;
    }
  }

  function downloadCSV(data: any[], filename: string) {
    if (data.length === 0) return;

    // Get headers from first object
    const headers = Object.keys(data[0]);
    
    // Create CSV content
    const csvContent = [
      headers.join(','), // Header row
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          // Escape commas and quotes in values
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  function handleDateRangeChange() {
    if (selectedDateRange !== 'custom') {
      const today = new Date();
      const daysAgo = new Date(today.getTime() - parseInt(selectedDateRange) * 24 * 60 * 60 * 1000);
      
      endDate = today.toISOString().split('T')[0];
      startDate = daysAgo.toISOString().split('T')[0];
    }
  }

  // Calculate summary statistics
  $: totalInvoiced = revenueData.reduce((sum, d) => sum + d.totalInvoiced, 0);
  $: totalPaid = revenueData.reduce((sum, d) => sum + d.totalPaid, 0);
  $: totalOutstanding = totalInvoiced - totalPaid;
  $: totalInvoiceCount = revenueData.reduce((sum, d) => sum + d.invoiceCount, 0);
  $: averageDailyRevenue = revenueData.length > 0 ? totalInvoiced / revenueData.length : 0;
</script>

<svelte:head>
  <title>Reports - Easy Job Planner</title>
</svelte:head>

<div class="container">
  <PageHeader title="Reports">
    <svelte:fragment slot="actions">
      <Button variant="secondary" on:click={exportCustomersCSV} disabled={isExportingCSV}>
        {#if isExportingCSV}
          Exporting...
        {:else}
          Export Customers
        {/if}
      </Button>
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if isLoading}
      <div class="loading-container">
        <LoadingSpinner />
        <p>Loading report data...</p>
      </div>
    {:else}
      <!-- Report Controls -->
      <div class="report-controls">
        <div class="control-group">
          <label for="report-type">Report Type</label>
          <select id="report-type" bind:value={selectedReportType}>
            <option value="revenue">Revenue Report</option>
            <option value="customers">Customer Export</option>
          </select>
        </div>

        {#if selectedReportType === 'revenue'}
          <div class="control-group">
            <label for="date-range">Date Range</label>
            <select id="date-range" bind:value={selectedDateRange} on:change={handleDateRangeChange}>
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
              <option value="custom">Custom range</option>
            </select>
          </div>

          {#if selectedDateRange === 'custom'}
            <div class="control-group">
              <label for="start-date">Start Date</label>
              <input id="start-date" type="date" bind:value={startDate} />
            </div>

            <div class="control-group">
              <label for="end-date">End Date</label>
              <input id="end-date" type="date" bind:value={endDate} />
            </div>
          {/if}

          <Button 
            variant="primary" 
            on:click={generateRevenueReport} 
            disabled={isGeneratingReport}
          >
            {#if isGeneratingReport}
              Generating...
            {:else}
              Generate Report
            {/if}
          </Button>
        {/if}
      </div>

      {#if selectedReportType === 'revenue' && revenueData.length > 0}
        <!-- Revenue Summary Cards -->
        <div class="summary-cards">
          <div class="summary-card">
            <h3>Total Invoiced</h3>
            <div class="amount">{formatCurrency(totalInvoiced)}</div>
            <div class="subtitle">{totalInvoiceCount} invoices</div>
          </div>

          <div class="summary-card">
            <h3>Total Paid</h3>
            <div class="amount paid">{formatCurrency(totalPaid)}</div>
            <div class="subtitle">{revenueData.reduce((sum, d) => sum + d.paidCount, 0)} payments</div>
          </div>

          <div class="summary-card">
            <h3>Outstanding</h3>
            <div class="amount outstanding">{formatCurrency(totalOutstanding)}</div>
            <div class="subtitle">{totalInvoiceCount - revenueData.reduce((sum, d) => sum + d.paidCount, 0)} unpaid</div>
          </div>

          <div class="summary-card">
            <h3>Daily Average</h3>
            <div class="amount">{formatCurrency(averageDailyRevenue)}</div>
            <div class="subtitle">{revenueData.length} days</div>
          </div>
        </div>

        <!-- Chart Placeholder -->
        <div class="chart-container">
          <h3>Revenue Trend</h3>
          <div class="chart-placeholder">
            <p>📊 Chart visualization would be displayed here</p>
            <p>In a real implementation, you would integrate with a charting library like Chart.js or D3.js</p>
            <div class="chart-data-preview">
              <h4>Chart Data Preview:</h4>
              <div class="data-points">
                {#each chartData.labels.slice(0, 5) as label, i}
                  <div class="data-point">
                    <span class="date">{label}</span>
                    <span class="invoiced">Invoiced: {formatCurrency(chartData.datasets[0].data[i])}</span>
                    <span class="paid">Paid: {formatCurrency(chartData.datasets[1].data[i])}</span>
                  </div>
                {/each}
                {#if chartData.labels.length > 5}
                  <div class="data-point">
                    <span class="more">... and {chartData.labels.length - 5} more data points</span>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        </div>

        <!-- Revenue Data Table -->
        <div class="table-section">
          <div class="table-header">
            <h3>Daily Revenue Breakdown</h3>
            <Button variant="secondary" on:click={exportRevenueCSV} disabled={isExportingCSV}>
              {#if isExportingCSV}
                Exporting...
              {:else}
                Export CSV
              {/if}
            </Button>
          </div>

          <div class="table-container">
            <table class="revenue-table">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Invoices</th>
                  <th>Total Invoiced</th>
                  <th>Paid</th>
                  <th>Total Paid</th>
                  <th>Outstanding</th>
                </tr>
              </thead>
              <tbody>
                {#each revenueData as data}
                  <tr>
                    <td>{new Date(data.date).toLocaleDateString()}</td>
                    <td>{data.invoiceCount}</td>
                    <td>{formatCurrency(data.totalInvoiced)}</td>
                    <td>{data.paidCount}</td>
                    <td>{formatCurrency(data.totalPaid)}</td>
                    <td class="outstanding">{formatCurrency(data.totalInvoiced - data.totalPaid)}</td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>
      {:else if selectedReportType === 'customers'}
        <!-- Customer Export Section -->
        <div class="export-section">
          <div class="export-card">
            <h3>Customer Data Export</h3>
            <p>Export all customer information including contact details, addresses, and revenue data to CSV format.</p>
            
            <div class="export-details">
              <h4>Export includes:</h4>
              <ul>
                <li>Customer ID and names</li>
                <li>Primary contact information</li>
                <li>Addresses and status</li>
                <li>Total invoices and revenue</li>
                <li>Account creation dates</li>
              </ul>
            </div>

            <div class="export-stats">
              <div class="stat">
                <span class="number">{$customers.length}</span>
                <span class="label">Total Customers</span>
              </div>
              <div class="stat">
                <span class="number">{$customers.filter(c => c.status === 'Customer').length}</span>
                <span class="label">Active Customers</span>
              </div>
              <div class="stat">
                <span class="number">{$customers.filter(c => c.status === 'Lead').length}</span>
                <span class="label">Leads</span>
              </div>
            </div>

            <Button 
              variant="primary" 
              on:click={exportCustomersCSV} 
              disabled={isExportingCSV}
            >
              {#if isExportingCSV}
                Exporting Customer Data...
              {:else}
                Export Customer Data to CSV
              {/if}
            </Button>
          </div>
        </div>
      {/if}
    {/if}
  </main>
</div>

<style lang="less">
  main {
    padding: 1rem 2rem;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .report-controls {
    display: flex;
    align-items: end;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: var(--br);
    box-shadow: var(--shadow);

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
    }

    .control-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      label {
        font-weight: 600;
        color: var(--black);
        font-size: 0.875rem;
      }

      select, input {
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 1rem;
        min-width: 150px;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 3px var(--primary-light);
        }
      }
    }
  }

  .summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .summary-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--br);
    box-shadow: var(--shadow);
    text-align: center;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--grey);
      font-size: 0.875rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .amount {
      font-size: 2rem;
      font-weight: 700;
      color: var(--black);
      margin-bottom: 0.5rem;

      &.paid {
        color: var(--success);
      }

      &.outstanding {
        color: var(--warning);
      }
    }

    .subtitle {
      color: var(--grey);
      font-size: 0.875rem;
    }
  }

  .chart-container {
    background: white;
    padding: 2rem;
    border-radius: var(--br);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;

    h3 {
      margin: 0 0 1.5rem 0;
      color: var(--black);
    }

    .chart-placeholder {
      background: var(--bg-light);
      border: 2px dashed var(--border);
      border-radius: var(--br);
      padding: 3rem 2rem;
      text-align: center;
      color: var(--grey);

      p {
        margin: 0.5rem 0;
        font-size: 1.125rem;
      }

      .chart-data-preview {
        margin-top: 2rem;
        text-align: left;
        background: white;
        padding: 1.5rem;
        border-radius: var(--br);
        border: 1px solid var(--border);

        h4 {
          margin: 0 0 1rem 0;
          color: var(--black);
        }

        .data-points {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }

        .data-point {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 1rem;
          padding: 0.5rem;
          background: var(--bg-light);
          border-radius: 4px;
          font-size: 0.875rem;

          .date {
            font-weight: 600;
            color: var(--black);
          }

          .invoiced {
            color: var(--primary);
          }

          .paid {
            color: var(--success);
          }

          .more {
            grid-column: 1 / -1;
            text-align: center;
            color: var(--grey);
            font-style: italic;
          }
        }
      }
    }
  }

  .table-section {
    background: white;
    border-radius: var(--br);
    box-shadow: var(--shadow);
    overflow: hidden;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      border-bottom: 1px solid var(--border);

      h3 {
        margin: 0;
        color: var(--black);
      }
    }

    .table-container {
      overflow-x: auto;
    }

    .revenue-table {
      width: 100%;
      border-collapse: collapse;

      th, td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid var(--border);
      }

      th {
        background: var(--bg-light);
        font-weight: 600;
        color: var(--black);
        font-size: 0.875rem;
      }

      td {
        color: var(--grey);

        &.outstanding {
          color: var(--warning);
          font-weight: 600;
        }
      }

      tr:last-child td {
        border-bottom: none;
      }

      tr:hover {
        background: var(--bg-light);
      }
    }
  }

  .export-section {
    display: flex;
    justify-content: center;
    padding: 2rem 0;
  }

  .export-card {
    background: white;
    padding: 2rem;
    border-radius: var(--br);
    box-shadow: var(--shadow);
    max-width: 600px;
    width: 100%;
    text-align: center;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
      font-size: 1.5rem;
    }

    p {
      color: var(--grey);
      line-height: 1.6;
      margin-bottom: 2rem;
    }

    .export-details {
      text-align: left;
      background: var(--bg-light);
      padding: 1.5rem;
      border-radius: var(--br);
      margin-bottom: 2rem;

      h4 {
        margin: 0 0 1rem 0;
        color: var(--black);
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;
        color: var(--grey);
        line-height: 1.6;

        li {
          margin-bottom: 0.5rem;
        }
      }
    }

    .export-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
      margin-bottom: 2rem;

      .stat {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1rem;
        background: var(--bg-light);
        border-radius: var(--br);

        .number {
          font-size: 2rem;
          font-weight: 700;
          color: var(--primary);
        }

        .label {
          font-size: 0.875rem;
          color: var(--grey);
          margin-top: 0.5rem;
        }
      }
    }
  }
</style> 