<script lang="ts">
  import { onMount } from 'svelte';
  import { 
    getJobTypes, 
    createJobType, 
    updateJobType, 
    deleteJobType,
    getResources,
    type JobType, 
    type Resource,
    type DefaultResource,
    type CustomField
  } from '$lib/api/jobs';
  import Button from '$lib/components/Button.svelte';
  import Modal from '$lib/components/Modal.svelte';
  import ConfirmDelete from '$lib/components/ConfirmDelete.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';

  let jobTypes: JobType[] = [];
  let resources: Resource[] = [];
  let showCreateModal = false;
  let showDeleteModal = false;
  let editingJobType: JobType | null = null;
  let jobTypeToDelete: JobType | null = null;
  let loading = false;

  // Form data
  let formData = {
    name: '',
    description: '',
    pricingModel: 'hourly' as 'hourly' | 'fixed' | 'per_unit',
    defaultDuration: 120,
    defaultFixedPrice: 0,
    defaultHourlyRate: 35,
    defaultUnitPrice: 0,
    category: '',
    requiredSkills: [] as string[],
    defaultResources: [] as DefaultResource[],
    defaultFields: [] as CustomField[]
  };

  let newSkill = '';
  let newFieldKey = '';
  let newFieldType: 'text' | 'number' | 'date' | 'boolean' | 'select' = 'text';

  onMount(async () => {
    await loadData();
  });

  async function loadData() {
    loading = true;
    try {
      [jobTypes, resources] = await Promise.all([
        getJobTypes(),
        getResources()
      ]);
    } catch (error) {
      console.error('Error loading data:', error);
      addToast({ type: 'error', message: 'Error loading job types' });
    } finally {
      loading = false;
    }
  }

  function openCreateModal() {
    resetForm();
    showCreateModal = true;
    editingJobType = null;
  }

  function openEditModal(jobType: JobType) {
    formData = {
      name: jobType.name,
      description: jobType.description || '',
      pricingModel: jobType.pricingModel,
      defaultDuration: jobType.defaultDuration || 120,
      defaultFixedPrice: jobType.defaultFixedPrice || 0,
      defaultHourlyRate: jobType.defaultHourlyRate || 35,
      defaultUnitPrice: jobType.defaultUnitPrice || 0,
      category: jobType.category || '',
      requiredSkills: [...jobType.requiredSkills],
      defaultResources: [...jobType.defaultResources],
      defaultFields: [...jobType.defaultFields]
    };
    editingJobType = jobType;
    showCreateModal = true;
  }

  function resetForm() {
    formData = {
      name: '',
      description: '',
      pricingModel: 'hourly',
      defaultDuration: 120,
      defaultFixedPrice: 0,
      defaultHourlyRate: 35,
      defaultUnitPrice: 0,
      category: '',
      requiredSkills: [],
      defaultResources: [],
      defaultFields: []
    };
    newSkill = '';
    newFieldKey = '';
    newFieldType = 'text';
  }

  async function handleSubmit() {
    loading = true;
    try {
      const jobTypeData = {
        ...formData,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      if (editingJobType) {
        await updateJobType(editingJobType.id, jobTypeData);
        addToast({ type: 'success', message: 'Job type updated successfully' });
      } else {
        await createJobType(jobTypeData);
        addToast({ type: 'success', message: 'Job type created successfully' });
      }

      await loadData();
      showCreateModal = false;
      resetForm();
    } catch (error) {
      console.error('Error saving job type:', error);
      addToast({ type: 'error', message: 'Error saving job type' });
    } finally {
      loading = false;
    }
  }

  function initiateDelete(jobType: JobType) {
    jobTypeToDelete = jobType;
    showDeleteModal = true;
  }

  async function confirmDelete() {
    if (jobTypeToDelete) {
      loading = true;
      try {
        await deleteJobType(jobTypeToDelete.id);
        await loadData();
        addToast({ type: 'success', message: 'Job type deleted successfully' });
      } catch (error) {
        console.error('Error deleting job type:', error);
        addToast({ type: 'error', message: 'Error deleting job type' });
      } finally {
        loading = false;
        showDeleteModal = false;
        jobTypeToDelete = null;
      }
    }
  }

  function addSkill() {
    if (newSkill.trim() && !formData.requiredSkills.includes(newSkill.trim())) {
      formData.requiredSkills = [...formData.requiredSkills, newSkill.trim()];
      newSkill = '';
    }
  }

  function removeSkill(skill: string) {
    formData.requiredSkills = formData.requiredSkills.filter(s => s !== skill);
  }

  function addDefaultField() {
    if (newFieldKey.trim()) {
      const newField: CustomField = {
        id: Date.now().toString(),
        key: newFieldKey.trim(),
        value: '',
        type: newFieldType,
        options: newFieldType === 'select' ? [] : undefined
      };
      formData.defaultFields = [...formData.defaultFields, newField];
      newFieldKey = '';
      newFieldType = 'text';
    }
  }

  function removeDefaultField(fieldId: string) {
    formData.defaultFields = formData.defaultFields.filter(f => f.id !== fieldId);
  }

  function addDefaultResource(resourceId: string) {
    const resource = resources.find(r => r.id === resourceId);
    if (resource && !formData.defaultResources.some(dr => dr.resourceId === resourceId)) {
      const defaultResource: DefaultResource = {
        resourceId: resource.id,
        resourceName: resource.name,
        resourceType: resource.type,
        quantity: 1,
        isRequired: true
      };
      formData.defaultResources = [...formData.defaultResources, defaultResource];
    }
  }

  function removeDefaultResource(resourceId: string) {
    formData.defaultResources = formData.defaultResources.filter(dr => dr.resourceId !== resourceId);
  }

  function updateResourceQuantity(resourceId: string, quantity: number) {
    formData.defaultResources = formData.defaultResources.map(dr => 
      dr.resourceId === resourceId ? { ...dr, quantity } : dr
    );
  }

  function toggleResourceRequired(resourceId: string) {
    formData.defaultResources = formData.defaultResources.map(dr => 
      dr.resourceId === resourceId ? { ...dr, isRequired: !dr.isRequired } : dr
    );
  }

  function getPricingDisplay(jobType: JobType): string {
    switch (jobType.pricingModel) {
      case 'hourly':
        return `$${jobType.defaultHourlyRate || 0}/hour`;
      case 'fixed':
        return `$${jobType.defaultFixedPrice || 0} fixed`;
      case 'per_unit':
        return `$${jobType.defaultUnitPrice || 0}/unit`;
      default:
        return 'Not set';
    }
  }
</script>

<PageHeader title="Job Types">
  <div slot="actions">
    <Button variant="primary" on:click={openCreateModal}>
      Add Job Type
    </Button>
  </div>
</PageHeader>

{#if loading}
  <LoadingSpinner />
{:else}
  <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
    {#each jobTypes as jobType}
      <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">{jobType.name}</h3>
            <p class="text-sm text-gray-600">{jobType.description || 'No description'}</p>
          </div>
          <div class="flex gap-2">
            <Button variant="secondary" size="small" on:click={() => openEditModal(jobType)}>
              Edit
            </Button>
            <Button variant="danger" size="small" on:click={() => initiateDelete(jobType)}>
              Delete
            </Button>
          </div>
        </div>

        <div class="space-y-3">
          <!-- Pricing Information -->
          <div class="flex items-center gap-2">
            <span class="font-medium">{getPricingDisplay(jobType)}</span>
            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
              {jobType.pricingModel}
            </span>
          </div>

          <!-- Duration -->
          {#if jobType.defaultDuration}
            <div class="text-sm text-gray-600">
              Duration: {Math.floor(jobType.defaultDuration / 60)}h {jobType.defaultDuration % 60}m
            </div>
          {/if}

          <!-- Category -->
          {#if jobType.category}
            <div class="text-sm">
              <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                {jobType.category}
              </span>
            </div>
          {/if}

          <!-- Required Skills -->
          {#if jobType.requiredSkills.length > 0}
            <div>
              <p class="text-sm font-medium mb-2">Required Skills:</p>
              <div class="flex flex-wrap gap-1">
                {#each jobType.requiredSkills as skill}
                  <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    {skill}
                  </span>
                {/each}
              </div>
            </div>
          {/if}

          <!-- Default Resources -->
          {#if jobType.defaultResources.length > 0}
            <div>
              <p class="text-sm font-medium mb-2">Default Resources:</p>
              <div class="space-y-1">
                {#each jobType.defaultResources as resource}
                  <div class="flex justify-between items-center text-sm">
                    <span>{resource.resourceName}</span>
                    <span class="text-gray-500">×{resource.quantity}</span>
                  </div>
                {/each}
              </div>
            </div>
          {/if}
        </div>
      </div>
    {/each}
  </div>
{/if}

<!-- Create/Edit Modal -->
<Modal bind:show={showCreateModal} title={editingJobType ? 'Edit Job Type' : 'Create Job Type'}>
  <form on:submit|preventDefault={handleSubmit} class="space-y-6">
    <!-- Basic Information -->
    <div class="grid gap-4 md:grid-cols-2">
      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
        <input 
          id="name" 
          type="text" 
          bind:value={formData.name} 
          required 
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div>
        <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
        <input 
          id="category" 
          type="text" 
          bind:value={formData.category} 
          placeholder="e.g., Residential, Commercial"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>

    <div>
      <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
      <textarea 
        id="description" 
        bind:value={formData.description}
        rows="3"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      ></textarea>
    </div>

    <!-- Pricing Model -->
    <div>
      <label for="pricingModel" class="block text-sm font-medium text-gray-700 mb-1">Pricing Model *</label>
      <select 
        id="pricingModel" 
        bind:value={formData.pricingModel}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="hourly">Hourly Rate</option>
        <option value="fixed">Fixed Price</option>
        <option value="per_unit">Per Unit</option>
      </select>
    </div>

    <!-- Pricing Details -->
    <div class="grid gap-4 md:grid-cols-2">
      <div>
        <label for="duration" class="block text-sm font-medium text-gray-700 mb-1">Default Duration (minutes)</label>
        <input 
          id="duration" 
          type="number" 
          bind:value={formData.defaultDuration}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      
      {#if formData.pricingModel === 'hourly'}
        <div>
          <label for="hourlyRate" class="block text-sm font-medium text-gray-700 mb-1">Default Hourly Rate ($)</label>
          <input 
            id="hourlyRate" 
            type="number" 
            step="0.01" 
            bind:value={formData.defaultHourlyRate}
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      {:else if formData.pricingModel === 'fixed'}
        <div>
          <label for="fixedPrice" class="block text-sm font-medium text-gray-700 mb-1">Fixed Price ($)</label>
          <input 
            id="fixedPrice" 
            type="number" 
            step="0.01" 
            bind:value={formData.defaultFixedPrice}
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      {:else if formData.pricingModel === 'per_unit'}
        <div>
          <label for="unitPrice" class="block text-sm font-medium text-gray-700 mb-1">Price Per Unit ($)</label>
          <input 
            id="unitPrice" 
            type="number" 
            step="0.01" 
            bind:value={formData.defaultUnitPrice}
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      {/if}
    </div>

    <!-- Required Skills -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">Required Skills</label>
      <div class="flex gap-2 mb-2">
        <input 
          bind:value={newSkill} 
          placeholder="Add skill" 
          class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <Button type="button" on:click={addSkill}>Add</Button>
      </div>
      <div class="flex flex-wrap gap-2">
        {#each formData.requiredSkills as skill}
          <span class="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-sm rounded-full">
            {skill}
            <button type="button" on:click={() => removeSkill(skill)} class="ml-1 hover:text-red-500">
              ×
            </button>
          </span>
        {/each}
      </div>
    </div>

    <!-- Default Resources -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">Default Resources</label>
      <div class="mb-2">
        <select 
          on:change={(e) => e.target.value && addDefaultResource(e.target.value)}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Add resource</option>
          {#each resources.filter(r => !formData.defaultResources.some(dr => dr.resourceId === r.id)) as resource}
            <option value={resource.id}>{resource.name} ({resource.type})</option>
          {/each}
        </select>
      </div>
      <div class="space-y-2">
        {#each formData.defaultResources as resource}
          <div class="flex items-center gap-2 p-2 border rounded">
            <span class="flex-1">{resource.resourceName}</span>
            <input 
              type="number" 
              min="1" 
              value={resource.quantity}
              on:input={(e) => updateResourceQuantity(resource.resourceId, parseInt(e.target.value) || 1)}
              class="w-20 px-2 py-1 border border-gray-300 rounded"
            />
            <label class="flex items-center gap-1">
              <input 
                type="checkbox" 
                checked={resource.isRequired}
                on:change={() => toggleResourceRequired(resource.resourceId)}
              />
              Required
            </label>
            <Button 
              type="button" 
              variant="danger" 
              size="small" 
              on:click={() => removeDefaultResource(resource.resourceId)}
            >
              Delete
            </Button>
          </div>
        {/each}
      </div>
    </div>

    <!-- Default Fields -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">Default Custom Fields</label>
      <div class="flex gap-2 mb-2">
        <input 
          bind:value={newFieldKey} 
          placeholder="Field name" 
          class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <select 
          bind:value={newFieldType}
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="text">Text</option>
          <option value="number">Number</option>
          <option value="date">Date</option>
          <option value="boolean">Boolean</option>
          <option value="select">Select</option>
        </select>
        <Button type="button" on:click={addDefaultField}>Add</Button>
      </div>
      <div class="space-y-2">
        {#each formData.defaultFields as field}
          <div class="flex items-center gap-2 p-2 border rounded">
            <span class="flex-1">{field.key}</span>
            <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">{field.type}</span>
            <Button 
              type="button" 
              variant="danger" 
              size="small" 
              on:click={() => removeDefaultField(field.id)}
            >
              Delete
            </Button>
          </div>
        {/each}
      </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-end gap-2">
      <Button type="button" variant="secondary" on:click={() => showCreateModal = false}>
        Cancel
      </Button>
      <Button type="submit" variant="primary" disabled={loading}>
        {loading ? 'Saving...' : editingJobType ? 'Update' : 'Create'}
      </Button>
    </div>
  </form>
</Modal>

<!-- Delete Confirmation Modal -->
<ConfirmDelete
  bind:show={showDeleteModal}
  title="Delete Job Type"
  message="Are you sure you want to delete this job type? This action cannot be undone."
  on:confirm={confirmDelete}
  on:cancel={() => showDeleteModal = false}
/> 