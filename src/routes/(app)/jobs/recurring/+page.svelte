<script lang="ts">
  import { onMount } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import Modal from '$lib/components/Modal.svelte';
  import ConfirmDelete from '$lib/components/ConfirmDelete.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import CustomerSelect from '$lib/components/CustomerSelect.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { customers } from '$lib/stores/customerStore';
  import { staff } from '$lib/stores/staffStore';
  import {
    createRecurringJob,
    generateRecurringJobInstances,
    getJobTypes,
    getResources,
    type RecurringJobTemplate,
    type RecurrencePattern,
    type JobType,
    type Resource,
    type AssignedStaff,
    type JobResource,
    type CustomField,
    type JobAddress
  } from '$lib/api/jobs';

  let recurringJobs: RecurringJobTemplate[] = [];
  let jobTypes: JobType[] = [];
  let resources: Resource[] = [];
  let showCreateModal = false;
  let showDeleteModal = false;
  let editingTemplate: RecurringJobTemplate | null = null;
  let templateToDelete: RecurringJobTemplate | null = null;
  let loading = false;

  // Form data
  let formData = {
    name: '',
    jobTypeId: '',
    customerId: '',
    customerSearch: '',
    description: '',
    assignedStaffIds: [] as string[],
    jobAddress: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US'
    } as JobAddress,
    customFields: [] as CustomField[],
    resourceIds: [] as string[],
    recurrencePattern: {
      type: 'weekly' as 'daily' | 'weekly' | 'monthly' | 'yearly',
      interval: 1,
      daysOfWeek: [1], // Monday by default
      dayOfMonth: 1,
      endDate: '',
      maxOccurrences: undefined
    } as RecurrencePattern
  };

  let errors: Record<string, string> = {};
  let formSubmitted = false;

  onMount(async () => {
    await loadData();
  });

  async function loadData() {
    loading = true;
    try {
      [jobTypes, resources] = await Promise.all([
        getJobTypes(),
        getResources()
      ]);
      
      // Load recurring jobs from localStorage for now
      const stored = localStorage.getItem('ejp_recurring_jobs');
      recurringJobs = stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error loading data:', error);
      addToast({ type: 'error', message: 'Error loading data' });
    } finally {
      loading = false;
    }
  }

  function openCreateModal() {
    resetForm();
    showCreateModal = true;
    editingTemplate = null;
  }

  function openEditModal(template: RecurringJobTemplate) {
    formData = {
      name: template.name,
      jobTypeId: template.jobTypeId,
      customerId: template.customerId,
      customerSearch: getCustomerName(template.customerId),
      description: template.description,
      assignedStaffIds: template.assignedStaff.map(s => s.staffId),
      jobAddress: { ...template.jobAddress },
      customFields: [...template.customFields],
      resourceIds: template.resources.map(r => r.resourceId),
      recurrencePattern: { ...template.recurrencePattern }
    };
    editingTemplate = template;
    showCreateModal = true;
  }

  function resetForm() {
    formData = {
      name: '',
      jobTypeId: '',
      customerId: '',
      customerSearch: '',
      description: '',
      assignedStaffIds: [],
      jobAddress: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'US'
      },
      customFields: [],
      resourceIds: [],
      recurrencePattern: {
        type: 'weekly',
        interval: 1,
        daysOfWeek: [1],
        dayOfMonth: 1,
        endDate: '',
        maxOccurrences: undefined
      }
    };
    errors = {};
    formSubmitted = false;
  }

  function validateForm(): boolean {
    errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Template name is required';
    }

    if (!formData.jobTypeId) {
      errors.jobTypeId = 'Job type is required';
    }

    if (!formData.customerId) {
      errors.customerId = 'Customer is required';
    }

    if (!formData.jobAddress.street.trim()) {
      errors.street = 'Street address is required';
    }

    if (!formData.jobAddress.city.trim()) {
      errors.city = 'City is required';
    }

    if (formData.recurrencePattern.interval < 1) {
      errors.interval = 'Interval must be at least 1';
    }

    if (formData.recurrencePattern.type === 'weekly' && formData.recurrencePattern.daysOfWeek?.length === 0) {
      errors.daysOfWeek = 'At least one day must be selected for weekly recurrence';
    }

    return Object.keys(errors).length === 0;
  }

  async function handleSubmit() {
    formSubmitted = true;
    
    if (!validateForm()) {
      addToast({ type: 'error', message: 'Please fix the errors in the form' });
      return;
    }

    loading = true;
    try {
      // Convert form data to template format
      const assignedStaff: AssignedStaff[] = formData.assignedStaffIds.map(staffId => {
        const staffMember = $staff.find(s => s.id === staffId);
        return {
          staffId,
          staffName: staffMember?.fullName || 'Unknown',
          hourlyRate: staffMember?.wageInfo?.rate || 0,
          estimatedHours: 2, // Default 2 hours
          role: staffMember?.position || ''
        };
      });

      const jobResources: JobResource[] = formData.resourceIds.map(resourceId => {
        const resource = resources.find(r => r.id === resourceId);
        return {
          id: Date.now().toString() + Math.random().toString(36).substr(2),
          resourceId,
          resourceName: resource?.name || 'Unknown',
          resourceType: resource?.type || 'Equipment',
          quantity: 1,
          costPerUnit: resource?.costPerUnit || 0,
          totalCost: resource?.costPerUnit || 0
        };
      });

      const templateData = {
        name: formData.name,
        jobTypeId: formData.jobTypeId,
        customerId: formData.customerId,
        description: formData.description,
        assignedStaff,
        jobAddress: formData.jobAddress,
        customFields: formData.customFields,
        resources: jobResources,
        recurrencePattern: formData.recurrencePattern,
        isActive: true
      };

      if (editingTemplate) {
        // Update existing template
        const updatedTemplate = {
          ...editingTemplate,
          ...templateData,
          updatedAt: new Date().toISOString()
        };
        
        recurringJobs = recurringJobs.map(t => 
          t.id === editingTemplate.id ? updatedTemplate : t
        );
        
        localStorage.setItem('ejp_recurring_jobs', JSON.stringify(recurringJobs));
        addToast({ type: 'success', message: 'Recurring job template updated successfully' });
      } else {
        // Create new template
        const newTemplate = await createRecurringJob(templateData);
        recurringJobs = [...recurringJobs, newTemplate];
        addToast({ type: 'success', message: 'Recurring job template created successfully' });
      }

      showCreateModal = false;
      resetForm();
    } catch (error) {
      console.error('Error saving template:', error);
      addToast({ type: 'error', message: 'Error saving recurring job template' });
    } finally {
      loading = false;
    }
  }

  function initiateDelete(template: RecurringJobTemplate) {
    templateToDelete = template;
    showDeleteModal = true;
  }

  async function confirmDelete() {
    if (templateToDelete) {
      try {
        recurringJobs = recurringJobs.filter(t => t.id !== templateToDelete.id);
        localStorage.setItem('ejp_recurring_jobs', JSON.stringify(recurringJobs));
        addToast({ type: 'success', message: 'Recurring job template deleted successfully' });
      } catch (error) {
        console.error('Error deleting template:', error);
        addToast({ type: 'error', message: 'Error deleting recurring job template' });
      } finally {
        showDeleteModal = false;
        templateToDelete = null;
      }
    }
  }

  async function generateJobs(template: RecurringJobTemplate) {
    try {
      const startDate = new Date().toISOString().split('T')[0];
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + 3); // Generate 3 months ahead
      
      const instances = await generateRecurringJobInstances(
        template.id,
        startDate,
        endDate.toISOString().split('T')[0]
      );
      
      addToast({ 
        type: 'success', 
        message: `Generated ${instances.length} job instances for the next 3 months` 
      });
    } catch (error) {
      console.error('Error generating jobs:', error);
      addToast({ type: 'error', message: 'Error generating job instances' });
    }
  }

  function handleCustomerSelect(event: CustomEvent<string>) {
    formData.customerId = event.detail;
    const customer = $customers.find(c => c.id === event.detail);
    if (customer) {
      formData.customerSearch = customer.companyName || customer.fullName;
    }
  }

  function getCustomerName(customerId: string): string {
    const customer = $customers.find(c => c.id === customerId);
    return customer ? (customer.companyName || customer.fullName) : 'Unknown Customer';
  }

  function getJobTypeName(jobTypeId: string): string {
    const jobType = jobTypes.find(jt => jt.id === jobTypeId);
    return jobType ? jobType.name : 'Unknown Job Type';
  }

  function formatRecurrencePattern(pattern: RecurrencePattern): string {
    const { type, interval, daysOfWeek, dayOfMonth } = pattern;
    
    switch (type) {
      case 'daily':
        return interval === 1 ? 'Daily' : `Every ${interval} days`;
      case 'weekly':
        const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        const selectedDays = daysOfWeek?.map(d => dayNames[d]).join(', ') || '';
        return interval === 1 
          ? `Weekly on ${selectedDays}` 
          : `Every ${interval} weeks on ${selectedDays}`;
      case 'monthly':
        return interval === 1 
          ? `Monthly on day ${dayOfMonth}` 
          : `Every ${interval} months on day ${dayOfMonth}`;
      case 'yearly':
        return interval === 1 ? 'Yearly' : `Every ${interval} years`;
      default:
        return 'Unknown pattern';
    }
  }

  function toggleDayOfWeek(day: number) {
    const currentDays = formData.recurrencePattern.daysOfWeek || [];
    if (currentDays.includes(day)) {
      formData.recurrencePattern.daysOfWeek = currentDays.filter(d => d !== day);
    } else {
      formData.recurrencePattern.daysOfWeek = [...currentDays, day].sort();
    }
  }
</script>

<PageHeader title="Recurring Jobs">
  <div slot="actions">
    <Button variant="primary" on:click={openCreateModal}>
      Create Recurring Job
    </Button>
  </div>
</PageHeader>

{#if loading}
  <LoadingSpinner />
{:else}
  <div class="recurring-jobs-grid">
    {#each recurringJobs as template}
      <div class="template-card">
        <div class="card-header">
          <h3>{template.name}</h3>
          <div class="card-actions">
            <Button variant="secondary" size="small" on:click={() => openEditModal(template)}>
              Edit
            </Button>
            <Button variant="primary" size="small" on:click={() => generateJobs(template)}>
              Generate Jobs
            </Button>
            <Button variant="danger" size="small" on:click={() => initiateDelete(template)}>
              Delete
            </Button>
          </div>
        </div>

        <div class="card-content">
          <div class="template-info">
            <p><strong>Job Type:</strong> {getJobTypeName(template.jobTypeId)}</p>
            <p><strong>Customer:</strong> {getCustomerName(template.customerId)}</p>
            <p><strong>Recurrence:</strong> {formatRecurrencePattern(template.recurrencePattern)}</p>
            
            {#if template.description}
              <p><strong>Description:</strong> {template.description}</p>
            {/if}

            {#if template.assignedStaff.length > 0}
              <p><strong>Assigned Staff:</strong> {template.assignedStaff.map(s => s.staffName).join(', ')}</p>
            {/if}

            {#if template.resources.length > 0}
              <p><strong>Resources:</strong> {template.resources.map(r => r.resourceName).join(', ')}</p>
            {/if}

            <div class="template-status">
              <span class="status-badge" class:active={template.isActive}>
                {template.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>
      </div>
    {/each}

    {#if recurringJobs.length === 0}
      <div class="empty-state">
        <h3>No Recurring Jobs</h3>
        <p>Create your first recurring job template to automate job scheduling.</p>
        <Button variant="primary" on:click={openCreateModal}>
          Create Recurring Job
        </Button>
      </div>
    {/if}
  </div>
{/if}

<!-- Create/Edit Modal -->
<Modal bind:show={showCreateModal} title={editingTemplate ? 'Edit Recurring Job' : 'Create Recurring Job'}>
  <form on:submit|preventDefault={handleSubmit} class="recurring-form">
    <!-- Basic Information -->
    <div class="form-section">
      <h4>Basic Information</h4>
      
      <div class="form-group">
        <label for="name">Template Name *</label>
        <input 
          id="name" 
          type="text" 
          bind:value={formData.name} 
          class:error={formSubmitted && errors.name}
          placeholder="e.g., Weekly Office Cleaning"
        />
        {#if formSubmitted && errors.name}
          <div class="error-message">{errors.name}</div>
        {/if}
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="jobType">Job Type *</label>
          <select 
            id="jobType" 
            bind:value={formData.jobTypeId}
            class:error={formSubmitted && errors.jobTypeId}
          >
            <option value="">Select job type</option>
            {#each jobTypes as jobType}
              <option value={jobType.id}>{jobType.name}</option>
            {/each}
          </select>
          {#if formSubmitted && errors.jobTypeId}
            <div class="error-message">{errors.jobTypeId}</div>
          {/if}
        </div>

        <div class="form-group">
          <CustomerSelect
            customerId={formData.customerId}
            customerSearch={formData.customerSearch}
            hasError={formSubmitted && !!errors.customerId}
            errorMessage={errors.customerId}
            on:selectcustomer={handleCustomerSelect}
          />
        </div>
      </div>

      <div class="form-group">
        <label for="description">Description</label>
        <textarea 
          id="description" 
          bind:value={formData.description}
          rows="3"
          placeholder="Job description"
        ></textarea>
      </div>
    </div>

    <!-- Job Address -->
    <div class="form-section">
      <h4>Job Address</h4>
      
      <div class="form-group">
        <label for="street">Street Address *</label>
        <input 
          id="street" 
          type="text" 
          bind:value={formData.jobAddress.street}
          class:error={formSubmitted && errors.street}
          placeholder="123 Main St"
        />
        {#if formSubmitted && errors.street}
          <div class="error-message">{errors.street}</div>
        {/if}
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="city">City *</label>
          <input 
            id="city" 
            type="text" 
            bind:value={formData.jobAddress.city}
            class:error={formSubmitted && errors.city}
            placeholder="City"
          />
          {#if formSubmitted && errors.city}
            <div class="error-message">{errors.city}</div>
          {/if}
        </div>

        <div class="form-group">
          <label for="state">State</label>
          <input 
            id="state" 
            type="text" 
            bind:value={formData.jobAddress.state}
            placeholder="State"
          />
        </div>

        <div class="form-group">
          <label for="zipCode">ZIP Code</label>
          <input 
            id="zipCode" 
            type="text" 
            bind:value={formData.jobAddress.zipCode}
            placeholder="ZIP"
          />
        </div>
      </div>
    </div>

    <!-- Staff Assignment -->
    <div class="form-section">
      <h4>Assigned Staff</h4>
      <div class="staff-checkboxes">
        {#each $staff.filter(s => s.isActive) as staffMember}
          <label class="checkbox-label">
            <input
              type="checkbox"
              bind:group={formData.assignedStaffIds}
              value={staffMember.id}
            />
            <span class="checkbox-custom"></span>
            {staffMember.fullName}
          </label>
        {/each}
      </div>
    </div>

    <!-- Resources -->
    <div class="form-section">
      <h4>Resources</h4>
      <div class="resource-checkboxes">
        {#each resources as resource}
          <label class="checkbox-label">
            <input
              type="checkbox"
              bind:group={formData.resourceIds}
              value={resource.id}
            />
            <span class="checkbox-custom"></span>
            {resource.name} ({resource.type})
          </label>
        {/each}
      </div>
    </div>

    <!-- Recurrence Pattern -->
    <div class="form-section">
      <h4>Recurrence Pattern</h4>
      
      <div class="form-row">
        <div class="form-group">
          <label for="recurrenceType">Frequency *</label>
          <select id="recurrenceType" bind:value={formData.recurrencePattern.type}>
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
          </select>
        </div>

        <div class="form-group">
          <label for="interval">Every</label>
          <input 
            id="interval" 
            type="number" 
            min="1" 
            bind:value={formData.recurrencePattern.interval}
            class:error={formSubmitted && errors.interval}
          />
          <span class="interval-label">
            {formData.recurrencePattern.type === 'daily' ? 'day(s)' : 
             formData.recurrencePattern.type === 'weekly' ? 'week(s)' :
             formData.recurrencePattern.type === 'monthly' ? 'month(s)' : 'year(s)'}
          </span>
          {#if formSubmitted && errors.interval}
            <div class="error-message">{errors.interval}</div>
          {/if}
        </div>
      </div>

      {#if formData.recurrencePattern.type === 'weekly'}
        <div class="form-group">
          <label>Days of Week *</label>
          <div class="days-of-week">
            {#each ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] as day, index}
              <label class="day-checkbox">
                <input
                  type="checkbox"
                  checked={formData.recurrencePattern.daysOfWeek?.includes(index)}
                  on:change={() => toggleDayOfWeek(index)}
                />
                <span class="day-label">{day}</span>
              </label>
            {/each}
          </div>
          {#if formSubmitted && errors.daysOfWeek}
            <div class="error-message">{errors.daysOfWeek}</div>
          {/if}
        </div>
      {/if}

      {#if formData.recurrencePattern.type === 'monthly'}
        <div class="form-group">
          <label for="dayOfMonth">Day of Month</label>
          <input 
            id="dayOfMonth" 
            type="number" 
            min="1" 
            max="31" 
            bind:value={formData.recurrencePattern.dayOfMonth}
          />
        </div>
      {/if}

      <div class="form-row">
        <div class="form-group">
          <label for="endDate">End Date (optional)</label>
          <input 
            id="endDate" 
            type="date" 
            bind:value={formData.recurrencePattern.endDate}
          />
        </div>

        <div class="form-group">
          <label for="maxOccurrences">Max Occurrences (optional)</label>
          <input 
            id="maxOccurrences" 
            type="number" 
            min="1" 
            bind:value={formData.recurrencePattern.maxOccurrences}
            placeholder="Unlimited"
          />
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="form-actions">
      <Button type="button" variant="secondary" on:click={() => showCreateModal = false}>
        Cancel
      </Button>
      <Button type="submit" variant="primary" disabled={loading}>
        {loading ? 'Saving...' : editingTemplate ? 'Update Template' : 'Create Template'}
      </Button>
    </div>
  </form>
</Modal>

<!-- Delete Confirmation Modal -->
<ConfirmDelete
  bind:showConfirm={showDeleteModal}
  title="Delete Recurring Job Template"
  message="Are you sure you want to delete this recurring job template? This action cannot be undone."
  on:confirm={confirmDelete}
  on:cancel={() => showDeleteModal = false}
/>

<style lang="less">
  .recurring-jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
  }

  .template-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      border-bottom: 1px solid var(--border);
      background: var(--bg);

      h3 {
        margin: 0;
        color: var(--primary);
        font-size: 1.125rem;
      }

      .card-actions {
        display: flex;
        gap: 0.5rem;
      }
    }

    .card-content {
      padding: 1.5rem;

      .template-info {
        p {
          margin: 0.5rem 0;
          font-size: 0.9rem;

          strong {
            color: var(--black);
          }
        }

        .template-status {
          margin-top: 1rem;

          .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            background: var(--grey-light);
            color: var(--grey);

            &.active {
              background: var(--green-light);
              color: var(--green);
            }
          }
        }
      }
    }
  }

  .empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    color: var(--grey);

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    p {
      margin: 0 0 2rem 0;
    }
  }

  .recurring-form {
    .form-section {
      margin-bottom: 2rem;
      padding-bottom: 1.5rem;
      border-bottom: 1px solid var(--border);

      &:last-of-type {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }

      h4 {
        margin: 0 0 1rem 0;
        color: var(--primary);
        font-size: 1rem;
        font-weight: 600;
      }
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }

    .staff-checkboxes,
    .resource-checkboxes {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.5rem;
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: var(--br);
      transition: background-color 0.2s ease;

      &:hover {
        background: var(--bg);
      }

      input[type="checkbox"] {
        margin: 0;
      }

      .checkbox-custom {
        // Custom checkbox styling if needed
      }
    }

    .days-of-week {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;

      .day-checkbox {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
        cursor: pointer;
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        transition: all 0.2s ease;

        &:hover {
          border-color: var(--primary);
        }

        input[type="checkbox"] {
          margin: 0;
        }

        .day-label {
          font-size: 0.8rem;
          font-weight: 500;
        }
      }
    }

    .interval-label {
      margin-left: 0.5rem;
      color: var(--grey);
      font-size: 0.9rem;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 2rem;
      padding-top: 1.5rem;
      border-top: 1px solid var(--border);
    }
  }

  @media (max-width: 768px) {
    .recurring-jobs-grid {
      grid-template-columns: 1fr;
      padding: 1rem;
    }

    .template-card .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .card-actions {
        align-self: stretch;
        justify-content: space-between;
      }
    }

    .recurring-form .form-row {
      grid-template-columns: 1fr;
    }

    .days-of-week {
      justify-content: center;
    }
  }
</style> 