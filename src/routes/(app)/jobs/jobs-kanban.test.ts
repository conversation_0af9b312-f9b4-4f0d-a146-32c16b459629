import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, fireEvent, waitFor } from '@testing-library/svelte';
import { get } from 'svelte/store';
import JobsPage from './+page.svelte';
import { jobs, jobStatuses, jobsByStatus, jobStore } from '$lib/stores/jobStore';
import type { Job, JobStatus } from '$lib/api/jobs';

// Mock the job store
vi.mock('$lib/stores/jobStore', () => ({
  jobs: { subscribe: vi.fn() },
  jobStatuses: { subscribe: vi.fn() },
  jobsByStatus: { subscribe: vi.fn() },
  jobsLoading: { subscribe: vi.fn() },
  jobStatusesLoading: { subscribe: vi.fn() },
  jobModalOpen: { subscribe: vi.fn() },
  jobStore: {
    loadJobs: vi.fn(),
    loadJobStatuses: vi.fn(),
    loadJobTypes: vi.fn(),
    updateJobStatus: vi.fn(),
    openJobModal: vi.fn()
  }
}));

// Mock svelte-dnd-action
vi.mock('svelte-dnd-action', () => ({
  dndzone: vi.fn(() => ({ destroy: vi.fn() }))
}));

// Mock toast store
vi.mock('$lib/stores/toastStore', () => ({
  addToast: vi.fn()
}));

describe('Jobs Kanban Board', () => {
  const mockJobStatuses: JobStatus[] = [
    { id: '1', name: 'Fresh', color: '#6B7280', order: 1, isCompleted: false },
    { id: '2', name: 'In Progress', color: '#3B82F6', order: 2, isCompleted: false },
    { id: '3', name: 'Completed', color: '#10B981', order: 3, isCompleted: true }
  ];

  const mockJobs: Job[] = [
    {
      id: 'job-1',
      title: 'Fix Kitchen Sink',
      customerId: 'customer-1',
      customerName: 'John Smith',
      jobType: 'Plumbing',
      status: mockJobStatuses[0],
      description: 'Kitchen sink is leaking',
      assignedStaff: [],
      jobAddress: {
        street: '123 Main St',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62701',
        country: 'USA'
      },
      customFields: [],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      priority: 'Medium',
      tags: [],
      attachments: [],
      notes: ''
    },
    {
      id: 'job-2',
      title: 'Install Light Fixture',
      customerId: 'customer-2',
      customerName: 'Jane Doe',
      jobType: 'Electrical',
      status: mockJobStatuses[1],
      description: 'Install new light fixture in living room',
      assignedStaff: [],
      jobAddress: {
        street: '456 Oak Ave',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62702',
        country: 'USA'
      },
      customFields: [],
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z',
      priority: 'High',
      tags: [],
      attachments: [],
      notes: ''
    }
  ];

  const mockJobsByStatus = {
    '1': [mockJobs[0]],
    '2': [mockJobs[1]],
    '3': []
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup store mocks
    (jobs.subscribe as any).mockImplementation((callback: any) => {
      callback(mockJobs);
      return () => {};
    });
    
    (jobStatuses.subscribe as any).mockImplementation((callback: any) => {
      callback(mockJobStatuses);
      return () => {};
    });
    
    (jobsByStatus.subscribe as any).mockImplementation((callback: any) => {
      callback(mockJobsByStatus);
      return () => {};
    });
    
    // Mock loading states
    const mockLoadingStore = { subscribe: (callback: any) => { callback(false); return () => {}; } };
    (require('$lib/stores/jobStore').jobsLoading as any) = mockLoadingStore;
    (require('$lib/stores/jobStore').jobStatusesLoading as any) = mockLoadingStore;
    (require('$lib/stores/jobStore').jobModalOpen as any) = { subscribe: (callback: any) => { callback(false); return () => {}; } };
  });

  it('should render kanban columns for each job status', async () => {
    const { getByText } = render(JobsPage);
    
    await waitFor(() => {
      expect(getByText('Fresh')).toBeInTheDocument();
      expect(getByText('In Progress')).toBeInTheDocument();
      expect(getByText('Completed')).toBeInTheDocument();
    });
  });

  it('should display jobs in correct columns', async () => {
    const { getByText } = render(JobsPage);
    
    await waitFor(() => {
      expect(getByText('Fix Kitchen Sink')).toBeInTheDocument();
      expect(getByText('Install Light Fixture')).toBeInTheDocument();
    });
  });

  it('should show job count in column headers', async () => {
    const { container } = render(JobsPage);
    
    await waitFor(() => {
      const freshColumn = container.querySelector('.kanban-column:first-child');
      expect(freshColumn?.textContent).toContain('1'); // 1 job in Fresh column
      
      const inProgressColumn = container.querySelector('.kanban-column:nth-child(2)');
      expect(inProgressColumn?.textContent).toContain('1'); // 1 job in In Progress column
      
      const completedColumn = container.querySelector('.kanban-column:last-child');
      expect(completedColumn?.textContent).toContain('0'); // 0 jobs in Completed column
    });
  });

  it('should call updateJobStatus when job is moved between columns', async () => {
    const mockUpdateJobStatus = vi.fn().mockResolvedValue(mockJobs[0]);
    (jobStore.updateJobStatus as any) = mockUpdateJobStatus;
    
    const { container } = render(JobsPage);
    
    // Simulate drag and drop finalize event
    const kanbanColumn = container.querySelector('.kanban-column-content');
    if (kanbanColumn) {
      const event = new CustomEvent('finalize', {
        detail: {
          items: [{ ...mockJobs[0], status: mockJobStatuses[1] }] // Job moved to "In Progress"
        }
      });
      
      fireEvent(kanbanColumn, event);
      
      await waitFor(() => {
        expect(mockUpdateJobStatus).toHaveBeenCalledWith('job-1', '2');
      });
    }
  });

  it('should handle drag and drop errors gracefully', async () => {
    const mockUpdateJobStatus = vi.fn().mockRejectedValue(new Error('Update failed'));
    (jobStore.updateJobStatus as any) = mockUpdateJobStatus;
    
    const mockAddToast = require('$lib/stores/toastStore').addToast;
    const mockLoadJobs = vi.fn();
    (jobStore.loadJobs as any) = mockLoadJobs;
    
    const { container } = render(JobsPage);
    
    // Simulate drag and drop finalize event that fails
    const kanbanColumn = container.querySelector('.kanban-column-content');
    if (kanbanColumn) {
      const event = new CustomEvent('finalize', {
        detail: {
          items: [{ ...mockJobs[0], status: mockJobStatuses[1] }]
        }
      });
      
      fireEvent(kanbanColumn, event);
      
      await waitFor(() => {
        expect(mockAddToast).toHaveBeenCalledWith({
          message: 'Failed to move job',
          type: 'error'
        });
        expect(mockLoadJobs).toHaveBeenCalled(); // Should reload jobs on error
      });
    }
  });

  it('should load jobs and statuses on mount', async () => {
    const mockLoadJobs = vi.fn();
    const mockLoadJobStatuses = vi.fn();
    const mockLoadJobTypes = vi.fn();
    
    (jobStore.loadJobs as any) = mockLoadJobs;
    (jobStore.loadJobStatuses as any) = mockLoadJobStatuses;
    (jobStore.loadJobTypes as any) = mockLoadJobTypes;
    
    render(JobsPage);
    
    await waitFor(() => {
      expect(mockLoadJobs).toHaveBeenCalled();
      expect(mockLoadJobStatuses).toHaveBeenCalled();
      expect(mockLoadJobTypes).toHaveBeenCalled();
    });
  });

  it('should open job modal when Add Job button is clicked', async () => {
    const mockOpenJobModal = vi.fn();
    (jobStore.openJobModal as any) = mockOpenJobModal;
    
    const { getByText } = render(JobsPage);
    
    const addButton = getByText('Add Job');
    fireEvent.click(addButton);
    
    expect(mockOpenJobModal).toHaveBeenCalledWith('create');
  });
});
