<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { Job } from '$lib/api/jobs';

  // Props
  export let job: Job;

  const dispatch = createEventDispatcher();

  function handleEdit(event: Event) {
    event.stopPropagation(); // Prevent card click from firing
    dispatch('edit', job);
  }

  function handleCardClick() {
    dispatch('edit', job);
  }

  // Format scheduled date for display
  function formatScheduledDate(dateTime?: string): string {
    if (!dateTime) return '';
    const date = new Date(dateTime);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  // Get priority color
  function getPriorityColor(priority: string): string {
    switch (priority) {
      case 'Urgent': return '#EF4444';
      case 'High': return '#F59E0B';
      case 'Medium': return '#3B82F6';
      case 'Low': return '#6B7280';
      default: return '#6B7280';
    }
  }
</script>

<div class="job-card" role="button" aria-label="Draggable job card" on:click={handleCardClick} on:keydown={(e) => e.key === 'Enter' && handleCardClick()} tabindex="0">
  <div class="job-header">
    <h4 class="job-title">{job.title}</h4>
    <div class="priority-badge" style="background-color: {getPriorityColor(job.priority)}">
      {job.priority}
    </div>
  </div>

  <div class="job-customer">
    <span class="label">Customer:</span> {job.customerName || 'Unknown'}
  </div>

  {#if job.scheduledDateTime}
    <div class="job-scheduled">
      <span class="label">Scheduled:</span> {formatScheduledDate(job.scheduledDateTime)}
    </div>
  {/if}

  {#if job.estimatedDuration}
    <div class="job-duration">
      <span class="label">Est. Duration:</span> {Math.round(job.estimatedDuration / 60 * 10) / 10}h
    </div>
  {/if}

  <div class="job-footer">
    <div class="job-type">{job.jobType || 'General'}</div>
    <button class="edit-button" on:click={handleEdit} aria-label="Edit job">Edit</button>
  </div>
</div>

<style lang="less">
  .job-card {
    background: white;
    border-radius: var(--br);
    border: 1px solid var(--border);
    padding: 1rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    cursor: grab;
    transition: box-shadow 0.2s, transform 0.1s;
    user-select: none; /* Prevent text selection during drag */

    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &:active {
      cursor: grabbing;
      transform: scale(0.98);
    }

    &:focus {
      outline: 2px solid var(--primary);
      outline-offset: -2px;
    }
  }

  .job-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;

    .job-title {
      margin: 0;
      font-size: 1rem;
      font-weight: 600;
      color: var(--black);
      flex: 1;
    }

    .priority-badge {
      font-size: 0.7rem;
      font-weight: 500;
      color: white;
      padding: 0.2rem 0.4rem;
      border-radius: var(--br);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .job-customer, .job-scheduled, .job-duration {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;

    .label {
      color: var(--grey);
      font-weight: 500;
    }
  }

  .job-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border);
    font-size: 0.8rem;

    .job-type {
      color: var(--grey);
      font-style: italic;
    }

    .edit-button {
      background: none;
      border: none;
      color: var(--primary);
      cursor: pointer;
      font-size: 0.8rem;
      padding: 0.25rem 0.5rem;
      border-radius: var(--br);
      font-weight: 500;

      &:hover {
        background: var(--secondary-fade);
      }
    }
  }
</style>
