<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Modal from '$lib/components/Modal.svelte';
  import Button from '$lib/components/Button.svelte';
  import CustomerSelect from '$lib/components/CustomerSelect.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { api } from '$lib/utils/api';
  import {
    selectedJob,
    jobModalOpen,
    jobModalMode,
    jobTypes,
    jobStore
  } from '$lib/stores/jobStore';
  import { customers, contactStore } from '$lib/stores/customerStore';
  import { activeStaff, staffStore } from '$lib/stores/staffStore';
  import { 
    getResources,
    calculateJobCostEstimate,
    type Job, 
    type Resource, 
    type JobType,
    type AssignedStaff,
    type JobResource,
    type CustomField,
    type JobCostEstimate
  } from '$lib/api/jobs';
  import { onMount } from 'svelte';

  const dispatch = createEventDispatcher();

  // Load data when component mounts
  onMount(async () => {
    await Promise.all([
      contactStore.loadContacts(),
      staffStore.loadActiveStaff(),
      loadResources()
    ]);
  });

  // Resources state
  let resources: Resource[] = [];
  let costEstimate: JobCostEstimate | null = null;

  async function loadResources() {
    try {
      resources = await getResources();
    } catch (error) {
      console.error('Error loading resources:', error);
    }
  }

  // Form data
  let formData = {
    title: '',
    description: '',
    customerId: '',
    jobTypeId: '',
    scheduledDate: '',
    scheduledTime: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US'
    },
    priority: 'Medium' as 'Low' | 'Medium' | 'High' | 'Urgent',
    estimatedHours: 0,
    customFields: [] as Array<{ key: string; value: string }>,
    assignedStaffIds: [] as string[],
    resourceIds: [] as string[]
  };

  // Customer search state
  let customerSearch = '';
  let formSubmitted = false;
  let errors: Record<string, string> = {};
  let selectedCustomer: any = null; // Store the selected customer object

  // Reset form when modal opens/closes or selected job changes
  $: if ($jobModalOpen && $selectedJob && $jobModalMode === 'edit') {
    // Populate form with selected job data
    formData = {
      title: $selectedJob.title,
      description: $selectedJob.description,
      customerId: $selectedJob.customerId,
      jobTypeId: $selectedJob.jobTypeId,
      scheduledDate: $selectedJob.scheduledDateTime ? $selectedJob.scheduledDateTime.split('T')[0] : '',
      scheduledTime: $selectedJob.scheduledDateTime ? $selectedJob.scheduledDateTime.split('T')[1]?.substring(0, 5) : '',
      address: { ...$selectedJob.jobAddress },
      priority: $selectedJob.priority,
      estimatedHours: Math.round(($selectedJob.estimatedDuration || 0) / 60 * 100) / 100,
      customFields: ($selectedJob.customFields || []).map(cf => ({ key: cf.key, value: cf.value })),
      assignedStaffIds: ($selectedJob.assignedStaff || []).map(staff => staff.staffId),
      resourceIds: ($selectedJob.resources || []).map(r => r.resourceId)
    };

    // Set customer search to the selected customer's name
    const customer = $customers.find(c => c.id === $selectedJob.customerId);
    customerSearch = customer ? (customer.companyName || customer.fullName) : '';
    selectedCustomer = customer || null;
    formSubmitted = false;
    errors = {};
  } else if ($jobModalOpen && $jobModalMode === 'create') {
    // Reset form for new job
    formData = {
      title: '',
      description: '',
      customerId: '',
      jobTypeId: '',
      scheduledDate: '',
      scheduledTime: '',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'US'
      },
      priority: 'Medium',
      estimatedHours: 0,
      customFields: [],
      assignedStaffIds: [],
      resourceIds: []
    };
    customerSearch = '';
    selectedCustomer = null;
    formSubmitted = false;
    errors = {};
  }

  // Calculate cost estimate reactively
  $: {
    if (formData.jobTypeId && (formData.assignedStaffIds.length > 0 || formData.resourceIds.length > 0)) {
      calculateCostEstimate();
    } else {
      costEstimate = null;
    }
  }

  async function calculateCostEstimate() {
    const selectedJobType = $jobTypes.find(jt => jt.id === formData.jobTypeId);
    if (!selectedJobType) {
      costEstimate = null;
      return;
    }

    // Build assigned staff array
    const assignedStaff: AssignedStaff[] = formData.assignedStaffIds.map(staffId => {
      const staffMember = $activeStaff.find(s => s.id === staffId);
      return {
        staffId,
        staffName: staffMember?.fullName || 'Unknown',
        hourlyRate: staffMember?.wageInfo?.rate || 0,
        estimatedHours: formData.estimatedHours || (selectedJobType.defaultDuration ? selectedJobType.defaultDuration / 60 : 2),
        role: staffMember?.position || ''
      };
    });

    // Build job resources array
    const jobResources: JobResource[] = formData.resourceIds.map(resourceId => {
      const resource = resources.find(r => r.id === resourceId);
      return {
        id: generateId(),
        resourceId,
        resourceName: resource?.name || 'Unknown',
        resourceType: resource?.type || 'Equipment',
        quantity: 1,
        costPerUnit: resource?.costPerUnit || resource?.costPerHour || 0,
        totalCost: resource?.costPerUnit || resource?.costPerHour || 0
      };
    });

    // Convert custom fields
    const customFields: CustomField[] = formData.customFields
      .filter(cf => cf.key.trim() && cf.value.trim())
      .map(cf => ({
        id: generateId(),
        key: cf.key.trim(),
        value: cf.value.trim(),
        type: 'text' as const,
        options: undefined
      }));

    try {
      costEstimate = calculateJobCostEstimate(selectedJobType, assignedStaff, jobResources, customFields);
    } catch (error) {
      console.error('Error calculating cost estimate:', error);
      costEstimate = null;
    }
  }

  function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  function handleCustomerSelect(customer: any) {
    formData.customerId = customer.id;
    selectedCustomer = customer;
    // Clear customer error if it exists
    if (errors.customerId) {
      errors = { ...errors };
      delete errors.customerId;
    }
  }

  async function handleSubmit() {
    formSubmitted = true;
    errors = {};

    try {
      // Validate required fields
      if (!formData.title.trim()) {
        errors.title = 'Job title is required';
      }

      if (!formData.customerId) {
        errors.customerId = 'Customer is required';
      }

      // If there are validation errors, don't submit
      if (Object.keys(errors).length > 0) {
        return;
      }

      // Find customer name for display
      let customer = selectedCustomer;

      // If we don't have the customer object, try to fetch it from the API
      if (!customer && formData.customerId) {
        try {
          const response: any = await api.get(`/Customers/${formData.customerId}`);
          customer = {
            id: response.id,
            fullName: response.name || response.fullName || 'Unknown Customer',
            companyName: response.companyName || '',
            emails: response.emails || [{ 
              id: '1', 
              email: response.email || '', 
              type: 'Work', 
              isPrimary: true 
            }],
            phones: response.phones || [{ 
              id: '1', 
              phone: response.phone || '', 
              type: 'Work', 
              isPrimary: true 
            }],
            addresses: response.addresses || [],
            status: response.status || 'Customer',
            notes: response.notes || [],
            checklists: response.checklists || [],
            communicationTimeline: response.communicationTimeline || [],
            createdAt: response.createdAt || new Date().toISOString(),
            updatedAt: response.updatedAt || new Date().toISOString()
          };
          selectedCustomer = customer;
        } catch (error) {
          console.error('Error fetching customer:', error);
        }
      }

      if (!customer) {
        addToast({ message: 'Selected customer not found', type: 'error' });
        return;
      }

      // Combine date and time
      let scheduledDateTime: string | undefined;
      if (formData.scheduledDate) {
        scheduledDateTime = formData.scheduledTime
          ? `${formData.scheduledDate}T${formData.scheduledTime}:00`
          : `${formData.scheduledDate}T09:00:00`;
      }

      // Convert custom fields to proper format
      const customFields = formData.customFields
        .filter(cf => cf.key.trim() && cf.value.trim())
        .map(cf => ({
          id: generateId(),
          key: cf.key.trim(),
          value: cf.value.trim(),
          type: 'text' as const,
          options: undefined
        }));

      // Build assigned staff array
      const assignedStaff: AssignedStaff[] = formData.assignedStaffIds.map(staffId => {
        const staffMember = $activeStaff.find(s => s.id === staffId);
        return {
          staffId,
          staffName: staffMember?.fullName || 'Unknown',
          hourlyRate: staffMember?.wageInfo?.rate || 0,
          estimatedHours: formData.estimatedHours || 2,
          role: staffMember?.position || ''
        };
      });

      // Build job resources array
      const jobResources: JobResource[] = formData.resourceIds.map(resourceId => {
        const resource = resources.find(r => r.id === resourceId);
        return {
          id: generateId(),
          resourceId,
          resourceName: resource?.name || 'Unknown',
          resourceType: resource?.type || 'Equipment',
          quantity: 1,
          costPerUnit: resource?.costPerUnit || resource?.costPerHour || 0,
          totalCost: resource?.costPerUnit || resource?.costPerHour || 0
        };
      });

      const jobData = {
        title: formData.title.trim(),
        customerId: formData.customerId,
        customerName: customer.fullName,
        jobTypeId: formData.jobTypeId,
        description: formData.description.trim(),
        scheduledDateTime,
        jobAddress: formData.address,
        priority: formData.priority,
        estimatedDuration: formData.estimatedHours * 60, // Convert hours to minutes
        customFields,
        tags: $selectedJob?.tags || [],
        // Default values for new jobs
        status: $selectedJob?.status || { id: '1', name: 'Backlog', color: '#6B7280', order: 1, isCompleted: false },
        assignedStaff,
        resources: jobResources,
        estimatedCost: costEstimate || { laborCost: 0, materialCost: 0, resourceCost: 0, totalCost: 0, breakdown: [] },
        attachments: $selectedJob?.attachments || [],
        notes: $selectedJob?.notes || ''
      };

      if ($jobModalMode === 'edit' && $selectedJob) {
        await jobStore.updateJob($selectedJob.id, jobData);
        addToast({ message: 'Job updated successfully', type: 'success' });
      } else {
        await jobStore.addJob(jobData);
        addToast({ message: 'Job created successfully', type: 'success' });
      }

      jobStore.closeJobModal();
    } catch (error) {
      console.error('Error saving job:', error);
      addToast({ message: 'Failed to save job', type: 'error' });
    }
  }

  function addCustomField() {
    formData.customFields = [...formData.customFields, { key: '', value: '' }];
  }

  function removeCustomField(index: number) {
    formData.customFields = formData.customFields.filter((_, i) => i !== index);
  }

  function handleClose() {
    jobStore.closeJobModal();
  }
</script>

<Modal bind:show={$jobModalOpen} title={$jobModalMode === 'edit' ? 'Edit Job' : 'Create New Job'} on:close={handleClose}>
    <form on:submit|preventDefault={handleSubmit} class="job-form">
      <div class="form-grid">
        <!-- Job Title -->
        <div class="form-group">
          <label for="title">Job Title *</label>
          <input
            id="title"
            type="text"
            bind:value={formData.title}
            placeholder="Enter job title"
            required
          />
        </div>

        <!-- Customer -->
        <div class="form-group">
          <CustomerSelect
            bind:customerId={formData.customerId}
            bind:customerSearch={customerSearch}
            hasError={formSubmitted && !!errors.customerId}
            errorMessage={formSubmitted && errors.customerId ? errors.customerId : ''}
            on:selectcustomer={(event) => handleCustomerSelect(event.detail)}
          />
        </div>

        <!-- Job Type -->
        <div class="form-group">
          <label for="jobType">Job Type</label>
          <select id="jobType" bind:value={formData.jobTypeId}>
            <option value="">Select job type</option>
            {#each $jobTypes as jobType}
              <option value={jobType.id}>{jobType.name}</option>
            {/each}
          </select>
        </div>

        <!-- Priority -->
        <div class="form-group">
          <label for="priority">Priority</label>
          <select id="priority" bind:value={formData.priority}>
            <option value="Low">Low</option>
            <option value="Medium">Medium</option>
            <option value="High">High</option>
            <option value="Urgent">Urgent</option>
          </select>
        </div>

        <!-- Scheduled Date -->
        <div class="form-group">
          <label for="scheduledDate">Scheduled Date</label>
          <input
            id="scheduledDate"
            type="date"
            bind:value={formData.scheduledDate}
          />
        </div>

        <!-- Scheduled Time -->
        <div class="form-group">
          <label for="scheduledTime">Scheduled Time</label>
          <input
            id="scheduledTime"
            type="time"
            bind:value={formData.scheduledTime}
          />
        </div>

        <!-- Estimated Hours -->
        <div class="form-group">
          <label for="estimatedHours">Estimated Hours</label>
          <input
            id="estimatedHours"
            type="number"
            min="0"
            step="0.5"
            bind:value={formData.estimatedHours}
            placeholder="0"
          />
        </div>

        <!-- Assigned Staff -->
        <div class="form-group staff-section">
          <label>Assigned Staff</label>
          <div class="staff-checkboxes">
            {#each $activeStaff as staffMember}
              <label>
                <input
                  type="checkbox"
                  bind:group={formData.assignedStaffIds}
                  value={staffMember.id}
                />
                <span class="checkbox-custom"></span>
                {staffMember.fullName} - {staffMember.position}
                {#if staffMember.wageInfo?.rate}
                  <span class="wage-info">(${staffMember.wageInfo.rate}/hr)</span>
                {/if}
              </label>
            {/each}
          </div>
        </div>

        <!-- Resources -->
        <div class="form-group resources-section">
          <label>Resources</label>
          <div class="resource-checkboxes">
            {#each resources as resource}
              <label>
                <input
                  type="checkbox"
                  bind:group={formData.resourceIds}
                  value={resource.id}
                />
                <span class="checkbox-custom"></span>
                {resource.name} ({resource.type})
                {#if resource.costPerHour || resource.costPerUnit}
                  <span class="cost-info">
                    {#if resource.costPerHour}
                      (${resource.costPerHour}/hr)
                    {:else if resource.costPerUnit}
                      (${resource.costPerUnit}/unit)
                    {/if}
                  </span>
                {/if}
              </label>
            {/each}
          </div>
        </div>
      </div>

      <!-- Description -->
      <div class="form-group full-width">
        <label for="description">Description</label>
        <textarea
          id="description"
          bind:value={formData.description}
          placeholder="Enter job description"
          rows="4"
        ></textarea>
      </div>

      <!-- Address -->
      <div class="address-section">
        <h3>Job Address</h3>
        <div class="form-grid">
          <div class="form-group">
            <label for="street">Street Address</label>
            <input
              id="street"
              type="text"
              bind:value={formData.address.street}
              placeholder="Enter street address"
            />
          </div>

          <div class="form-group">
            <label for="city">City</label>
            <input
              id="city"
              type="text"
              bind:value={formData.address.city}
              placeholder="Enter city"
            />
          </div>

          <div class="form-group">
            <label for="state">State</label>
            <input
              id="state"
              type="text"
              bind:value={formData.address.state}
              placeholder="Enter state"
            />
          </div>

          <div class="form-group">
            <label for="zipCode">ZIP Code</label>
            <input
              id="zipCode"
              type="text"
              bind:value={formData.address.zipCode}
              placeholder="Enter ZIP code"
            />
          </div>
        </div>
      </div>

      <!-- Custom Fields -->
      <div class="custom-fields-section">
        <div class="section-header">
          <h3>Custom Fields</h3>
          <Button type="button" variant="secondary" size="small" on:click={addCustomField}>
            Add Field
          </Button>
        </div>

        {#each formData.customFields as field, index}
          <div class="custom-field">
            <input
              type="text"
              bind:value={field.key}
              placeholder="Field name"
              class="field-key"
            />
            <input
              type="text"
              bind:value={field.value}
              placeholder="Field value"
              class="field-value"
            />
            <Button
              type="button"
              variant="tertiary"
              size="small"
              on:click={() => removeCustomField(index)}
            >
              Remove
            </Button>
          </div>
        {/each}
      </div>

      <!-- Cost Estimate -->
      {#if costEstimate}
        <div class="cost-estimate-section">
          <h3>Cost Estimate</h3>
          <div class="cost-breakdown">
            <div class="cost-row">
              <span>Labor Cost:</span>
              <span>${costEstimate.laborCost.toFixed(2)}</span>
            </div>
            <div class="cost-row">
              <span>Material Cost:</span>
              <span>${costEstimate.materialCost.toFixed(2)}</span>
            </div>
            <div class="cost-row">
              <span>Resource Cost:</span>
              <span>${costEstimate.resourceCost.toFixed(2)}</span>
            </div>
            <div class="cost-row total">
              <span>Total Estimated Cost:</span>
              <span>${costEstimate.totalCost.toFixed(2)}</span>
            </div>
          </div>
          
          {#if costEstimate.breakdown.length > 0}
            <details class="cost-details">
              <summary>Cost Breakdown</summary>
              <div class="breakdown-items">
                {#each costEstimate.breakdown as item}
                  <div class="breakdown-item">
                    <span class="description">{item.description}</span>
                    <span class="amount">${item.totalCost.toFixed(2)}</span>
                  </div>
                {/each}
              </div>
            </details>
          {/if}
        </div>
      {/if}

      <div class="form-actions">
        <Button type="button" variant="secondary" on:click={handleClose}>
          Cancel
        </Button>
        <Button type="submit" variant="primary">
          {$jobModalMode === 'edit' ? 'Update Job' : 'Create Job'}
        </Button>
      </div>
    </form>
</Modal>

<style lang="less">
  .job-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
  }

  .staff-section {
    grid-column: 1 / -1;
  }

  .staff-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .address-section, .custom-fields-section {
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1rem;

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1rem;
      color: var(--black);
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      margin: 0;
    }
  }

  .custom-field {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    margin-bottom: 0.5rem;

    .field-key, .field-value {
      flex: 1;
      padding: 0.5rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border);
  }

  .resources-section {
    grid-column: 1 / -1;
  }

  .resource-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .cost-estimate-section {
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1rem;

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1rem;
      color: var(--black);
    }
  }

  .cost-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .cost-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .cost-details {
    margin-top: 0.5rem;
  }

  .breakdown-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .wage-info, .cost-info {
    font-size: 0.8rem;
    color: var(--gray);
  }
</style>