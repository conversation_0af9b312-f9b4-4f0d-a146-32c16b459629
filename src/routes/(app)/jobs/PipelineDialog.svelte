<script lang="ts">
  import { dndzone } from 'svelte-dnd-action';
  import Button from '$lib/components/Button.svelte';

  // Define types
  interface Stage {
    id: string;
    name: string;
  }

  interface Pipeline {
    id: string;
    name: string;
    stages: Stage[];
  }

  // Props
  export let pipeline: Pipeline | null = null;

  // Default stages if creating a new pipeline
  const defaultStages: Stage[] = [
    { id: 's1', name: 'Fresh' },
    { id: 's2', name: 'Contacted' },
    { id: 's3', name: 'Won' },
    { id: 's4', name: 'Lost' }
  ];

  // Local state
  let pipelineName = pipeline?.name || '';
  let stages: Stage[] = pipeline?.stages ? [...pipeline.stages] : [...defaultStages];
  let newStageName = '';
  let errors = { name: '' };

  import { createEventDispatcher } from 'svelte';
  const dispatch = createEventDispatcher<{
    save: Pipeline;
    cancel: void;
  }>();

  // Event handling
  function dispatchSave(data: Pipeline) {
    dispatch('save', data);
  }

  function dispatchCancel() {
    dispatch('cancel');
  }

  function handleSave() {
    // Validate
    errors.name = '';

    if (!pipelineName.trim()) {
      errors.name = 'Pipeline name is required';
      return;
    }

    if (stages.length === 0) {
      // Add toast or error message about needing at least one stage
      return;
    }

    // Create pipeline object
    const pipelineData = {
      id: pipeline?.id || '',
      name: pipelineName.trim(),
      stages: stages
    };

    dispatchSave(pipelineData);
  }

  function handleCancel() {
    dispatchCancel();
  }

  function addStage() {
    if (!newStageName.trim()) return;

    const newStage = {
      id: `s${Date.now()}`, // Simple unique ID
      name: newStageName.trim()
    };

    stages = [...stages, newStage];
    newStageName = '';
  }

  function removeStage(stageId: string) {
    stages = stages.filter(stage => stage.id !== stageId);
  }

  function handleDndConsiderStages(event: CustomEvent<{items: Stage[]}>) {
    const { detail } = event;
    stages = [...detail.items];
  }

  function handleDndFinalizeStages(event: CustomEvent<{items: Stage[]}>) {
    const { detail } = event;
    stages = [...detail.items];
  }

  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      addStage();
    }
  }
</script>

<div class="pipeline-dialog">
  <div class="form-group">
    <label for="pipeline-name">Pipeline Name</label>
    <input
      type="text"
      id="pipeline-name"
      bind:value={pipelineName}
      placeholder="Enter pipeline name"
      class:error={errors.name}
    />
    {#if errors.name}
      <div class="error-message">{errors.name}</div>
    {/if}
  </div>

  <div class="stages-section">
    <h3>Pipeline Stages</h3>
    <p class="help-text">Drag to reorder stages. Jobs will flow through these stages from left to right.</p>

    <div class="add-stage">
      <input
        type="text"
        placeholder="New stage name"
        bind:value={newStageName}
        on:keypress={handleKeyPress}
      />
      <Button on:click={addStage} variant="secondary" size="small" type="button">Add Stage</Button>
    </div>

    <div class="stages-list"
      use:dndzone={{
        items: stages,
        type: 'pipeline-stages',
        flipDurationMs: 200,
        morphDisabled: true,
        dropTargetStyle: {
          outline: '2px dashed var(--primary)',
          outlineOffset: '-4px',
          backgroundColor: 'var(--secondary-fade)'
        }
      }}
      on:consider={handleDndConsiderStages}
      on:finalize={handleDndFinalizeStages}>
      {#each stages as stage (stage.id)}
        <div class="stage-item">
          <div class="drag-handle">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="9" cy="6" r="2" />
              <circle cx="9" cy="12" r="2" />
              <circle cx="9" cy="18" r="2" />
              <circle cx="15" cy="6" r="2" />
              <circle cx="15" cy="12" r="2" />
              <circle cx="15" cy="18" r="2" />
            </svg>
          </div>
          <div class="stage-name">{stage.name}</div>
          <button class="delete-button" on:click={() => removeStage(stage.id)} aria-label={`Remove ${stage.name} stage`}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </button>
        </div>
      {/each}

      {#if stages.length === 0}
        <div class="empty-stages">
          No stages defined. Add at least one stage.
        </div>
      {/if}
    </div>
  </div>

  <div class="dialog-actions">
    <Button on:click={handleCancel} variant="tertiary" type="button">Cancel</Button>
    <Button on:click={handleSave} variant="primary" type="button">Save Pipeline</Button>
  </div>
</div>

<style lang="less">
  .pipeline-dialog {
    width: 100%;
    max-width: 500px;
  }

  .help-text {
    font-size: 0.9rem;
    color: var(--grey);
    margin-bottom: 1rem;
  }

  .stages-section {
    margin-top: 1.5rem;

    h3 {
      margin-top: 0;
      margin-bottom: 0.5rem;
    }
  }

  .add-stage {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;

    input {
      flex: 1;
    }
  }

  .stages-list {
    border: 1px solid var(--border);
    border-radius: var(--br);
    min-height: 100px;
    max-height: 300px;
    overflow-y: auto;
  }

  .stage-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid var(--border);
    background: white;

    &:last-child {
      border-bottom: none;
    }

    .drag-handle {
      cursor: move;
      color: var(--grey);
      margin-right: 0.75rem;
      display: flex;
      align-items: center;
    }

    .stage-name {
      flex: 1;
    }

    .delete-button {
      background: none;
      border: none;
      color: var(--grey);
      cursor: pointer;
      padding: 0.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;

      &:hover {
        background: var(--border);
        color: var(--red);
      }
    }
  }

  .empty-stages {
    padding: 2rem;
    text-align: center;
    color: var(--grey);
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }
</style>
