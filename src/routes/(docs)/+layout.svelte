<script>
  import Sidebar from '$lib/components/Sidebar.svelte';
  import Toasts from '$lib/components/Toasts.svelte';
</script>

<div class="app-layout">
    
        <Sidebar />

    <slot />

    <Toasts />
</div>

<style lang="less">
  :global {
    body {
      padding: 0px;
      margin: 0px;
      font-family: 'IBM Plex Sans', sans-serif;
      background: var(--bg);
      * {
        font-family: 'IBM Plex Sans', sans-serif;
      }
    }
    .app-layout {
      display: grid;
      grid-template-columns: 200px 1fr;
    }

    main {
      padding: 2rem;
    }

    .container {
      overflow: auto;
      height: 100vh;
    }

    .form-group {
    flex: 1;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    
    &.full-width {
      grid-column: 1 / -1;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      font-size: 0.9rem;
      color: var(--grey);
      &:has(input[type="checkbox"]) {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 0;

        &:hover {
          background: var(--bg);
          border-color: var(--primary);
        }

        input[type="checkbox"] {
          display: none;
        }

        .checkbox-custom {
          width: 18px;
          height: 18px;
          border: 2px solid var(--border);
          border-radius: 3px;
          position: relative;
          flex-shrink: 0;
          transition: all 0.2s ease;

          &::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 6px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
            opacity: 0;
            transition: opacity 0.2s ease;
          }
        }

        input[type="checkbox"]:checked + .checkbox-custom {
          background: var(--primary);
          border-color: var(--primary);

          &::after {
            opacity: 1;
          }
        }
      }
      input[type="text"],
      input[type="number"],
      select,
      textarea {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
        &.error {
          border-color: var(--red);
        }
        
      }
      select[multiple] {
        min-height: 120px;
      }
    }
    .error-message {
      color: var(--red);
      font-size: 0.8rem;
      margin-top: 0.25rem;
    }
    .help-text {
      font-size: 0.8rem;
      color: var(--grey);
      margin-top: 0.25rem;
    }
  }

  input, select, textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
    font-family: var(--font-family);

    &:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 2px var(--primary-fade);
    }

    &.error {
      border-color: var(--red);
    }
  }

  textarea {
    resize: vertical;
    min-height: 100px;
  }

  .error-message {
    color: var(--red);
    font-size: 12px;
    margin-top: 4px;
  }

  .vertical-sidebar-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
    min-height: calc(100vh - 200px);
    .vertical-sidebar-content {
      background: white;
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 2rem;
    }
  }

  

  .component-docs-container {
    h1 {
      margin: 0 0 0.5rem 0;
      color: var(--black);
      font-size: 1.75rem;
    }

    h2 {
      margin: 2rem 0 1rem 0;
      color: var(--black);
      font-size: 1.2rem;
    }

    h3 {
      margin: 1rem 0 0.5rem 0;
      color: var(--black);
      font-size: 1.2rem;
    }

    h4 {
      margin-top: 0;
      margin-bottom: 1rem;
      color: var(--primary);
      font-size: 1rem;
    }
    
    .description {
      margin: 0 0 2rem 0;
      color: var(--grey);
      font-size: 1rem;
      line-height: 1.5;
    }
    .props-table {
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;

    .prop-row {
      display: grid;
      grid-template-columns: 1fr 1fr 2fr;
      border-bottom: 1px solid var(--border);

      &:last-child {
        border-bottom: none;
      }

      &:first-child {
        background: var(--bg);
        font-weight: 600;
      }

      div {
        padding: 0.75rem 1rem;
        border-right: 1px solid var(--border);

        &:last-child {
          border-right: none;
        }
      }

      .prop-name {
        font-family: monospace;
        color: var(--primary);
      }

      .prop-type {
        font-family: monospace;
        color: var(--grey);
      }

      .prop-description {
        color: var(--black);
      }
    }
  }
    
  }


  
  }


</style>