<script lang="ts">
  import { onMount } from 'svelte';
  import { marked } from 'marked';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import VerticalMenu from '$lib/components/VerticalMenu.svelte';

  // State for docs
  let docFiles: { id: string; label: string; filename: string }[] = [];
  let currentItem = '';
  let markdownContent = '';
  let renderedHtml = '';
  let loading = false;
  let error = '';

  // Load available markdown files
  onMount(async () => {
    try {
      const response = await fetch('/api/docs');
      if (response.ok) {
        docFiles = await response.json();
      } else {
        throw new Error('Failed to load documentation files');
      }
    } catch (err) {
      console.error('Error loading doc files:', err);
      error = 'Failed to load documentation files';
    }
  });

  // Handle menu selection
  function handleMenuSelect(docId: string) {
    currentItem = docId;
    loadMarkdownFile(docId);
  }

  // Load and render markdown file
  async function loadMarkdownFile(docId: string) {
    const docFile = docFiles.find(f => f.id === docId);
    if (!docFile) return;

    loading = true;
    error = '';

    try {
      const response = await fetch(`/api/docs?file=${docFile.filename}`);
      if (!response.ok) {
        throw new Error(`Failed to load ${docFile.filename}`);
      }
      
      markdownContent = await response.text();
      renderedHtml = await marked(markdownContent);
    } catch (err) {
      console.error('Error loading markdown file:', err);
      error = `Failed to load ${docFile?.label || 'document'}`;
      renderedHtml = '';
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>Documentation</title>
</svelte:head>

<div class="container">
  <PageHeader title="Documentation" />

  <main>
    <div class="vertical-sidebar-layout">
      <VerticalMenu
        items={docFiles}
        currentItem={currentItem}
        onSelect={handleMenuSelect}
      />

      <div class="vertical-sidebar-content">
        {#if !currentItem}
          <div class="welcome-section">
            <h2>Welcome to Documentation</h2>
            <p>Select a document from the menu to view its contents.</p>
          </div>
        {:else if loading}
          <div class="loading-section">
            <p>Loading document...</p>
          </div>
        {:else if error}
          <div class="error-section">
            <h2>Error</h2>
            <p>{error}</p>
          </div>
        {:else if renderedHtml}
          <div class="markdown-content">
            {@html renderedHtml}
          </div>
        {/if}
      </div>
    </div>
  </main>
</div>

<style lang="less">
  .welcome-section,
  .loading-section,
  .error-section {
    text-align: center;
    padding: 4rem 2rem;

    h2 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    p {
      margin: 0;
      color: var(--grey);
    }
  }

  .error-section {
    h2 {
      color: #ef4444;
    }
  }

  .markdown-content {
    line-height: 1.6;
    color: var(--black);

    // Markdown styling
    :global(h1) {
      font-size: 2rem;
      font-weight: 700;
      margin: 0 0 1.5rem 0;
      color: var(--black);
      border-bottom: 2px solid var(--border);
      padding-bottom: 0.5rem;
    }

    :global(h2) {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 2rem 0 1rem 0;
      color: var(--black);
    }

    :global(h3) {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 1.5rem 0 0.75rem 0;
      color: var(--black);
    }

    :global(h4) {
      font-weight: 600;
      margin: 1rem 0 0.5rem 0;
      color: var(--black);
    }

    :global(h5) {
      font-weight: 600;
      margin: 1rem 0 0.5rem 0;
      color: var(--black);
    }

    :global(h6) {
      font-weight: 600;
      margin: 1rem 0 0.5rem 0;
      color: var(--black);
    }

    :global(p) {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    :global(ul) {
      margin: 0 0 1rem 0;
      padding-left: 1.5rem;
    }

    :global(ol) {
      margin: 0 0 1rem 0;
      padding-left: 1.5rem;
    }

    :global(li) {
      margin: 0.25rem 0;
    }

    :global(blockquote) {
      margin: 1rem 0;
      padding: 1rem;
      background: var(--bg);
      border-left: 4px solid var(--primary);
      border-radius: var(--br);
      
      :global(p) {
        margin: 0;
      }
    }

    :global(code) {
      background: var(--bg);
      padding: 0.125rem 0.25rem;
      border-radius: 3px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 0.875rem;
    }

    :global(pre) {
      background: var(--bg);
      padding: 1rem;
      border-radius: var(--br);
      overflow-x: auto;
      margin: 1rem 0;
      border: 1px solid var(--border);

      :global(code) {
        background: none;
        padding: 0;
      }
    }

    :global(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 1rem 0;
      border: 1px solid var(--border);
    }

    :global(th) {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid var(--border);
      background: var(--bg);
      font-weight: 600;
    }

    :global(td) {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid var(--border);
    }

    :global(a) {
      color: var(--primary);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    :global(img) {
      max-width: 100%;
      height: auto;
      border-radius: var(--br);
      margin: 1rem 0;
    }

    :global(hr) {
      border: none;
      border-top: 1px solid var(--border);
      margin: 2rem 0;
    }
  }

  @media (max-width: 768px) {
    .docs-layout {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .docs-sidebar {
      order: 2;
    }

    .docs-content {
      order: 1;
    }
  }
</style> 