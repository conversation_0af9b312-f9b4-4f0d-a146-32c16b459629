<script lang="ts">
  import VerticalMenu from '$lib/components/VerticalMenu.svelte';

  // Basic menu items
  const basicMenuItems = [
    { id: 'dashboard', label: 'Dashboard' },
    { id: 'users', label: 'Users' },
    { id: 'settings', label: 'Settings' },
    { id: 'reports', label: 'Reports' }
  ];
  let currentBasicItem = 'dashboard';

  // Navigation menu items
  const navMenuItems = [
    { id: 'home', label: 'Home' },
    { id: 'about', label: 'About Us' },
    { id: 'services', label: 'Services' },
    { id: 'portfolio', label: 'Portfolio' },
    { id: 'contact', label: 'Contact' }
  ];
  let currentNavItem = 'home';

  // Admin menu items
  const adminMenuItems = [
    { id: 'overview', label: 'Overview' },
    { id: 'user-management', label: 'User Management' },
    { id: 'content-management', label: 'Content Management' },
    { id: 'analytics', label: 'Analytics' },
    { id: 'system-settings', label: 'System Settings' },
    { id: 'security', label: 'Security' },
    { id: 'backup', label: 'Backup & Restore' }
  ];
  let currentAdminItem = 'overview';

  // Category menu items
  const categoryMenuItems = [
    { id: 'electronics', label: 'Electronics' },
    { id: 'clothing', label: 'Clothing & Fashion' },
    { id: 'home-garden', label: 'Home & Garden' },
    { id: 'sports', label: 'Sports & Outdoors' },
    { id: 'books', label: 'Books & Media' },
    { id: 'toys', label: 'Toys & Games' }
  ];
  let currentCategoryItem = 'electronics';

  function handleBasicSelect(itemId: string) {
    currentBasicItem = itemId;
    console.log('Basic menu selected:', itemId);
  }

  function handleNavSelect(itemId: string) {
    currentNavItem = itemId;
    console.log('Navigation menu selected:', itemId);
  }

  function handleAdminSelect(itemId: string) {
    currentAdminItem = itemId;
    console.log('Admin menu selected:', itemId);
  }

  function handleCategorySelect(itemId: string) {
    currentCategoryItem = itemId;
    console.log('Category menu selected:', itemId);
  }
</script>

<div class="component-docs-container">
  <h1>Vertical Menu Components</h1>
  <p class="description">Examples and usage of the VerticalMenu component for navigation and selection interfaces.</p>

  <section>
    <h2>Basic Vertical Menu</h2>
    <p>Simple vertical menu with basic navigation items.</p>
    
    <div class="menu-demo">
      <div class="menu-container">
        <VerticalMenu 
          items={basicMenuItems}
          currentItem={currentBasicItem}
          onSelect={handleBasicSelect}
        />
      </div>
      <div class="content-display">
        <h3>Selected: {currentBasicItem}</h3>
        <p>Content for the {basicMenuItems.find(item => item.id === currentBasicItem)?.label} section would appear here.</p>
      </div>
    </div>
  </section>

  <section>
    <h2>Website Navigation Menu</h2>
    <p>Vertical menu used for website navigation with multiple pages.</p>
    
    <div class="menu-demo">
      <div class="menu-container">
        <VerticalMenu 
          items={navMenuItems}
          currentItem={currentNavItem}
          onSelect={handleNavSelect}
        />
      </div>
      <div class="content-display">
        <h3>{navMenuItems.find(item => item.id === currentNavItem)?.label}</h3>
        {#if currentNavItem === 'home'}
          <p>Welcome to our homepage! Here you'll find the latest updates and featured content.</p>
        {:else if currentNavItem === 'about'}
          <p>Learn more about our company, mission, and the team behind our success.</p>
        {:else if currentNavItem === 'services'}
          <p>Discover our comprehensive range of services designed to meet your needs.</p>
        {:else if currentNavItem === 'portfolio'}
          <p>Browse through our portfolio of successful projects and case studies.</p>
        {:else if currentNavItem === 'contact'}
          <p>Get in touch with us through our contact form or find our office locations.</p>
        {/if}
      </div>
    </div>
  </section>

  <section>
    <h2>Admin Dashboard Menu</h2>
    <p>Comprehensive admin menu with multiple management sections.</p>
    
    <div class="menu-demo">
      <div class="menu-container">
        <VerticalMenu 
          items={adminMenuItems}
          currentItem={currentAdminItem}
          onSelect={handleAdminSelect}
        />
      </div>
      <div class="content-display">
        <h3>{adminMenuItems.find(item => item.id === currentAdminItem)?.label}</h3>
        {#if currentAdminItem === 'overview'}
          <p>Dashboard overview with key metrics and system status.</p>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">1,234</div>
              <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">567</div>
              <div class="stat-label">Active Sessions</div>
            </div>
          </div>
        {:else if currentAdminItem === 'user-management'}
          <p>Manage user accounts, permissions, and access levels.</p>
        {:else if currentAdminItem === 'content-management'}
          <p>Create, edit, and organize website content and media.</p>
        {:else if currentAdminItem === 'analytics'}
          <p>View detailed analytics and performance reports.</p>
        {:else if currentAdminItem === 'system-settings'}
          <p>Configure system-wide settings and preferences.</p>
        {:else if currentAdminItem === 'security'}
          <p>Manage security settings, authentication, and access controls.</p>
        {:else if currentAdminItem === 'backup'}
          <p>Schedule backups and restore system data when needed.</p>
        {/if}
      </div>
    </div>
  </section>

  <section>
    <h2>Category Filter Menu</h2>
    <p>Vertical menu used for filtering content by categories.</p>
    
    <div class="menu-demo">
      <div class="menu-container">
        <VerticalMenu 
          items={categoryMenuItems}
          currentItem={currentCategoryItem}
          onSelect={handleCategorySelect}
        />
      </div>
      <div class="content-display">
        <h3>{categoryMenuItems.find(item => item.id === currentCategoryItem)?.label}</h3>
        <p>Showing products in the {categoryMenuItems.find(item => item.id === currentCategoryItem)?.label} category.</p>
        <div class="product-grid">
          <div class="product-item">Product 1</div>
          <div class="product-item">Product 2</div>
          <div class="product-item">Product 3</div>
          <div class="product-item">Product 4</div>
        </div>
      </div>
    </div>
  </section>
</div>

<style lang="less">
    section {
      background: var(--bg);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--border);
      
      h2 {
        color: var(--black);
        margin-top: 0;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
      }

      p {
        margin-bottom: 1.5rem;
        color: var(--grey);
        line-height: 1.5;
      }
    }

    .menu-demo {
      display: grid;
      grid-template-columns: 250px 1fr;
      gap: 2rem;
      background: white;
      border-radius: 8px;
      border: 1px solid var(--border);
      overflow: hidden;
      min-height: 300px;
    }

    .menu-container {
      background: var(--bg);
      padding: 1rem;
      border-right: 1px solid var(--border);
    }

    .content-display {
      padding: 1.5rem;

      h3 {
        margin-top: 0;
        margin-bottom: 1rem;
        color: var(--primary);
        font-size: 1.25rem;
      }

      p {
        margin-bottom: 1rem;
        color: var(--grey);
        line-height: 1.5;
      }
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
      margin-top: 1rem;
    }

    .stat-item {
      background: var(--bg);
      padding: 1rem;
      border-radius: 6px;
      text-align: center;

      .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--primary);
        margin-bottom: 0.5rem;
      }

      .stat-label {
        font-size: 0.9rem;
        color: var(--grey);
      }
    }

    .product-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 1rem;
      margin-top: 1rem;
    }

    .product-item {
      background: var(--bg);
      padding: 1rem;
      border-radius: 6px;
      text-align: center;
      border: 1px solid var(--border);
      color: var(--grey);
    }

    @media (max-width: 768px) {
      .menu-demo {
        grid-template-columns: 1fr;
        
        .menu-container {
          border-right: none;
          border-bottom: 1px solid var(--border);
        }
      }
    }
  
</style> 