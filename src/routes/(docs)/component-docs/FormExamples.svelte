<script lang="ts">
  // Form state examples
  let basicForm = {
    title: '',
    description: '',
    email: '',
    isDefault: false
  };

  let validationForm = {
    title: '',
    email: '',
    customerId: '',
    customerSearch: ''
  };

  let radioForm = {
    priority: 'medium'
  };

  let switchForm = {
    notifications: true,
    newsletter: false,
    marketing: false
  };

  let errors: Record<string, string> = {};
  let formSubmitted = false;

  // Mock CustomerSelect component for example
  const CustomerSelect = () => '';

  function handleCustomerSelect(event: any) {
    validationForm.customerId = event.detail.customerId;
    validationForm.customerSearch = event.detail.customerName;
  }

  function validateForm() {
    errors = {};
    
    if (!validationForm.title.trim()) {
      errors.title = 'Title is required';
    }
    
    if (!validationForm.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(validationForm.email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    if (!validationForm.customerId) {
      errors.customerId = 'Please select a customer';
    }

    return Object.keys(errors).length === 0;
  }

  function handleSubmit() {
    formSubmitted = true;
    if (validateForm()) {
      alert('Form is valid!');
    }
  }

  function resetForms() {
    basicForm = {
      title: '',
      description: '',
      email: '',
      isDefault: false
    };
    
    validationForm = {
      title: '',
      email: '',
      customerId: '',
      customerSearch: ''
    };
    
    errors = {};
    formSubmitted = false;
  }
</script>

<div class="component-docs-container">
  <h1>Form Layouts</h1>
  <p class="description">Standard patterns for laying out form fields including text inputs, checkboxes, radio buttons, switches, and validation.</p>

  <section>
    <h2>Basic Form Fields</h2>
    <form class="form-example">
      <div class="form-group">
        <label for="basic-title">Title</label>
        <input
          type="text"
          id="basic-title"
          bind:value={basicForm.title}
          placeholder="Enter title"
        />
      </div>

      <div class="form-group">
        <label for="basic-email">Email</label>
        <input
          type="email"
          id="basic-email"
          bind:value={basicForm.email}
          placeholder="<EMAIL>"
        />
      </div>

      <div class="form-group">
        <label for="basic-description">Description</label>
        <textarea
          id="basic-description"
          bind:value={basicForm.description}
          placeholder="Enter description"
          rows="4"
        ></textarea>
      </div>

      <div class="form-group">
        <label>
          <input type="checkbox" bind:checked={basicForm.isDefault} />
          <span class="checkbox-custom"></span>
          Set as default
        </label>
      </div>
    </form>
  </section>

  <section>
    <h2>Form with Validation</h2>
    <form class="form-example" on:submit|preventDefault={handleSubmit}>
      <div class="form-group">
        <label for="validation-title">Title *</label>
        <input
          type="text"
          id="validation-title"
          bind:value={validationForm.title}
          class:error={formSubmitted && errors.title}
          placeholder="Event title"
        />
        {#if formSubmitted && errors.title}
          <div class="error-message">{errors.title}</div>
        {/if}
      </div>

      <div class="form-group">
        <label for="validation-email">Email *</label>
        <input
          type="email"
          id="validation-email"
          bind:value={validationForm.email}
          class:error={formSubmitted && errors.email}
          placeholder="<EMAIL>"
        />
        {#if formSubmitted && errors.email}
          <div class="error-message">{errors.email}</div>
        {/if}
      </div>

      <div class="form-group">
        <label>Customer *</label>
        <div class="custom-select-wrapper">
          <select
            bind:value={validationForm.customerId}
            class:error={formSubmitted && errors.customerId}
          >
            <option value="">Select a customer...</option>
            <option value="1">Acme Corp</option>
            <option value="2">Tech Solutions Ltd</option>
            <option value="3">Global Industries</option>
          </select>
        </div>
        {#if formSubmitted && errors.customerId}
          <div class="error-message">{errors.customerId}</div>
        {/if}
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-secondary" on:click={resetForms}>Reset</button>
        <button type="submit" class="btn btn-primary">Submit</button>
      </div>
    </form>
  </section>

  <section>
    <h2>Radio Buttons</h2>
    <div class="form-example">
      <div class="form-group">
        <label class="form-label">Priority Level</label>
        <div class="radio-group">
          <label class="radio-option">
            <input
              type="radio"
              bind:group={radioForm.priority}
              value="low"
            />
            <span class="radio-custom"></span>
            Low Priority
          </label>
          <label class="radio-option">
            <input
              type="radio"
              bind:group={radioForm.priority}
              value="medium"
            />
            <span class="radio-custom"></span>
            Medium Priority
          </label>
          <label class="radio-option">
            <input
              type="radio"
              bind:group={radioForm.priority}
              value="high"
            />
            <span class="radio-custom"></span>
            High Priority
          </label>
        </div>
        <p class="field-help">Selected: {radioForm.priority}</p>
      </div>
    </div>
  </section>

  <section>
    <h2>Toggle Switches</h2>
    <div class="form-example">
      <div class="form-group">
        <label class="switch-label">
          <span>Email Notifications</span>
          <label class="switch">
            <input type="checkbox" bind:checked={switchForm.notifications} />
            <span class="switch-slider"></span>
          </label>
        </label>
      </div>

      <div class="form-group">
        <label class="switch-label">
          <span>Newsletter Subscription</span>
          <label class="switch">
            <input type="checkbox" bind:checked={switchForm.newsletter} />
            <span class="switch-slider"></span>
          </label>
        </label>
      </div>

      <div class="form-group">
        <label class="switch-label">
          <span>Marketing Communications</span>
          <label class="switch">
            <input type="checkbox" bind:checked={switchForm.marketing} />
            <span class="switch-slider"></span>
          </label>
        </label>
      </div>
    </div>
  </section>

  <section>
    <h2>Form Layout Examples</h2>
    <div class="form-example">
      <div class="form-row">
        <div class="form-group">
          <label for="first-name">First Name</label>
          <input type="text" id="first-name" placeholder="First name" />
        </div>
        <div class="form-group">
          <label for="last-name">Last Name</label>
          <input type="text" id="last-name" placeholder="Last name" />
        </div>
      </div>

      <div class="form-row">
        <div class="form-group flex-2">
          <label for="address">Address</label>
          <input type="text" id="address" placeholder="Street address" />
        </div>
        <div class="form-group">
          <label for="postal">Postal Code</label>
          <input type="text" id="postal" placeholder="12345" />
        </div>
      </div>
    </div>
  </section>
</div>

<style lang="less">
  h1 {
    margin: 0 0 0.5rem 0;
    color: var(--black);
    font-size: 1.75rem;
  }

  .description {
    margin: 0 0 2rem 0;
    color: var(--grey);
    font-size: 1rem;
    line-height: 1.5;
  }
  
  section {
    background: var(--bg);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border);
    
    h2 {
      color: var(--black);
      margin-top: 0;
      margin-bottom: 1.5rem;
      font-size: 1.25rem;
      font-weight: 600;
    }
  }

  .form-example {
    max-width: 600px;
  }

  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: var(--black);
      font-weight: 500;
      font-size: 0.9rem;

      &.form-label {
        margin-bottom: 0.75rem;
      }
    }

    input[type="text"],
    input[type="email"],
    textarea,
    select {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
      font-size: 0.9rem;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px var(--primary-fade);
      }

      &.error {
        border-color: var(--red);
        box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
      }

      &::placeholder {
        color: var(--grey-light);
      }
    }

    textarea {
      resize: vertical;
      min-height: 100px;
    }
  }

  .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;

    .form-group {
      flex: 1;
      margin-bottom: 0;

      &.flex-2 {
        flex: 2;
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0;

      .form-group {
        margin-bottom: 1.5rem;
      }
    }
  }

  .error-message {
    color: var(--red);
    font-size: 0.8rem;
    margin-top: 0.25rem;
  }

  .field-help {
    color: var(--grey);
    font-size: 0.8rem;
    margin-top: 0.5rem;
    margin-bottom: 0;
  }

  // Checkbox styling
  .form-group label {
    input[type="checkbox"] {
      display: none;
    }

    input[type="checkbox"] + .checkbox-custom {
      display: inline-block;
      width: 18px;
      height: 18px;
      border: 2px solid var(--border);
      border-radius: 3px;
      margin-right: 0.5rem;
      vertical-align: middle;
      position: relative;
      cursor: pointer;
      transition: all 0.2s ease;

      &::after {
        content: '';
        position: absolute;
        left: 5px;
        top: 2px;
        width: 6px;
        height: 10px;
        border: solid var(--white);
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        opacity: 0;
        transition: opacity 0.2s ease;
      }
    }

    input[type="checkbox"]:checked + .checkbox-custom {
      background-color: var(--primary);
      border-color: var(--primary);

      &::after {
        opacity: 1;
      }
    }

    &:has(input[type="checkbox"]) {
      display: flex;
      align-items: center;
      cursor: pointer;
      font-weight: normal;
    }
  }

  // Radio button styling
  .radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;

    input[type="radio"] {
      display: none;
    }

    .radio-custom {
      display: inline-block;
      width: 18px;
      height: 18px;
      border: 2px solid var(--border);
      border-radius: 50%;
      margin-right: 0.5rem;
      position: relative;
      transition: all 0.2s ease;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: var(--primary);
        transform: translate(-50%, -50%) scale(0);
        transition: transform 0.2s ease;
      }
    }

    input[type="radio"]:checked + .radio-custom {
      border-color: var(--primary);

      &::after {
        transform: translate(-50%, -50%) scale(1);
      }
    }
  }

  // Switch styling
  .switch-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
  }

  .switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;

    input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .switch-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--grey-light);
      transition: 0.3s;
      border-radius: 24px;

      &::before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: var(--white);
        transition: 0.3s;
        border-radius: 50%;
      }
    }

    input:checked + .switch-slider {
      background-color: var(--primary);
    }

    input:checked + .switch-slider::before {
      transform: translateX(20px);
    }
  }

  // Form actions
  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border);
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--br);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &.btn-primary {
      background-color: var(--primary);
      color: var(--white);

      &:hover {
        background-color: var(--primary-dark);
      }
    }

    &.btn-secondary {
      background-color: var(--bg);
      color: var(--black);
      border: 1px solid var(--border);

      &:hover {
        background-color: var(--grey-lightest);
      }
    }
  }

  .custom-select-wrapper {
    position: relative;

    select {
      appearance: none;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
      background-position: right 0.75rem center;
      background-repeat: no-repeat;
      background-size: 1.5em 1.5em;
      padding-right: 2.5rem;
    }
  }
</style> 