<script lang="ts">
  import Button from '$lib/components/Button.svelte';

  // Example icon function
  const PlusIcon = () => `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
  `;

  function handleClick() {
    console.log('Button clicked!');
  }
</script>

<div class="component-docs-container">
  <h1>Button Components</h1>
  <p class="description">Examples and usage of the Button component with different variants, sizes, and states.</p>

  <section>
    <h2>Variants</h2>
    <div class="button-row">
      <Button on:click={handleClick}>Primary Button</Button>
      <Button variant="secondary" on:click={handleClick}>Secondary Button</Button>
      <Button variant="tertiary" on:click={handleClick}>Tertiary Button</Button>
    </div>
  </section>

  <section>
    <h2>Sizes</h2>
    <div class="button-row">
      <Button size="small" on:click={handleClick}>Small Button</Button>
      <Button size="medium" on:click={handleClick}>Medium Button</Button>
      <Button on:click={handleClick}>Default Size</Button>
    </div>
  </section>

  <section>
    <h2>With Icons</h2>
    <div class="button-row">
      <Button icon={PlusIcon} on:click={handleClick}>Add Item</Button>
      <Button variant="secondary" icon={PlusIcon} on:click={handleClick}>Add Item</Button>
      <Button variant="tertiary" icon={PlusIcon} on:click={handleClick}>Add Item</Button>
    </div>
  </section>

  <section>
    <h2>Disabled State</h2>
    <div class="button-row">
      <Button disabled>Disabled Primary</Button>
      <Button variant="secondary" disabled>Disabled Secondary</Button>
      <Button variant="tertiary" disabled>Disabled Tertiary</Button>
    </div>
  </section>

  <section>
    <h2>Form Submission</h2>
    <form on:submit|preventDefault={() => alert('Form submitted!')}>
      <div class="form-row">
        <input type="text" placeholder="Enter some text..." />
        <Button type="submit">Submit Form</Button>
      </div>
    </form>
  </section>
</div>

<style lang="less">
  
    h1 {
      margin: 0 0 0.5rem 0;
      color: var(--black);
      font-size: 1.75rem;
    }

    .description {
      margin: 0 0 2rem 0;
      color: var(--grey);
      font-size: 1rem;
      line-height: 1.5;
    }
    
    section {
      background: var(--bg);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--border);
      
      h2 {
        color: var(--black);
        margin-top: 0;
        margin-bottom: 1.5rem;
        font-size: 1.25rem;
        font-weight: 600;
      }
    }
    
    .button-row {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      margin-bottom: 1.5rem;
      align-items: center;
    }

    .form-row {
      display: flex;
      gap: 1rem;
      align-items: center;
      flex-wrap: wrap;

      input {
        padding: 0.5rem 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;
        min-width: 200px;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }
  
</style> 