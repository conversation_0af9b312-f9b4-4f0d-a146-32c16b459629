<script lang="ts">
  import PageHeader from '$lib/components/PageHeader.svelte';
  import VerticalMenu from '$lib/components/VerticalMenu.svelte';
  import ButtonExamples from './ButtonExamples.svelte';
  import StatCardExamples from './StatCardExamples.svelte';
  import GridExamples from './GridExamples.svelte';
  import ModalExamples from './ModalExamples.svelte';
  import TabsExamples from './TabsExamples.svelte';
  import LoadingSpinnerExamples from './LoadingSpinnerExamples.svelte';
  import PageHeaderExamples from './PageHeaderExamples.svelte';
  import VerticalMenuExamples from './VerticalMenuExamples.svelte';
  import ToastsExamples from './ToastsExamples.svelte';
  import FormExamples from './FormExamples.svelte';

  // Component examples menu items
  const componentItems = [
    {
      id: 'buttons',
      label: 'Buttons'
    },
    {
      id: 'statcards',
      label: 'Stat Cards'
    },
    {
      id: 'grid',
      label: 'Grid'
    },
    {
      id: 'modal',
      label: 'Modal'
    },
    {
      id: 'tabs',
      label: 'Tabs'
    },
    {
      id: 'loading-spinner',
      label: 'Loading Spinner'
    },
    {
      id: 'page-header',
      label: 'Page Header'
    },
    {
      id: 'vertical-menu',
      label: 'Vertical Menu'
    },
    {
      id: 'toasts',
      label: 'Toasts'
    },
    {
      id: 'forms',
      label: 'Forms'
    }
  ];

  // Track selected component
  let currentItem = '';

  function handleMenuSelect(itemId: string) {
    currentItem = itemId;
  }
</script>

<svelte:head>
  <title>Components</title>
</svelte:head>

<div class="container">
  <PageHeader title="Components" />

  <main>
    <div class="vertical-sidebar-layout">
    
        <VerticalMenu
        items={componentItems}
        {currentItem}
        onSelect={handleMenuSelect}
        />
    
      

      <div class="vertical-sidebar-content">
        {#if !currentItem}
          <div class="welcome-section">
            <h2>Welcome to Components</h2>
            <p>Select a component from the menu to view examples and documentation.</p>
          </div>
        {:else if currentItem === 'buttons'}
          <ButtonExamples />
        {:else if currentItem === 'statcards'}
          <StatCardExamples />
        {:else if currentItem === 'grid'}
          <GridExamples />
        {:else if currentItem === 'modal'}
          <ModalExamples />
        {:else if currentItem === 'tabs'}
          <TabsExamples />
        {:else if currentItem === 'loading-spinner'}
          <LoadingSpinnerExamples />
        {:else if currentItem === 'page-header'}
          <PageHeaderExamples />
        {:else if currentItem === 'vertical-menu'}
          <VerticalMenuExamples />
        {:else if currentItem === 'toasts'}
          <ToastsExamples />
        {:else if currentItem === 'forms'}
          <FormExamples />
        {/if}
      </div>
    </div>
  </main>
</div>

<style lang="less">

  .welcome-section {
    text-align: center;
    padding: 4rem 2rem;

    h2 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    p {
      margin: 0;
      color: var(--grey);
    }
  }

  @media (max-width: 768px) {
    .components-layout {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .components-sidebar {
      order: 2;
    }

    .components-content {
      order: 1;
    }
  }
</style> 