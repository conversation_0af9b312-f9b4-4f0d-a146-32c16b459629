<script lang="ts">
  import Grid, { type SortState } from '$lib/components/Grid.svelte';

  // Define example data and headers for the Grid component
  const headers = [
    { key: 'id', text: 'ID', sortable: true },
    { key: 'name', text: 'Name', sortable: true },
    { key: 'age', text: 'Age', sortable: true },
  ];

  const dataRows = [
    { id: 1, name: '<PERSON>', age: 30 },
    { id: 2, name: '<PERSON>', age: 25 },
    { id: 3, name: '<PERSON>', age: 35 },
  ];

  // You can add state and functions for sorting, searching, pagination here
  let currentSort: SortState = { key: '', direction: '' };
  let searchFields = [
    { displayName: 'Name', queryKey: 'name', currentQuery: '' },
  ];
  let itemsPerPage = 10;
  let currentPage = 1;
  let totalItems = dataRows.length; // Use actual total from backend in a real app

  function handleHeaderClick(headerKey: string) {
    // Implement sorting logic here
    console.log('Sort by:', headerKey);
  }

  function handleSearch(searchQueries: Record<string, string>) {
    // Implement search logic here
    console.log('Search queries:', searchQueries);
  }

  function handleItemsPerPageChange(newItemsPerPage: number) {
    itemsPerPage = newItemsPerPage;
    currentPage = 1; // Reset to first page on items per page change
    console.log('Items per page:', itemsPerPage);
  }

  function handlePageChange(newPage: number) {
    currentPage = newPage;
    console.log('Current page:', currentPage);
  }

</script>

<div class="component-docs-container">
  <h1>Grid Components</h1>
  <p class="description">Examples and usage of the Grid component with different content types and configurations.</p>

<Grid
  {headers}
  {dataRows}
  {currentSort}
  onHeaderClick={handleHeaderClick}
  {searchFields}
  itemsPerPageOptions={[5, 10, 20]}
  {itemsPerPage}
  {currentPage}
  {totalItems}
  onSearch={handleSearch}
  onItemsPerPageChange={handleItemsPerPageChange}
  onPageChange={handlePageChange}
/>
</div>

<style lang="less">
  /* Add component-specific styles here */
</style> 