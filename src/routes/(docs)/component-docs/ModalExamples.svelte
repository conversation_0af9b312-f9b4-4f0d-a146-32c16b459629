<script lang="ts">
  import Modal from '$lib/components/Modal.svelte';
  import Button from '$lib/components/Button.svelte';

  let showBasicModal = false;
  let showCustomModal = false;
  let showFormModal = false;

  function openBasicModal() {
    showBasicModal = true;
  }

  function openCustomModal() {
    showCustomModal = true;
  }

  function openFormModal() {
    showFormModal = true;
  }

  function handleFormSubmit() {
    alert('Form submitted!');
    showFormModal = false;
  }
</script>

<div class="component-docs-container">
  <h1>Modal Components</h1>
  <p class="description">Examples and usage of the Modal component with different content types and configurations.</p>

  <section>
    <h2>Basic Modal</h2>
    <p>Simple modal with default content and close functionality.</p>
    <Button on:click={openBasicModal}>Open Basic Modal</Button>
    
    <Modal bind:show={showBasicModal} title="Basic Modal">
      <p>This is a basic modal with default styling and behavior.</p>
      <p>You can close it by clicking the X button, clicking outside, or using the close button in the footer.</p>
    </Modal>
  </section>

  <section>
    <h2>Custom Content Modal</h2>
    <p>Modal with custom content and footer actions.</p>
    <Button variant="secondary" on:click={openCustomModal}>Open Custom Modal</Button>
    
    <Modal bind:show={showCustomModal} title="Custom Content">
      <div class="custom-content">
        <h3>Custom Content Example</h3>
        <p>This modal contains custom content with different styling.</p>
        <ul>
          <li>Custom HTML content</li>
          <li>Lists and formatted text</li>
          <li>Multiple paragraphs</li>
        </ul>
        <p>The footer also has custom actions.</p>
      </div>
      
      <div slot="footer" class="custom-footer">
        <Button variant="tertiary" on:click={() => showCustomModal = false}>Cancel</Button>
        <Button on:click={() => { alert('Action performed!'); showCustomModal = false; }}>Perform Action</Button>
      </div>
    </Modal>
  </section>

  <section>
    <h2>Form Modal</h2>
    <p>Modal containing a form with validation and submission.</p>
    <Button variant="tertiary" on:click={openFormModal}>Open Form Modal</Button>
    
    <Modal bind:show={showFormModal} title="Contact Form">
      <form on:submit|preventDefault={handleFormSubmit} class="modal-form">
        <div class="form-group">
          <label for="name">Name:</label>
          <input type="text" id="name" required />
        </div>
        
        <div class="form-group">
          <label for="email">Email:</label>
          <input type="email" id="email" required />
        </div>
        
        <div class="form-group">
          <label for="message">Message:</label>
          <textarea id="message" rows="4" required></textarea>
        </div>
      </form>
      
      <div slot="footer" class="form-footer">
        <Button variant="tertiary" on:click={() => showFormModal = false}>Cancel</Button>
        <Button type="submit" on:click={handleFormSubmit}>Send Message</Button>
      </div>
    </Modal>
  </section>
</div>

<style lang="less">

    h1 {
      margin: 0 0 0.5rem 0;
      color: var(--black);
      font-size: 1.75rem;
    }

    .description {
      margin: 0 0 2rem 0;
      color: var(--grey);
      font-size: 1rem;
      line-height: 1.5;
    }
    
    section {
      background: var(--bg);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--border);
      
      h2 {
        color: var(--black);
        margin-top: 0;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
      }

      p {
        margin-bottom: 1rem;
        color: var(--grey);
        line-height: 1.5;
      }
    }

    .custom-content {
      h3 {
        color: var(--primary);
        margin-top: 0;
        margin-bottom: 1rem;
      }

      ul {
        margin: 1rem 0;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.5rem;
          color: var(--grey);
        }
      }
    }

    .custom-footer, .form-footer {
      display: flex;
      gap: 0.5rem;
      justify-content: flex-end;
    }

    .modal-form {
      .form-group {
        margin-bottom: 1rem;
        
        label {
          display: block;
          margin-bottom: 0.5rem;
          color: var(--black);
          font-weight: 500;
        }
        
        input, textarea {
          width: 100%;
          padding: 0.5rem 0.75rem;
          border: 1px solid var(--border);
          border-radius: var(--br);
          font-size: 0.9rem;
          font-family: inherit;
          
          &:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px var(--primary-fade);
          }
        }
        
        textarea {
          resize: vertical;
          min-height: 80px;
        }
      }
    }
  
</style> 