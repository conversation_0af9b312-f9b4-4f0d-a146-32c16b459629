<script lang="ts">
  import Tabs from '$lib/components/Tabs.svelte';

  interface Tab {
    id: string;
    label: string;
    disabled?: boolean;
  }

  // Basic tabs example
  const basicTabs: Tab[] = [
    { id: 'tab1', label: 'Tab 1' },
    { id: 'tab2', label: 'Tab 2' },
    { id: 'tab3', label: 'Tab 3' }
  ];
  let activeBasicTab = 'tab1';

  // Tabs with disabled state
  const disabledTabs: Tab[] = [
    { id: 'home', label: 'Home' },
    { id: 'about', label: 'About' },
    { id: 'contact', label: 'Contact', disabled: true },
    { id: 'services', label: 'Services' }
  ];
  let activeDisabledTab = 'home';

  // Content tabs example
  const contentTabs: Tab[] = [
    { id: 'overview', label: 'Overview' },
    { id: 'features', label: 'Features' },
    { id: 'pricing', label: 'Pricing' },
    { id: 'support', label: 'Support' }
  ];
  let activeContentTab = 'overview';

  function handleTabChange(event: CustomEvent<{ tabId: string }>) {
    console.log('Tab changed to:', event.detail.tabId);
  }
</script>

<div class="component-docs-container">
  <h1>Tabs Components</h1>
  <p class="description">Examples and usage of the Tabs component with different configurations and states.</p>

  <section>
    <h2>Basic Tabs</h2>
    <p>Simple tab navigation with basic functionality.</p>
    
    <Tabs 
      tabs={basicTabs} 
      bind:activeTab={activeBasicTab}
      on:change={handleTabChange}
    />
    
    <div class="tab-content">
      {#if activeBasicTab === 'tab1'}
        <div class="content-panel">
          <h3>Tab 1 Content</h3>
          <p>This is the content for the first tab. It contains some sample text to demonstrate how tab content works.</p>
        </div>
      {:else if activeBasicTab === 'tab2'}
        <div class="content-panel">
          <h3>Tab 2 Content</h3>
          <p>This is the content for the second tab. Each tab can have completely different content and layout.</p>
          <ul>
            <li>List item 1</li>
            <li>List item 2</li>
            <li>List item 3</li>
          </ul>
        </div>
      {:else if activeBasicTab === 'tab3'}
        <div class="content-panel">
          <h3>Tab 3 Content</h3>
          <p>This is the content for the third tab. You can include any type of content here.</p>
          <button class="demo-button">Sample Button</button>
        </div>
      {/if}
    </div>
  </section>

  <section>
    <h2>Tabs with Disabled State</h2>
    <p>Tabs can be disabled to prevent user interaction.</p>
    
    <Tabs 
      tabs={disabledTabs} 
      bind:activeTab={activeDisabledTab}
      on:change={handleTabChange}
    />
    
    <div class="tab-content">
      {#if activeDisabledTab === 'home'}
        <div class="content-panel">
          <h3>Home</h3>
          <p>Welcome to the home page. Notice that the "Contact" tab is disabled and cannot be clicked.</p>
        </div>
      {:else if activeDisabledTab === 'about'}
        <div class="content-panel">
          <h3>About</h3>
          <p>Learn more about our company and mission. The disabled tab demonstrates how to prevent access to certain sections.</p>
        </div>
      {:else if activeDisabledTab === 'services'}
        <div class="content-panel">
          <h3>Services</h3>
          <p>Explore our range of services and offerings.</p>
        </div>
      {/if}
    </div>
  </section>

  <section>
    <h2>Content-Rich Tabs</h2>
    <p>Tabs with more complex content and styling.</p>
    
    <Tabs 
      tabs={contentTabs} 
      bind:activeTab={activeContentTab}
      on:change={handleTabChange}
    />
    
    <div class="tab-content">
      {#if activeContentTab === 'overview'}
        <div class="content-panel rich-content">
          <h3>Product Overview</h3>
          <p>Our product is designed to solve complex problems with simple, elegant solutions.</p>
          <div class="feature-grid">
            <div class="feature-item">
              <h4>Fast</h4>
              <p>Lightning-fast performance</p>
            </div>
            <div class="feature-item">
              <h4>Secure</h4>
              <p>Enterprise-grade security</p>
            </div>
            <div class="feature-item">
              <h4>Scalable</h4>
              <p>Grows with your business</p>
            </div>
          </div>
        </div>
      {:else if activeContentTab === 'features'}
        <div class="content-panel rich-content">
          <h3>Key Features</h3>
          <ul class="feature-list">
            <li>✅ Real-time collaboration</li>
            <li>✅ Advanced analytics</li>
            <li>✅ Custom integrations</li>
            <li>✅ 24/7 support</li>
            <li>✅ Mobile-first design</li>
          </ul>
        </div>
      {:else if activeContentTab === 'pricing'}
        <div class="content-panel rich-content">
          <h3>Pricing Plans</h3>
          <div class="pricing-grid">
            <div class="pricing-card">
              <h4>Starter</h4>
              <div class="price">$9/month</div>
              <p>Perfect for small teams</p>
            </div>
            <div class="pricing-card">
              <h4>Professional</h4>
              <div class="price">$29/month</div>
              <p>For growing businesses</p>
            </div>
            <div class="pricing-card">
              <h4>Enterprise</h4>
              <div class="price">Custom</div>
              <p>For large organizations</p>
            </div>
          </div>
        </div>
      {:else if activeContentTab === 'support'}
        <div class="content-panel rich-content">
          <h3>Support & Resources</h3>
          <p>Get help when you need it with our comprehensive support options.</p>
          <div class="support-options">
            <div class="support-item">
              <h4>📚 Documentation</h4>
              <p>Comprehensive guides and tutorials</p>
            </div>
            <div class="support-item">
              <h4>💬 Live Chat</h4>
              <p>Real-time support during business hours</p>
            </div>
            <div class="support-item">
              <h4>📧 Email Support</h4>
              <p>Get help via email within 24 hours</p>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </section>
</div>

<style lang="less">
    
    section {
      background: var(--bg);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--border);
      
      h2 {
        color: var(--black);
        margin-top: 0;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
      }

      p {
        margin-bottom: 1.5rem;
        color: var(--grey);
        line-height: 1.5;
      }
    }

    .tab-content {
      margin-top: 1.5rem;
    }

    .content-panel {
      padding: 1.5rem;
      background: white;
      border-radius: 6px;
      border: 1px solid var(--border);

      h3 {
        margin-top: 0;
        margin-bottom: 1rem;
        color: var(--primary);
      }

      h4 {
        margin-top: 0;
        margin-bottom: 0.5rem;
        color: var(--black);
      }

      p {
        margin-bottom: 1rem;
        color: var(--grey);
        line-height: 1.5;
      }

      ul {
        margin: 1rem 0;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.5rem;
          color: var(--grey);
        }
      }

      .demo-button {
        background: var(--primary);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: var(--br);
        cursor: pointer;
        font-size: 0.9rem;

        &:hover {
          background: var(--primary-dark);
        }
      }
    }

    .rich-content {
      .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
      }

      .feature-item {
        padding: 1rem;
        background: var(--bg);
        border-radius: 6px;
        text-align: center;

        h4 {
          color: var(--primary);
          margin-bottom: 0.5rem;
        }

        p {
          margin: 0;
          font-size: 0.9rem;
        }
      }

      .feature-list {
        list-style: none;
        padding: 0;

        li {
          padding: 0.5rem 0;
          font-size: 1rem;
          color: var(--black);
        }
      }

      .pricing-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
      }

      .pricing-card {
        padding: 1.5rem;
        background: var(--bg);
        border-radius: 8px;
        text-align: center;
        border: 1px solid var(--border);

        h4 {
          color: var(--primary);
          margin-bottom: 1rem;
        }

        .price {
          font-size: 1.5rem;
          font-weight: bold;
          color: var(--black);
          margin-bottom: 0.5rem;
        }

        p {
          margin: 0;
          font-size: 0.9rem;
        }
      }

      .support-options {
        display: grid;
        gap: 1rem;
        margin-top: 1.5rem;
      }

      .support-item {
        padding: 1rem;
        background: var(--bg);
        border-radius: 6px;

        h4 {
          color: var(--primary);
          margin-bottom: 0.5rem;
        }

        p {
          margin: 0;
          font-size: 0.9rem;
        }
      }
    }
  
</style> 