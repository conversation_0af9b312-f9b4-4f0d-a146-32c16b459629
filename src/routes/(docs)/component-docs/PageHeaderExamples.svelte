<script lang="ts">
  import PageHeader from '$lib/components/PageHeader.svelte';
  import Button from '$lib/components/Button.svelte';

  function handleAction() {
    alert('Action clicked!');
  }
</script>

<div class="component-docs-container">
  <h1>Page Header Components</h1>
  <p class="description">Examples and usage of the PageHeader component with different configurations and action buttons.</p>

  <section>
    <h2>Basic Page Header</h2>
    <p>Simple page header with just a title.</p>
    
    <div class="example-container">
      <PageHeader title="Dashboard" />
    </div>
  </section>

  <section>
    <h2>Page Header with Single Action</h2>
    <p>Page header with a single action button in the actions slot.</p>
    
    <div class="example-container">
      <PageHeader title="User Management">
        <div slot="actions">
          <Button on:click={handleAction}>Add User</Button>
        </div>
      </PageHeader>
    </div>
  </section>

  <section>
    <h2>Page Header with Multiple Actions</h2>
    <p>Page header with multiple action buttons showing different variants.</p>
    
    <div class="example-container">
      <PageHeader title="Project Settings">
        <div slot="actions">
          <Button variant="tertiary" on:click={handleAction}>Cancel</Button>
          <Button variant="secondary" on:click={handleAction}>Save Draft</Button>
          <Button on:click={handleAction}>Publish</Button>
        </div>
      </PageHeader>
    </div>
  </section>

  <section>
    <h2>Page Header with Mixed Actions</h2>
    <p>Page header with a mix of buttons and other action elements.</p>
    
    <div class="example-container">
      <PageHeader title="Analytics Dashboard">
        <div slot="actions" class="mixed-actions">
          <select class="action-select">
            <option>Last 7 days</option>
            <option>Last 30 days</option>
            <option>Last 90 days</option>
          </select>
          <Button variant="secondary" on:click={handleAction}>Export</Button>
          <Button on:click={handleAction}>Refresh</Button>
        </div>
      </PageHeader>
    </div>
  </section>

  <section>
    <h2>Different Title Lengths</h2>
    <p>Examples showing how the component handles different title lengths.</p>
    
    <div class="title-examples">
      <div class="example-container">
        <PageHeader title="Home" />
      </div>
      
      <div class="example-container">
        <PageHeader title="Customer Relationship Management" />
      </div>
      
      <div class="example-container">
        <PageHeader title="Advanced Analytics and Reporting Dashboard with Real-time Data">
          <div slot="actions">
            <Button size="small" on:click={handleAction}>Action</Button>
          </div>
        </PageHeader>
      </div>
    </div>
  </section>

  <section>
    <h2>Real-world Examples</h2>
    <p>Examples of how page headers might appear in different application contexts.</p>
    
    <div class="context-examples">
      <div class="context-example">
        <h4>E-commerce Admin</h4>
        <PageHeader title="Products">
          <div slot="actions">
            <Button variant="tertiary" on:click={handleAction}>Import</Button>
            <Button on:click={handleAction}>Add Product</Button>
          </div>
        </PageHeader>
      </div>
      
      <div class="context-example">
        <h4>Content Management</h4>
        <PageHeader title="Blog Posts">
          <div slot="actions">
            <Button variant="secondary" on:click={handleAction}>Drafts (3)</Button>
            <Button on:click={handleAction}>New Post</Button>
          </div>
        </PageHeader>
      </div>
      
      <div class="context-example">
        <h4>Team Management</h4>
        <PageHeader title="Team Members">
          <div slot="actions">
            <Button variant="tertiary" on:click={handleAction}>Invite Link</Button>
            <Button on:click={handleAction}>Invite Member</Button>
          </div>
        </PageHeader>
      </div>
    </div>
  </section>
</div>

<style lang="less">
    section {
      background: var(--bg);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--border);
      
      h2 {
        color: var(--black);
        margin-top: 0;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
      }

      h4 {
        margin-top: 0;
        margin-bottom: 1rem;
        color: var(--primary);
        font-size: 1rem;
      }

      p {
        margin-bottom: 1.5rem;
        color: var(--grey);
        line-height: 1.5;
      }
    }

    .example-container {
      background: white;
      border-radius: 8px;
      border: 1px solid var(--border);
      overflow: hidden;
      margin-bottom: 1rem;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .title-examples {
      .example-container {
        margin-bottom: 1.5rem;
      }
    }

    .mixed-actions {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .action-select {
        padding: 0.5rem 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;
        background: white;
        color: var(--black);

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }

    .context-examples {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .context-example {
      h4 {
        margin-bottom: 1rem;
        color: var(--primary);
      }

      .example-container {
        margin-bottom: 0;
      }
    }
  

  // Override some PageHeader styles for better example display
  :global(.page-header-examples .page-header) {
    padding: 1rem 1.5rem;
  }
</style> 