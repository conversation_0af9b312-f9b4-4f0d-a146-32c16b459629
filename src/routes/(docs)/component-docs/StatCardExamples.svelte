<script lang="ts">
  import StatCard from '$lib/components/StatCard.svelte';
</script>

<div class="component-docs-container">
  <h1>Stat Card Components</h1>
  <p class="description">A reusable component for displaying statistics with a title and value.</p>

  <h3>Basic Usage</h3>
  <div class="example">
    <div class="stats-grid">
      <StatCard title="Total Users" value={1234} />
      <StatCard title="Revenue" value="$45,678" />
      <StatCard title="Orders" value={89} />
    </div>
  </div>

  <h3>With Special Styling</h3>
  <div class="example">
    <div class="stats-grid">
      <StatCard title="Overdue Items" value={5} valueClass="overdue" />
      <StatCard title="Active Users" value={2456} />
      <StatCard title="Pending" value={12} />
    </div>
  </div>

  <h3>Props</h3>
  <div class="props-table">
    <div class="prop-row">
      <div class="prop-name">title</div>
      <div class="prop-type">string</div>
      <div class="prop-description">The title/label for the statistic</div>
    </div>
    <div class="prop-row">
      <div class="prop-name">value</div>
      <div class="prop-type">string | number</div>
      <div class="prop-description">The value to display</div>
    </div>
    <div class="prop-row">
      <div class="prop-name">valueClass</div>
      <div class="prop-type">string | undefined</div>
      <div class="prop-description">Optional CSS class for special styling (e.g., "overdue")</div>
    </div>
  </div>
</div>

<style lang="less">
  .example {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid var(--border);
    border-radius: var(--br);
    background: var(--bg);
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

 
</style> 