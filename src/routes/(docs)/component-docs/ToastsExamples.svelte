<script lang="ts">
  import Toasts from '$lib/components/Toasts.svelte';
  import Button from '$lib/components/Button.svelte';
  import { addToast } from '$lib/stores/toastStore';

  function showSuccessToast() {
    addToast({
      type: 'success',
      message: 'Operation completed successfully!'
    });
  }

  function showErrorToast() {
    addToast({
      type: 'error',
      message: 'An error occurred while processing your request.'
    });
  }

  function showWarningToast() {
    addToast({
      type: 'warning',
      message: 'Please review your input before proceeding.'
    });
  }

  function showInfoToast() {
    addToast({
      type: 'info',
      message: 'Here is some helpful information for you.'
    });
  }

  function showLongToast() {
    addToast({
      type: 'info',
      message: 'This is a longer toast message that demonstrates how the component handles multiple lines of text and wrapping content gracefully.',
      duration: 5000
    });
  }

  function showCustomDurationToast() {
    addToast({
      type: 'success',
      message: 'This toast will disappear in 10 seconds.',
      duration: 10000
    });
  }

  function showMultipleToasts() {
    addToast({
      type: 'info',
      message: 'First toast message'
    });
    
    setTimeout(() => {
      addToast({
        type: 'success',
        message: 'Second toast message'
      });
    }, 500);
    
    setTimeout(() => {
      addToast({
        type: 'warning',
        message: 'Third toast message'
      });
    }, 1000);
  }

  function simulateFormSubmission() {
    addToast({
      type: 'info',
      message: 'Submitting form...'
    });

    setTimeout(() => {
      addToast({
        type: 'success',
        message: 'Form submitted successfully!'
      });
    }, 2000);
  }

  function simulateError() {
    addToast({
      type: 'info',
      message: 'Processing request...'
    });

    setTimeout(() => {
      addToast({
        type: 'error',
        message: 'Network error: Unable to connect to server.'
      });
    }, 2000);
  }
</script>

<div class="component-docs-container">
  <h1>Toasts Components</h1>
  <p class="description">Examples and usage of the Toasts component for displaying notifications and feedback messages.</p>

  <section>
    <h2>Basic Toast Types</h2>
    <p>Different types of toast notifications for various scenarios.</p>
    
    <div class="button-grid">
      <Button on:click={showSuccessToast} variant="secondary">
        Show Success Toast
      </Button>
      
      <Button on:click={showErrorToast} variant="secondary">
        Show Error Toast
      </Button>
      
      <Button on:click={showWarningToast} variant="secondary">
        Show Warning Toast
      </Button>
      
      <Button on:click={showInfoToast} variant="secondary">
        Show Info Toast
      </Button>
    </div>
  </section>

  <section>
    <h2>Custom Duration</h2>
    <p>Toasts with custom display durations.</p>
    
    <div class="button-grid">
      <Button on:click={showLongToast} variant="tertiary">
        Long Message (5s)
      </Button>
      
      <Button on:click={showCustomDurationToast} variant="tertiary">
        Custom Duration (10s)
      </Button>
    </div>
  </section>

  <section>
    <h2>Multiple Toasts</h2>
    <p>Demonstrate how multiple toasts stack and display.</p>
    
    <div class="button-grid">
      <Button on:click={showMultipleToasts}>
        Show Multiple Toasts
      </Button>
    </div>
  </section>

  <section>
    <h2>Real-world Examples</h2>
    <p>Examples of how toasts might be used in real application scenarios.</p>
    
    <div class="scenario-examples">
      <div class="scenario-card">
        <h4>Form Submission</h4>
        <p>Simulate a form submission with progress and success feedback.</p>
        <Button on:click={simulateFormSubmission} variant="secondary">
          Submit Form
        </Button>
      </div>
      
      <div class="scenario-card">
        <h4>Error Handling</h4>
        <p>Simulate a network error with appropriate error messaging.</p>
        <Button on:click={simulateError} variant="secondary">
          Trigger Error
        </Button>
      </div>
      
      <div class="scenario-card">
        <h4>User Actions</h4>
        <p>Common user action feedback messages.</p>
        <div class="action-buttons">
          <Button size="small" on:click={() => addToast({ type: 'success', message: 'Item saved to favorites!' })}>
            Save to Favorites
          </Button>
          <Button size="small" on:click={() => addToast({ type: 'info', message: 'Item copied to clipboard!' })}>
            Copy Link
          </Button>
          <Button size="small" on:click={() => addToast({ type: 'warning', message: 'Item removed from cart.' })}>
            Remove Item
          </Button>
        </div>
      </div>
    </div>
  </section>

  <section>
    <h2>Toast Guidelines</h2>
    <div class="guidelines">
      <div class="guideline-item">
        <h4>✅ Success Toasts</h4>
        <p>Use for successful operations, confirmations, and positive feedback.</p>
      </div>
      
      <div class="guideline-item">
        <h4>❌ Error Toasts</h4>
        <p>Use for errors, failures, and critical issues that need user attention.</p>
      </div>
      
      <div class="guideline-item">
        <h4>⚠️ Warning Toasts</h4>
        <p>Use for warnings, cautions, and situations requiring user awareness.</p>
      </div>
      
      <div class="guideline-item">
        <h4>ℹ️ Info Toasts</h4>
        <p>Use for general information, tips, and neutral notifications.</p>
      </div>
    </div>
  </section>
</div>

<!-- Include the Toasts component to display notifications -->
<Toasts />

<style lang="less">
    h1 {
      margin: 0 0 0.5rem 0;
      color: var(--black);
      font-size: 1.75rem;
    }

    .description {
      margin: 0 0 2rem 0;
      color: var(--grey);
      font-size: 1rem;
      line-height: 1.5;
    }
    
    section {
      background: var(--bg);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--border);
      
      h2 {
        color: var(--black);
        margin-top: 0;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
      }

      h4 {
        margin-top: 0;
        margin-bottom: 0.5rem;
        color: var(--primary);
        font-size: 1rem;
      }

      p {
        margin-bottom: 1.5rem;
        color: var(--grey);
        line-height: 1.5;
      }
    }

    .button-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }

    .scenario-examples {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }

    .scenario-card {
      background: white;
      border-radius: 8px;
      border: 1px solid var(--border);
      padding: 1.5rem;

      h4 {
        margin-bottom: 0.5rem;
        color: var(--primary);
      }

      p {
        margin-bottom: 1rem;
        font-size: 0.9rem;
      }

      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }
    }

    .guidelines {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
    }

    .guideline-item {
      background: white;
      border-radius: 8px;
      border: 1px solid var(--border);
      padding: 1rem;

      h4 {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
      }

      p {
        margin: 0;
        font-size: 0.85rem;
        line-height: 1.4;
      }
    }
</style> 