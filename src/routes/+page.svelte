<script lang="ts">
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { user } from '$lib/stores/auth';

  let showTest = true;

  onMount(() => {
    // Check if user is logged in
    const unsubscribe = user.subscribe(value => {
      if (value) {
        goto('/dashboard');
      } else {
        goto('/login');
      }
    });

    return unsubscribe;
  });
</script>

<div class="loading">
  <p>Loading...</p>
</div>

<style lang="less">
  .loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 1.2rem;
    color: var(--grey);
  }
</style>
