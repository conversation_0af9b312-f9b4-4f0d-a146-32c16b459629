// Job Store - Comprehensive job management
import { writable, derived } from 'svelte/store';
import type { Job, JobStatus, JobType } from '$lib/api/jobs';
import { 
  getJobs, 
  createJob, 
  updateJob, 
  deleteJob, 
  getJobStatuses, 
  getJobTypes,
  createJobType,
  updateJobStatus,
  getJobsByCustomer,
  getUninvoicedJobs
} from '$lib/api/jobs';

// Initialize with sample job types if none exist
async function initializeSampleJobTypes() {
  const existingTypes = await getJobTypes();
  if (existingTypes.length === 0) {
    const sampleJobTypes = [
      {
        name: 'Plumbing',
        description: 'General plumbing services',
        defaultDuration: 120, // 2 hours
        defaultFields: [],
        isActive: true
      },
      {
        name: 'Electrical',
        description: 'Electrical installation and repair',
        defaultDuration: 180, // 3 hours
        defaultFields: [],
        isActive: true
      },
      {
        name: 'HVAC',
        description: 'Heating, ventilation, and air conditioning',
        defaultDuration: 240, // 4 hours
        defaultFields: [],
        isActive: true
      },
      {
        name: 'General Maintenance',
        description: 'General maintenance and repair work',
        defaultDuration: 90, // 1.5 hours
        defaultFields: [],
        isActive: true
      },
      {
        name: 'Inspection',
        description: 'Property inspection services',
        defaultDuration: 60, // 1 hour
        defaultFields: [],
        isActive: true
      }
    ];

    for (const jobType of sampleJobTypes) {
      await createJobType(jobType);
    }
  }
}

// Store for all jobs
export const jobs = writable<Job[]>([]);

// Store for job statuses (Kanban pipeline)
export const jobStatuses = writable<JobStatus[]>([]);

// Store for job types
export const jobTypes = writable<JobType[]>([]);

// Store for loading states
export const jobsLoading = writable<boolean>(false);
export const jobStatusesLoading = writable<boolean>(false);
export const jobTypesLoading = writable<boolean>(false);

// Store for selected job
export const selectedJob = writable<Job | null>(null);

// Store for job modal state
export const jobModalOpen = writable<boolean>(false);
export const jobModalMode = writable<'create' | 'edit'>('create');

// Derived stores for different job views
export const jobsByStatus = derived([jobs, jobStatuses], ([$jobs, $jobStatuses]) => {
  const grouped: Record<string, Job[]> = {};
  
  $jobStatuses.forEach(status => {
    grouped[status.id] = $jobs.filter(job => job.status.id === status.id);
  });
  
  return grouped;
});

export const completedJobs = derived(jobs, ($jobs) => 
  $jobs.filter(job => job.status.isCompleted)
);

export const activeJobs = derived(jobs, ($jobs) => 
  $jobs.filter(job => !job.status.isCompleted)
);

export const urgentJobs = derived(jobs, ($jobs) => 
  $jobs.filter(job => job.priority === 'Urgent' || job.priority === 'High')
);

// Store functions
export const jobStore = {
  // Load all jobs
  async loadJobs() {
    jobsLoading.set(true);
    try {
      const jobList = await getJobs();
      jobs.set(jobList);
    } catch (error) {
      console.error('Error loading jobs:', error);
    } finally {
      jobsLoading.set(false);
    }
  },

  // Load job statuses
  async loadJobStatuses() {
    jobStatusesLoading.set(true);
    try {
      const statusList = await getJobStatuses();
      jobStatuses.set(statusList);
    } catch (error) {
      console.error('Error loading job statuses:', error);
    } finally {
      jobStatusesLoading.set(false);
    }
  },

  // Load job types
  async loadJobTypes() {
    jobTypesLoading.set(true);
    try {
      await initializeSampleJobTypes(); // Initialize sample data if needed
      const typeList = await getJobTypes();
      jobTypes.set(typeList);
    } catch (error) {
      console.error('Error loading job types:', error);
    } finally {
      jobTypesLoading.set(false);
    }
  },

  // Add new job
  async addJob(jobData: Omit<Job, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      const newJob = await createJob(jobData);
      jobs.update(list => [...list, newJob]);
      return newJob;
    } catch (error) {
      console.error('Error adding job:', error);
      throw error;
    }
  },

  // Update existing job
  async updateJob(id: string, updates: Partial<Job>) {
    try {
      const updatedJob = await updateJob(id, updates);
      jobs.update(list => 
        list.map(job => job.id === id ? updatedJob : job)
      );
      
      // Update selected job if it's the one being updated
      selectedJob.update(selected => 
        selected?.id === id ? updatedJob : selected
      );
      
      return updatedJob;
    } catch (error) {
      console.error('Error updating job:', error);
      throw error;
    }
  },

  // Update job status
  async updateJobStatus(jobId: string, statusId: string) {
    try {
      const updatedJob = await updateJobStatus(jobId, statusId);
      jobs.update(list => 
        list.map(job => job.id === jobId ? updatedJob : job)
      );
      
      selectedJob.update(selected => 
        selected?.id === jobId ? updatedJob : selected
      );
      
      return updatedJob;
    } catch (error) {
      console.error('Error updating job status:', error);
      throw error;
    }
  },

  // Delete job
  async deleteJob(id: string) {
    try {
      await deleteJob(id);
      jobs.update(list => list.filter(job => job.id !== id));
      
      // Clear selected job if it's the one being deleted
      selectedJob.update(selected => 
        selected?.id === id ? null : selected
      );
    } catch (error) {
      console.error('Error deleting job:', error);
      throw error;
    }
  },

  // Select a job
  selectJob(job: Job | null) {
    selectedJob.set(job);
  },

  // Open job modal
  openJobModal(mode: 'create' | 'edit' = 'create', job?: Job) {
    jobModalMode.set(mode);
    if (mode === 'edit' && job) {
      selectedJob.set(job);
    } else {
      selectedJob.set(null);
    }
    jobModalOpen.set(true);
  },

  // Close job modal
  closeJobModal() {
    jobModalOpen.set(false);
    selectedJob.set(null);
  },

  // Get jobs by customer
  async getJobsByCustomer(customerId: string) {
    try {
      return await getJobsByCustomer(customerId);
    } catch (error) {
      console.error('Error getting jobs by customer:', error);
      throw error;
    }
  },

  // Get uninvoiced jobs for a customer
  async getUninvoicedJobs(customerId: string) {
    try {
      return await getUninvoicedJobs(customerId);
    } catch (error) {
      console.error('Error getting uninvoiced jobs:', error);
      throw error;
    }
  },

  // Search jobs
  searchJobs(query: string) {
    return derived(jobs, ($jobs) => 
      $jobs.filter(job => 
        job.title.toLowerCase().includes(query.toLowerCase()) ||
        job.description.toLowerCase().includes(query.toLowerCase()) ||
        job.customerName?.toLowerCase().includes(query.toLowerCase()) ||
        job.jobType.toLowerCase().includes(query.toLowerCase())
      )
    );
  },

  // Filter jobs by status
  filterJobsByStatus(statusId: string) {
    return derived(jobs, ($jobs) => 
      $jobs.filter(job => job.status.id === statusId)
    );
  },

  // Filter jobs by priority
  filterJobsByPriority(priority: string) {
    return derived(jobs, ($jobs) => 
      $jobs.filter(job => job.priority === priority)
    );
  }
}; 