// Staff Store - Comprehensive staff management
import { writable, derived } from 'svelte/store';
import type { Staff } from '$lib/api/staff';
import {
  getStaff,
  createStaff,
  updateStaff,
  deleteStaff,
  getActiveStaff
} from '$lib/api/staff';

// Initialize with sample staff if none exist
async function initializeSampleStaff() {
  const existingStaff = await getStaff();
  if (existingStaff.length === 0) {
    const sampleStaff = [
      {
        firstName: 'Tom',
        lastName: 'Anderson',
        email: '<EMAIL>',
        phone: '(*************',
        position: 'Senior Technician',
        department: 'Field Services',
        hireDate: '2022-01-15',
        isActive: true,
        wageInfo: {
          type: 'Hourly' as const,
          rate: 28.00,
          currency: 'GBP',
          effectiveDate: '2022-01-15',
          overtimeRate: 42.00
        },
        wageHistory: [],
        skills: ['Plumbing', 'Electrical', 'HVAC'],
        certifications: [],
        availability: {
          monday: { isAvailable: true, startTime: '08:00', endTime: '17:00' },
          tuesday: { isAvailable: true, startTime: '08:00', endTime: '17:00' },
          wednesday: { isAvailable: true, startTime: '08:00', endTime: '17:00' },
          thursday: { isAvailable: true, startTime: '08:00', endTime: '17:00' },
          friday: { isAvailable: true, startTime: '08:00', endTime: '17:00' },
          saturday: { isAvailable: false },
          sunday: { isAvailable: false },
          timeOff: []
        }
      },
      {
        firstName: 'Lisa',
        lastName: 'Rodriguez',
        email: '<EMAIL>',
        phone: '(*************',
        position: 'Technician',
        department: 'Field Services',
        hireDate: '2022-06-01',
        isActive: true,
        wageInfo: {
          type: 'Hourly' as const,
          rate: 28.00,
          currency: 'USD',
          effectiveDate: '2022-06-01',
          overtimeRate: 42.00
        },
        wageHistory: [],
        skills: ['General Maintenance', 'Inspection'],
        certifications: [],
        availability: {
          monday: { isAvailable: true, startTime: '09:00', endTime: '18:00' },
          tuesday: { isAvailable: true, startTime: '09:00', endTime: '18:00' },
          wednesday: { isAvailable: true, startTime: '09:00', endTime: '18:00' },
          thursday: { isAvailable: true, startTime: '09:00', endTime: '18:00' },
          friday: { isAvailable: true, startTime: '09:00', endTime: '18:00' },
          saturday: { isAvailable: true, startTime: '10:00', endTime: '15:00' },
          sunday: { isAvailable: false },
          timeOff: []
        }
      },
      {
        firstName: 'David',
        lastName: 'Chen',
        email: '<EMAIL>',
        phone: '(*************',
        position: 'Lead Electrician',
        department: 'Electrical',
        hireDate: '2021-03-10',
        isActive: true,
        wageInfo: {
          type: 'Hourly' as const,
          rate: 42.00,
          currency: 'USD',
          effectiveDate: '2021-03-10',
          overtimeRate: 63.00
        },
        wageHistory: [],
        skills: ['Electrical', 'Industrial Electrical', 'Safety'],
        certifications: [],
        availability: {
          monday: { isAvailable: true, startTime: '07:00', endTime: '16:00' },
          tuesday: { isAvailable: true, startTime: '07:00', endTime: '16:00' },
          wednesday: { isAvailable: true, startTime: '07:00', endTime: '16:00' },
          thursday: { isAvailable: true, startTime: '07:00', endTime: '16:00' },
          friday: { isAvailable: true, startTime: '07:00', endTime: '16:00' },
          saturday: { isAvailable: false },
          sunday: { isAvailable: false },
          timeOff: []
        }
      }
    ];

    for (const staff of sampleStaff) {
      await createStaff(staff);
    }
  }
}

// Store for all staff
export const staff = writable<Staff[]>([]);

// Store for loading state
export const staffLoading = writable<boolean>(false);

// Store for selected staff member
export const selectedStaff = writable<Staff | null>(null);

// Derived store for active staff only
export const activeStaff = derived(staff, ($staff) =>
  $staff.filter(s => s.isActive)
);

// Store functions
export const staffStore = {
  // Load all staff
  async loadStaff() {
    staffLoading.set(true);
    try {
      await initializeSampleStaff(); // Initialize sample data if needed
      const staffList = await getStaff();
      staff.set(staffList);
    } catch (error) {
      console.error('Error loading staff:', error);
    } finally {
      staffLoading.set(false);
    }
  },

  // Load active staff only
  async loadActiveStaff() {
    staffLoading.set(true);
    try {
      await initializeSampleStaff(); // Initialize sample data if needed
      const activeStaffList = await getActiveStaff();
      staff.set(activeStaffList);
    } catch (error) {
      console.error('Error loading active staff:', error);
    } finally {
      staffLoading.set(false);
    }
  },

  // Add new staff member
  async addStaff(staffData: Omit<Staff, 'id' | 'fullName' | 'createdAt' | 'updatedAt'>) {
    try {
      const newStaff = await createStaff(staffData);
      staff.update(list => [...list, newStaff]);
      return newStaff;
    } catch (error) {
      console.error('Error adding staff:', error);
      throw error;
    }
  },

  // Update existing staff member
  async updateStaff(id: string, updates: Partial<Staff>) {
    try {
      const updatedStaff = await updateStaff(id, updates);
      staff.update(list =>
        list.map(s => s.id === id ? updatedStaff : s)
      );

      // Update selected staff if it's the one being updated
      selectedStaff.update(selected =>
        selected?.id === id ? updatedStaff : selected
      );

      return updatedStaff;
    } catch (error) {
      console.error('Error updating staff:', error);
      throw error;
    }
  },

  // Delete staff member
  async deleteStaff(id: string) {
    try {
      await deleteStaff(id);
      staff.update(list => list.filter(s => s.id !== id));

      // Clear selected staff if it's the one being deleted
      selectedStaff.update(selected =>
        selected?.id === id ? null : selected
      );
    } catch (error) {
      console.error('Error deleting staff:', error);
      throw error;
    }
  },

  // Select a staff member
  selectStaff(staffMember: Staff | null) {
    selectedStaff.set(staffMember);
  },

  // Search staff
  searchStaff(query: string) {
    return derived(staff, ($staff) =>
      $staff.filter(s =>
        s.fullName.toLowerCase().includes(query.toLowerCase()) ||
        s.email.toLowerCase().includes(query.toLowerCase()) ||
        s.position.toLowerCase().includes(query.toLowerCase()) ||
        s.skills.some(skill => skill.toLowerCase().includes(query.toLowerCase()))
      )
    );
  }
};