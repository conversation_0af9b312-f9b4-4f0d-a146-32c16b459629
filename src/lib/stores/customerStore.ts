// Customer Store - Comprehensive contact/customer management
import { writable, derived } from 'svelte/store';
import type { Contact } from '$lib/api/contacts';
import { getContacts, createContact, updateContact, deleteContact } from '$lib/api/contacts';

// Initialize with sample data if none exists
async function initializeSampleData() {
  const existingContacts = await getContacts();
  if (existingContacts.length === 0) {
    // Create sample customers
    const sampleCustomers: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        fullName: '<PERSON>',
        companyName: 'Smith Construction',
        emails: [{ id: '1', email: '<EMAIL>', type: 'Work', isPrimary: true }],
        phones: [{ id: '1', phone: '(*************', type: 'Work', isPrimary: true }],
        addresses: [{
          id: '1',
          street: '123 Main St',
          city: 'Springfield',
          state: 'IL',
          zipCode: '62701',
          country: 'US',
          type: 'Work',
          isPrimary: true
        }],
        status: 'Customer',
        notes: [],
        checklists: [],
        communicationTimeline: []
      },
      {
        fullName: '<PERSON>',
        companyName: 'Johnson Enterprises',
        emails: [{ id: '1', email: '<EMAIL>', type: 'Work', isPrimary: true }],
        phones: [{ id: '1', phone: '(*************', type: 'Work', isPrimary: true }],
        addresses: [{
          id: '1',
          street: '456 Oak Ave',
          city: 'Springfield',
          state: 'IL',
          zipCode: '62702',
          country: 'US',
          type: 'Work',
          isPrimary: true
        }],
        status: 'Customer',
        notes: [],
        checklists: [],
        communicationTimeline: []
      },
      {
        fullName: 'Mike Wilson',
        emails: [{ id: '1', email: '<EMAIL>', type: 'Personal', isPrimary: true }],
        phones: [{ id: '1', phone: '(*************', type: 'Mobile', isPrimary: true }],
        addresses: [{
          id: '1',
          street: '789 Pine St',
          city: 'Springfield',
          state: 'IL',
          zipCode: '62703',
          country: 'US',
          type: 'Home',
          isPrimary: true
        }],
        status: 'Lead',
        notes: [],
        checklists: [],
        communicationTimeline: []
      }
    ];

    for (const customer of sampleCustomers) {
      await createContact(customer);
    }
  }
}

// Store for all contacts/customers
export const contacts = writable<Contact[]>([]);

// Store for loading state
export const contactsLoading = writable<boolean>(false);

// Store for selected contact
export const selectedContact = writable<Contact | null>(null);

// Derived store for customers only (contacts with status 'Customer')
export const customers = derived(contacts, ($contacts) => 
  $contacts.filter(contact => contact.status === 'Customer')
);

// Derived store for leads only
export const leads = derived(contacts, ($contacts) => 
  $contacts.filter(contact => contact.status === 'Lead')
);

// Store functions
export const contactStore = {
  // Load all contacts
  async loadContacts() {
    contactsLoading.set(true);
    try {
      await initializeSampleData(); // Initialize sample data if needed
      const contactList = await getContacts();
      contacts.set(contactList);
    } catch (error) {
      console.error('Error loading contacts:', error);
    } finally {
      contactsLoading.set(false);
    }
  },

  // Add new contact
  async addContact(contactData: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      const newContact = await createContact(contactData);
      contacts.update(list => [...list, newContact]);
      return newContact;
    } catch (error) {
      console.error('Error adding contact:', error);
      throw error;
    }
  },

  // Update existing contact
  async updateContact(id: string, updates: Partial<Contact>) {
    try {
      const updatedContact = await updateContact(id, updates);
      contacts.update(list => 
        list.map(contact => contact.id === id ? updatedContact : contact)
      );
      
      // Update selected contact if it's the one being updated
      selectedContact.update(selected => 
        selected?.id === id ? updatedContact : selected
      );
      
      return updatedContact;
    } catch (error) {
      console.error('Error updating contact:', error);
      throw error;
    }
  },

  // Delete contact
  async deleteContact(id: string) {
    try {
      await deleteContact(id);
      contacts.update(list => list.filter(contact => contact.id !== id));
      
      // Clear selected contact if it's the one being deleted
      selectedContact.update(selected => 
        selected?.id === id ? null : selected
      );
    } catch (error) {
      console.error('Error deleting contact:', error);
      throw error;
    }
  },

  // Select a contact
  selectContact(contact: Contact | null) {
    selectedContact.set(contact);
  },

  // Search contacts
  searchContacts(query: string) {
    return derived(contacts, ($contacts) => 
      $contacts.filter(contact => 
        contact.fullName.toLowerCase().includes(query.toLowerCase()) ||
        contact.companyName?.toLowerCase().includes(query.toLowerCase()) ||
        contact.emails.some(email => email.email.toLowerCase().includes(query.toLowerCase()))
      )
    );
  }
};

// Legacy export for backward compatibility
export const customerStore = writable({});
