// Placeholder for toastStore.ts
import { writable } from 'svelte/store';

export interface ToastMessage {
  id?: string; // Optional: auto-generated if not provided
  type: 'info' | 'success' | 'warning' | 'error';
  message: string;
  duration?: number; // Optional: duration in ms, defaults to a standard value
}

const defaultDuration = 3000;

// Store to hold the list of active toast messages
export const toasts = writable<ToastMessage[]>([]);

// Function to add a new toast
export function addToast(toast: ToastMessage) {
  const id = toast.id || Math.random().toString(36).substring(2, 9);
  const duration = toast.duration || defaultDuration;

  toasts.update(allToasts => [
    ...allToasts,
    { ...toast, id }
  ]);

  // Automatically remove the toast after its duration
  setTimeout(() => {
    removeToast(id);
  }, duration);
}

// Function to remove a toast by its ID
export function removeToast(id: string) {
  toasts.update(allToasts => allToasts.filter(t => t.id !== id));
}

// You would also typically have a Toasts.svelte component to display these messages.

console.log('Placeholder toastStore.ts loaded');
