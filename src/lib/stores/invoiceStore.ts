// Invoice Store - Comprehensive invoice management
import { writable, derived } from 'svelte/store';
import type { Invoice, InvoiceStatus, Product, InvoiceTemplate } from '$lib/api/invoices';
import { 
  getInvoices, 
  createInvoice, 
  updateInvoice, 
  deleteInvoice,
  getInvoiceById,
  getInvoicesByCustomer,
  getProducts,
  createProduct,
  updateProduct as apiUpdateProduct,
  getInvoiceTemplates,
  createInvoiceTemplate,
  updateInvoiceTemplate as apiUpdateInvoiceTemplate,
  getInvoiceStatuses,
  calculateInvoiceTotals,
  getStatusDisplay,
  getStatusNumber,
  type ApiInvoice
} from '$lib/api/invoices';

// Store for all invoices
export const invoices = writable<ApiInvoice[]>([]);

// Store for invoice statuses
export const invoiceStatuses = writable<InvoiceStatus[]>([]);

// Store for products/services
export const products = writable<Product[]>([]);

// Store for invoice templates
export const invoiceTemplates = writable<InvoiceTemplate[]>([]);

// Store for loading states
export const invoicesLoading = writable<boolean>(false);
export const productsLoading = writable<boolean>(false);
export const templatesLoading = writable<boolean>(false);

// Store for selected invoice
export const selectedInvoice = writable<ApiInvoice | null>(null);

// Store for invoice modal state
export const invoiceModalOpen = writable<boolean>(false);
export const invoiceModalMode = writable<'create' | 'edit'>('create');

// Derived stores for different invoice views
export const invoicesByStatus = derived([invoices, invoiceStatuses], ([$invoices, $invoiceStatuses]) => {
  const grouped: Record<string, ApiInvoice[]> = {};
  
  $invoiceStatuses.forEach(status => {
    const statusNumber = getStatusNumber(status.name);
    grouped[status.id] = $invoices.filter(invoice => invoice.status === statusNumber);
  });
  
  return grouped;
});

export const draftInvoices = derived(invoices, ($invoices) => 
  $invoices.filter(invoice => invoice.status === 0) // 0 = Draft
);

export const sentInvoices = derived(invoices, ($invoices) => 
  $invoices.filter(invoice => invoice.status === 1) // 1 = Sent
);

export const paidInvoices = derived(invoices, ($invoices) => 
  $invoices.filter(invoice => invoice.status === 2) // 2 = Paid
);

export const overdueInvoices = derived(invoices, ($invoices) => 
  $invoices.filter(invoice => invoice.status === 3) // 3 = Overdue
);

export const activeProducts = derived(products, ($products) => 
  $products.filter(product => product.isActive)
);

// Store functions
export const invoiceStore = {
  subscribe: invoices.subscribe,
  
  async loadInvoices() {
    try {
      const invoiceList = await getInvoices();
      invoices.set(invoiceList);
    } catch (error) {
      console.error('Error loading invoices:', error);
      throw error;
    }
  },

  // Load invoice statuses
  async loadInvoiceStatuses() {
    try {
      const statusList = await getInvoiceStatuses();
      invoiceStatuses.set(statusList);
    } catch (error) {
      console.error('Error loading invoice statuses:', error);
    }
  },

  // Load products
  async loadProducts() {
    productsLoading.set(true);
    try {
      const productList = await getProducts();
      products.set(productList);
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      productsLoading.set(false);
    }
  },

  // Load invoice templates
  async loadInvoiceTemplates() {
    templatesLoading.set(true);
    try {
      const templateList = await getInvoiceTemplates();
      invoiceTemplates.set(templateList);
    } catch (error) {
      console.error('Error loading invoice templates:', error);
    } finally {
      templatesLoading.set(false);
    }
  },

  // Add new invoice
  addInvoice(invoice: ApiInvoice) {
    invoices.update(list => [...list, invoice]);
  },

  // Update existing invoice
  updateInvoice(updatedInvoice: ApiInvoice) {
    invoices.update(list => 
      list.map(invoice => 
        invoice.id === updatedInvoice.id ? updatedInvoice : invoice
      )
    );
  },

  // Delete invoice
  removeInvoice(invoiceId: string) {
    invoices.update(list => 
      list.filter(invoice => invoice.id !== invoiceId)
    );
  },

  // Get invoice by ID
  async getInvoiceById(id: string) {
    try {
      return await getInvoiceById(id);
    } catch (error) {
      console.error('Error getting invoice by ID:', error);
      throw error;
    }
  },

  // Get invoices by customer
  async getInvoicesByCustomer(customerId: string) {
    try {
      return await getInvoicesByCustomer(customerId);
    } catch (error) {
      console.error('Error getting invoices by customer:', error);
      throw error;
    }
  },

  // Add new product
  async addProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      const newProduct = await createProduct(productData);
      products.update(list => [...list, newProduct]);
      return newProduct;
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  },

  // Update existing product
  async updateProduct(id: string, updates: Partial<Product>) {
    try {
      const updatedProduct = await apiUpdateProduct(id, updates);
      products.update(list => 
        list.map(product => product.id === id ? updatedProduct : product)
      );
      return updatedProduct;
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  },

  // Add new invoice template
  async addInvoiceTemplate(templateData: Omit<InvoiceTemplate, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      const newTemplate = await createInvoiceTemplate(templateData);
      invoiceTemplates.update(list => [...list, newTemplate]);
      return newTemplate;
    } catch (error) {
      console.error('Error adding invoice template:', error);
      throw error;
    }
  },

  // Update existing invoice template
  async updateInvoiceTemplate(id: string, updates: Partial<InvoiceTemplate>) {
    try {
      const updatedTemplate = await apiUpdateInvoiceTemplate(id, updates);
      invoiceTemplates.update(list => 
        list.map(template => template.id === id ? updatedTemplate : template)
      );
      return updatedTemplate;
    } catch (error) {
      console.error('Error updating invoice template:', error);
      throw error;
    }
  },

  // Select an invoice
  selectInvoice(invoice: ApiInvoice | null) {
    selectedInvoice.set(invoice);
  },

  // Open invoice modal
  openInvoiceModal(mode: 'create' | 'edit' = 'create', invoice?: ApiInvoice) {
    invoiceModalMode.set(mode);
    if (mode === 'edit' && invoice) {
      selectedInvoice.set(invoice);
    } else {
      selectedInvoice.set(null);
    }
    invoiceModalOpen.set(true);
  },

  // Close invoice modal
  closeInvoiceModal() {
    invoiceModalOpen.set(false);
    selectedInvoice.set(null);
  },

  // Calculate invoice totals
  calculateTotals: calculateInvoiceTotals,

  // Search invoices
  searchInvoices(query: string) {
    return derived(invoices, ($invoices) => 
      $invoices.filter(invoice => 
        (invoice.invoiceNumber?.toString() || '').toLowerCase().includes(query.toLowerCase())
        // Note: customerName and customerEmail are not available in ApiInvoice
        // These should be retrieved separately if needed for search
      )
    );
  },

  // Filter invoices by status
  filterInvoicesByStatus(statusName: string) {
    const statusNumber = getStatusNumber(statusName);
    return derived(invoices, ($invoices) => 
      $invoices.filter(invoice => invoice.status === statusNumber)
    );
  },

  // Search products
  searchProducts(query: string) {
    return derived(products, ($products) => 
      $products.filter(product => 
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase()) ||
        product.category?.toLowerCase().includes(query.toLowerCase())
      )
    );
  }
}; 