<script lang="ts">
  export let title: string;
  export let value: string | number;
  export let valueClass: string | undefined = undefined;
</script>

<div class="stat-card">
  <h3>{title}</h3>
  <p class="stat-number" class:overdue={valueClass === 'overdue'}>{value}</p>
</div>

<style lang="less">
  .stat-card {
    background: var(--bg);
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;
    text-align: center;

    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 0.9rem;
      color: var(--grey);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .stat-number {
      margin: 0;
      font-size: 2rem;
      font-weight: 600;
      color: var(--primary);

      &.overdue {
        color: #EF4444;
      }
    }
  }
</style> 