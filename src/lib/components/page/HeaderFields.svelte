<script lang="ts">
    export let formData: any;
    export let formSubmitted: boolean = false;
    export let errors: Record<string, string> = {};
    export let mode: 'edit' | 'readonly' = 'edit';

    // Helper function to format date for display
    function formatDate(dateString: string): string {
        if (!dateString) return 'Not set';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit', 
            year: 'numeric'
        });
    }
</script>

<h3>Invoice Details</h3>

<div class="form-group">
    <label for="issueDate">Issue Date</label>
    {#if mode === 'readonly'}
        <p class="readonly-text">{formatDate(formData.issueDate)}</p>
    {:else}
        <input
          type="date"
          id="issueDate"
          bind:value={formData.issueDate}
          class:error={formSubmitted && errors.issueDate}
        />
        {#if formSubmitted && errors.issueDate}
          <div class="error-message">{errors.issueDate}</div>
        {/if}
    {/if}
</div>
<div class="form-group">
    <label for="dueDate">Due Date</label>
    {#if mode === 'readonly'}
        <p class="readonly-text">{formatDate(formData.dueDate)}</p>
    {:else}
        <input
          type="date"
          id="dueDate"
          bind:value={formData.dueDate}
          class:error={formSubmitted && errors.dueDate}
        />
        {#if formSubmitted && errors.dueDate}
          <div class="error-message">{errors.dueDate}</div>
        {/if}
    {/if}
</div>

<style lang="less">
    .form-group {
        margin-bottom: 0px;
        flex-direction: row;
        align-items: center;
        label {
            width: 140px;
            margin-bottom: 0px;
        }
    }

    .readonly-text {
        margin: 0;
        padding: 8px 0;
        color: var(--black);
        font-size: 14px;
        line-height: 1.4;
    }

    :global(.form-group) {
        margin-bottom: 0px;
    }
</style>