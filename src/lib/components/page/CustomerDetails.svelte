<script lang="ts">
  import CustomerSelect from '$lib/components/CustomerSelect.svelte';
  import { createEventDispatcher } from 'svelte';

  // Props
  export let formData: any;
  export let customerSearch: string = '';
  export let formSubmitted: boolean = false;
  export let errors: Record<string, string> = {};
  export let mode: 'edit' | 'readonly' = 'edit';

  // Event dispatcher
  const dispatch = createEventDispatcher();

  function loadUninvoicedItems(detail: any) {
    dispatch('selectcustomer', detail);
  }
</script>

<label>To</label>

<CustomerSelect
bind:customerId={formData.customerId}
bind:customerSearch={customerSearch}
hasError={formSubmitted && !!errors.customerId}
errorMessage={formSubmitted && errors.customerId ? errors.customerId : ''}
showLabel={false}
on:selectcustomer={(event) => loadUninvoicedItems(event.detail)}    
/>


<div class="form-group">
{#if mode === 'readonly'}
  <p class="readonly-address">
    Customer Name<br>
    123 Customer Street<br>
    City, State 12345<br>
    Phone: (*************<br>
    Email: <EMAIL>
  </p>
{:else}
  <textarea id="sellerDetails" rows="5" placeholder="Customer Name
123 Customer Street
City, State 12345
Phone: (*************
Email: <EMAIL>"></textarea>
{/if}
</div>

<style lang="less">
    p {
        font-size: 1rem;
        line-height: 120%;
        color: var(--grey);
    }
    
    .readonly-address {
        background-color: var(--bg);
        border: 1px solid var(--border);
        border-radius: 4px;
        padding: 12px;
        margin: 0;
        line-height: 1.5;
        color: var(--black);
    }
    
    label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        font-size: 0.9rem;
        color: var(--grey);
    }

    .form-group {
        margin-bottom: 0px;
    }

    :global(.form-group) {
        margin-bottom: 0px;
    }
</style>