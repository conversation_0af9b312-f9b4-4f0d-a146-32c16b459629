<!-- Modal.svelte -->
<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { fade, scale } from 'svelte/transition';
  import { quintOut } from 'svelte/easing';
  
  export let show = false;
  export let showModal = false; // Keep for backward compatibility
  export let title = 'Modal Title';

  const dispatch = createEventDispatcher();

  // Use a single reactive statement to determine visibility
  $: isVisible = show || showModal;

  // Function to close the modal
  function closeModal() {
    show = false;
    showModal = false;
    dispatch('close');
  }

  // Trap focus, handle escape key, etc. for accessibility in a real modal
</script>

{#if isVisible}
  <div
    class="modal-backdrop"
    on:click={closeModal}
    on:keydown={(e) => e.key === 'Escape' && closeModal()}
    role="presentation"
    transition:fade={{ duration: 300 }}
  >
    <div
      class="modal-content"
      role="dialog"
      aria-modal="true"
      tabindex="-1"
      on:click|stopPropagation
      on:keydown|stopPropagation
      transition:scale={{
        duration: 300,
        delay: 150,
        opacity: 0,
        start: 0.7,
        easing: quintOut
      }}
    >
      <div class="modal-header">
        <h2>{title}</h2>
        <button class="close-button" on:click={closeModal} aria-label="Close modal">&times;</button>
      </div>

      <div class="modal-body">
        <slot>
          <p>Modal body content goes here.</p>
        </slot>
      </div>

      <div class="modal-footer">
        <slot name="footer">
          <button on:click={closeModal}>Close</button>
        </slot>
      </div>
    </div>
  </div>
{/if}

<style lang="less">
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
    will-change: opacity;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 8px;
    min-width: 300px;
    max-width: 90%;
    max-height: 90vh;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column;
    will-change: transform, opacity;
    transform-origin: center center;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
    
    h2 {
      margin: 0;
      color: var(--primary);
    }
    
    .close-button {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: var(--grey);
      padding: 0;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: all 0.2s ease;
      
      &:hover {
        background: var(--bg);
        color: var(--black);
        transform: scale(1.1);
      }
    }
  }
  
  .modal-body {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
  }
  
  .modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border);
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
  }
</style>

<!-- console.log('Placeholder Modal.svelte loaded'); -->
