<script lang="ts">
  import { onMount } from 'svelte';
  import { fly } from 'svelte/transition';
  import { toasts, removeToast, type ToastMessage } from '$lib/stores/toastStore';

  // Local state for animation
  let mounted = false;

  onMount(() => {
    mounted = true;
  });

  function handleClose(id: string) {
    removeToast(id);
  }

  // Helper function to get icon based on toast type
  function getIconForType(type: ToastMessage['type']): string {
    switch (type) {
      case 'success':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        `;
      case 'error':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
        `;
      case 'warning':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        `;
      case 'info':
      default:
        return `
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>
        `;
    }
  }
</script>

<div class="toasts-container" class:mounted>
  {#each $toasts as toast (toast.id)}
    <div class="toast-item {toast.type}" transition:fly={{ y: 50, duration: 300 }}>
      <div class="toast-icon">
        {@html getIconForType(toast.type)}
      </div>
      <div class="toast-content">
        {toast.message}
      </div>
      <button class="toast-close" on:click={() => handleClose(toast.id || '')} aria-label="Close notification">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
  {/each}
</div>

<style lang="less">
  .toasts-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column-reverse; /* Reverse to stack from bottom up */
    gap: 10px;
    max-width: 350px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s, transform 0.3s;

    &.mounted {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .toast-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background-color: white;
    animation: slide-in 0.3s ease-out;

    &.success {
      border-left: 4px solid var(--green);
      .toast-icon {
        color: var(--green);
      }
    }

    &.error {
      border-left: 4px solid var(--red);
      .toast-icon {
        color: var(--red);
      }
    }

    &.warning {
      border-left: 4px solid var(--orange, #f59e0b);
      .toast-icon {
        color: var(--orange, #f59e0b);
      }
    }

    &.info {
      border-left: 4px solid var(--primary);
      .toast-icon {
        color: var(--primary);
      }
    }
  }

  .toast-icon {
    margin-right: 12px;
    display: flex;
    align-items: center;
  }

  .toast-content {
    flex: 1;
    padding-right: 12px;
    font-size: 14px;
    line-height: 1.5;
  }

  .toast-close {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: var(--grey);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.6;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }

  @keyframes slide-in {
    from {
      transform: translateY(50px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
</style>
