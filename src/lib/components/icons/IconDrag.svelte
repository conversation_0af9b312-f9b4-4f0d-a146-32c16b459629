<script>
    export let color = "currentColor";
    export let size = "18";
    export let className = "";
  </script>
  
  <svg
    width={size}
    height={parseInt(size, 12) + 2}
    viewBox="0 0 5 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    class="icon-drag {className}"
  >
    <circle cx="0.75" cy="0.75" r="0.75" fill="#292D32"/>
    <circle cx="3.75" cy="0.75" r="0.75" fill="#292D32"/>
    <circle cx="0.75" cy="3.75" r="0.75" fill="#292D32"/>
    <circle cx="3.75" cy="3.75" r="0.75" fill="#292D32"/>
    <circle cx="0.75" cy="6.75" r="0.75" fill="#292D32"/>
    <circle cx="3.75" cy="6.75" r="0.75" fill="#292D32"/>
    </svg>

<style lang="less">
  .icon-drag {
    transition: all 0.3s ease;

    &:hover {
      fill: @red;
      transform: scale(1.1);
    }
  }
</style>