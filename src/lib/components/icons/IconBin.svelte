<script>
  export let color = "currentColor";
  export let size = "18";
  export let className = "";
</script>

<svg
  width={size}
  height={parseInt(size, 12) + 2}
  viewBox="0 0 20 20"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
  class="icon-bin {className}"
>
  <path d="M8.98047 3.73C11.9251 3.73002 14.8768 3.86128 17.8154 4.11672L19.0742 4.23391L19.1494 4.24465C19.5223 4.31994 19.7844 4.6676 19.7461 5.05422C19.7077 5.44045 19.3829 5.72898 19.0029 5.73L18.9258 5.7261L17.6856 5.61086C14.789 5.35909 11.8807 5.23002 8.98047 5.23C7.02577 5.23 5.07032 5.32842 3.11524 5.5259L3.11329 5.52688L1.07325 5.7261C0.661041 5.76651 0.294373 5.46543 0.253911 5.05325C0.213498 4.64104 0.514574 4.27437 0.926762 4.23391L2.96485 4.03469V4.03371C4.96969 3.83121 6.97525 3.73 8.98047 3.73Z" fill="#292D32"/>
  <path d="M11.3096 0.25C12.2444 0.25 12.9672 0.455349 13.4385 0.998047C13.8648 1.48931 13.9533 2.13766 14.0195 2.54492L14.2393 3.84473L14.248 3.9209C14.2734 4.30029 14.008 4.64515 13.625 4.70996C13.2422 4.77464 12.8783 4.5364 12.7773 4.16992L12.7607 4.09473L12.54 2.79492V2.79102C12.4564 2.278 12.4028 2.09341 12.3057 1.98145C12.2516 1.91929 12.0634 1.75 11.3096 1.75H8.69043C7.92433 1.75 7.74179 1.9148 7.69336 1.96973C7.62433 2.04809 7.57839 2.16655 7.52148 2.44629L7.45996 2.78418L7.23926 4.09375C7.17067 4.50216 6.78438 4.77842 6.37598 4.70996C5.96748 4.64136 5.69214 4.2542 5.76074 3.8457L5.98047 2.53613V2.53516L6.04101 2.18555C6.11543 1.8035 6.24566 1.34353 6.56836 0.977539C7.04242 0.439965 7.76653 0.25 8.69043 0.25H11.3096Z" fill="#292D32"/>
  <path d="M16.898 7.39195C17.3112 7.41862 17.6247 7.77469 17.5982 8.18785L16.9488 18.2582L16.9478 18.2621C16.9211 18.6434 16.8929 19.065 16.8141 19.4564C16.7337 19.8555 16.5923 20.277 16.3053 20.6507C15.7041 21.4334 14.6801 21.7504 13.2096 21.7504H6.78964C5.31927 21.7503 4.29604 21.4334 3.69492 20.6507C3.40785 20.277 3.26652 19.8555 3.18613 19.4564C3.10729 19.065 3.07808 18.6434 3.05136 18.2621V18.2582L2.40195 8.18785L2.39999 8.11168C2.41388 7.73173 2.71457 7.41697 3.10214 7.39195C3.48932 7.36718 3.82638 7.64044 3.88925 8.015L3.89804 8.09215L4.54843 18.1576L4.5914 18.7054C4.60793 18.8709 4.62873 19.021 4.65683 19.1605C4.71138 19.4312 4.78664 19.6093 4.88437 19.7367C5.05318 19.9565 5.47048 20.2503 6.78964 20.2504H13.2096C14.5289 20.2504 14.946 19.9565 15.1148 19.7367C15.2127 19.6093 15.2888 19.4315 15.3434 19.1605C15.3995 18.8816 15.4235 18.5613 15.4518 18.1576L16.1012 8.09215L16.11 8.015C16.1728 7.64032 16.5107 7.36707 16.898 7.39195Z" fill="#292D32"/>
  <path d="M11.6601 14.75L11.7363 14.7539C12.1146 14.7921 12.4101 15.1115 12.4101 15.5C12.4101 15.8885 12.1146 16.2079 11.7363 16.2461L11.6601 16.25H8.33C7.91579 16.25 7.58 15.9142 7.58 15.5C7.58 15.0858 7.91579 14.75 8.33 14.75H11.6601Z" fill="#292D32"/>
  <path d="M12.5 10.75C12.9142 10.75 13.25 11.0858 13.25 11.5C13.25 11.9142 12.9142 12.25 12.5 12.25H7.5C7.08579 12.25 6.75 11.9142 6.75 11.5C6.75 11.0858 7.08579 10.75 7.5 10.75H12.5Z" fill="#292D32"/>
  </svg>
  

<style lang="less">
  .icon-bin {
    transition: all 0.3s ease;

    &:hover {
      fill: @red;
      transform: scale(1.1);
    }
  }
</style>
