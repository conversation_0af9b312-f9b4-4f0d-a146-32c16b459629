<script lang="ts">
	export let items: { id: string; label: string }[];
	export let currentItem: string;
	export let onSelect: (itemId: string) => void;
</script>

<nav class="vertical-menu">
	<ul>
		{#each items as item (item.id)}
			<li>
				<button
					class:active={item.id === currentItem}
					on:click={() => onSelect(item.id)}
					aria-current={item.id === currentItem ? 'page' : undefined}
				>
					{item.label}
				</button>
			</li>
		{/each}
	</ul>
</nav>

<style>
	.vertical-menu {
		width: 100%;
	}

	.vertical-menu ul {
		list-style: none;
		padding: 0;
		margin: 0;
	}

	.vertical-menu li button {
		display: block;
		width: 100%;
		padding: 12px 18px;
		text-align: left;
		background-color: transparent;
		border: none;
		cursor: pointer;
		font-size: 0.95rem;
		color: #333;
		border-radius: 6px;
		transition: background-color 0.2s ease, color 0.2s ease;
		margin-bottom: 4px; /* Space between buttons */
	}

	.vertical-menu li button:hover {
		background-color: var(--primary-color, var(--primary));
		color: white;
	}

	.vertical-menu li button.active {
		background-color: var(--primary-color, var(--primary)); 
		color: white;
		font-weight: 500;
	}

	.vertical-menu li button.active:hover {
		background-color: var(--primary)); /* Darker shade on hover for active */
	}
</style>
