<script lang="ts">
  export let title: string;
</script>

<div class="page-header">
  <h1>{title}</h1>
  <div class="actions">
    <slot name="actions"></slot>
  </div>
</div>

<style lang="less">
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    padding-bottom: 1rem; // Add some padding for visual separation
    border-bottom: 1px solid var(--border); // Optional: adds a subtle separator
    height: 78px;
    box-sizing: border-box;
  

    h1 {
      margin: 0;
      font-size: 24px; // Consistent heading size
      color: var(--text-primary-color, #333);
    }

    .actions {
      display: flex;
      gap: 0.75rem; // Space between multiple buttons/actions
      align-items: center;
    }
  }
</style>
