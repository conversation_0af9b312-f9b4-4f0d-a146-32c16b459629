<script lang="ts">
	let selectedCurrency = 'USD';
	const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'];
</script>

<div class="settings-section">
	<h2>General Settings</h2>
	
	<div class="setting-item">
		<label for="currency-select">Currency</label>
		<p class="setting-description">Choose the default currency for your application.</p>
		<select id="currency-select" bind:value={selectedCurrency}>
			{#each currencies as currency}
				<option value={currency}>{currency}</option>
			{/each}
		</select>
	</div>

	<!-- Add more general settings here -->
	<!-- Example:
	<div class="setting-item">
		<label for="language-select">Language</label>
		<p class="setting-description">Select your preferred language.</p>
		<select id="language-select">
			<option value="en">English</option>
			<option value="es">Español</option>
		</select>
	</div>
	-->
</div>

<style>
	.settings-section {
		padding: 25px 30px;
	}
	.settings-section h2 {
		margin-top: 0;
		margin-bottom: 25px;
		font-size: 1.6rem;
		color: #333;
		border-bottom: 1px solid #eee;
		padding-bottom: 10px;
	}
	.setting-item {
		margin-bottom: 25px;
	}
	.setting-item label {
		display: block;
		margin-bottom: 6px;
		font-weight: 600;
		font-size: 1rem;
		color: #444;
	}
	.setting-description {
		font-size: 0.85rem;
		color: #666;
		margin-top: 0;
		margin-bottom: 10px;
	}
	.setting-item select {
		width: 100%;
		max-width: 350px;
		padding: 10px;
		border: 1px solid #ccc;
		border-radius: 4px;
		box-sizing: border-box;
		font-size: 0.9rem;
	}
	.setting-item select {
		appearance: none;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='currentColor' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
		background-repeat: no-repeat;
		background-position: right 10px center;
		background-size: 16px;
		padding-right: 30px; /* Make space for arrow */
	}
</style>
