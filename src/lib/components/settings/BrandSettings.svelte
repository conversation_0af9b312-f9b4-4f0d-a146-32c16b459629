<script lang="ts">
	let logoUrl: string | ArrayBuffer | null = null;
	let primaryColor = '#007bff'; // Default primary color
	let secondaryColor = '#6c757d'; // Default secondary color

	function handleLogoUpload(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files[0]) {
			const file = input.files[0];
			const reader = new FileReader();
			reader.onload = (e) => {
				logoUrl = e.target?.result || null;
			};
			reader.readAsDataURL(file);
		}
	}
</script>

<div class="settings-section">
	<h2>Brand Settings</h2>

	<div class="setting-item">
		<label for="logo-upload">Brand Logo</label>
		<p class="setting-description">Upload your company logo. Recommended format: SVG, PNG, JPG.</p>
		<input type="file" id="logo-upload" accept="image/svg+xml, image/png, image/jpeg" on:change={handleLogoUpload} />
		{#if logoUrl && typeof logoUrl === 'string'}
			<div class="logo-preview">
				<img src={logoUrl} alt="Brand Logo Preview" />
			</div>
		{/if}
	</div>

	<div class="setting-item">
		<label for="primary-color">Primary Brand Color</label>
		<p class="setting-description">Choose the main color for your brand identity.</p>
		<div class="color-picker-container">
			<input type="color" id="primary-color" bind:value={primaryColor} />
			<span class="color-value">{primaryColor}</span>
		</div>
	</div>

	<div class="setting-item">
		<label for="secondary-color">Secondary Brand Color</label>
		<p class="setting-description">Choose an accent or secondary color.</p>
		<div class="color-picker-container">
			<input type="color" id="secondary-color" bind:value={secondaryColor} />
			<span class="color-value">{secondaryColor}</span>
		</div>
	</div>
</div>

<style>
	.settings-section {
		padding: 25px 30px;
	}
	.settings-section h2 {
		margin-top: 0;
		margin-bottom: 25px;
		font-size: 1.6rem;
		color: #333;
		border-bottom: 1px solid #eee;
		padding-bottom: 10px;
	}
	.setting-item {
		margin-bottom: 25px;
	}
	.setting-item label {
		display: block;
		margin-bottom: 6px;
		font-weight: 600;
		font-size: 1rem;
		color: #444;
	}
	.setting-description {
		font-size: 0.85rem;
		color: #666;
		margin-top: 0;
		margin-bottom: 10px;
	}
	.setting-item input[type="file"] {
		display: block;
		margin-bottom: 10px;
		font-size: 0.9rem;
	}
	.logo-preview {
		margin-top: 15px;
		padding: 10px;
		border: 1px dashed #ccc;
		border-radius: 4px;
		display: inline-block;
		background-color: #f9f9f9;
	}
	.logo-preview img {
		max-width: 200px;
		max-height: 80px;
		display: block;
	}
	.color-picker-container {
		display: flex;
		align-items: center;
		gap: 10px;
	}
	.color-picker-container input[type="color"] {
		width: 40px;
		height: 40px;
		border: 1px solid #ccc;
		border-radius: 4px;
		padding: 2px; /* Small padding for the color swatch */
		cursor: pointer;
	}
	.color-value {
		font-family: monospace;
		font-size: 0.9rem;
		background-color: #f0f0f0;
		padding: 4px 8px;
		border-radius: 4px;
	}
</style>
