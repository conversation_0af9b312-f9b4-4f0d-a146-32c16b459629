<script lang="ts">
	// TODO: Implement actual logic for language, timezone, and date/time format selection
	let selectedLanguage = 'en';
	let selectedTimezone = 'UTC';
	let selectedDateFormat = 'MM/DD/YYYY';
	let selectedTimeFormat = 'HH:mm';

	const languages = [
		{ value: 'en', label: 'English' },
		{ value: 'es', label: 'Español' },
		{ value: 'fr', label: 'Français' },
		{ value: 'de', label: 'Deutsch' }
	];

	// TODO: Populate with a comprehensive list of timezones
	const timezones = [
		{ value: 'UTC', label: 'Coordinated Universal Time (UTC)' },
		{ value: 'America/New_York', label: 'Eastern Time (ET)' },
		{ value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' }
	];

	const dateFormats = [
		{ value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
		{ value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
		{ value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' }
	];

	const timeFormats = [
		{ value: 'HH:mm', label: '24-hour (HH:mm)' },
		{ value: 'hh:mm A', label: '12-hour (hh:mm A)' }
	];
</script>

<div class="settings-section">
	<h2>Localisation</h2>

	<div class="setting-item">
		<label for="language-select">Language</label>
		<select id="language-select" bind:value={selectedLanguage}>
			{#each languages as lang}
				<option value={lang.value}>{lang.label}</option>
			{/each}
		</select>
	</div>

	<div class="setting-item">
		<label for="timezone-select">Timezone</label>
		<select id="timezone-select" bind:value={selectedTimezone}>
			{#each timezones as tz}
				<option value={tz.value}>{tz.label}</option>
			{/each}
		</select>
	</div>

	<div class="setting-item">
		<label for="date-format-select">Date Format</label>
		<select id="date-format-select" bind:value={selectedDateFormat}>
			{#each dateFormats as format}
				<option value={format.value}>{format.label}</option>
			{/each}
		</select>
	</div>

	<div class="setting-item">
		<label for="time-format-select">Time Format</label>
		<select id="time-format-select" bind:value={selectedTimeFormat}>
			{#each timeFormats as format}
				<option value={format.value}>{format.label}</option>
			{/each}
		</select>
	</div>
</div>

<style>
	.settings-section h2 {
		margin-bottom: 20px;
		font-size: 1.5em;
	}

	.setting-item {
		display: flex;
		flex-direction: column;
		margin-bottom: 15px;
	}

	.setting-item label {
		margin-bottom: 5px;
		font-weight: bold;
	}

	.setting-item select {
		padding: 8px;
		border: 1px solid #ccc;
		border-radius: 4px;
		font-size: 1em;
	}
</style>
