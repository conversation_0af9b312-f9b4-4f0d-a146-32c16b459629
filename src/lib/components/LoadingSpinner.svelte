<script lang="ts">
  // Props
  export let message: string = "Loading...";
  export let size: 'small' | 'medium' | 'large' = 'medium';
</script>

<div class="loading-container">
  <div class="loading-spinner {size}"></div>
  {#if message}
    <p class="loading-message">{message}</p>
  {/if}
</div>

<style lang="less">
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    color: var(--grey);
  }

  .loading-spinner {
    border: 4px solid var(--secondary-fade);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;

    &.small {
      width: 24px;
      height: 24px;
      border-width: 3px;
    }

    &.medium {
      width: 40px;
      height: 40px;
      border-width: 4px;
    }

    &.large {
      width: 60px;
      height: 60px;
      border-width: 5px;
    }
  }

  .loading-message {
    margin: 0;
    font-size: 14px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
