<script context="module" lang="ts">
  export interface HeaderConfig {
    text: string;
    key: string; 
    sortable?: boolean;
  }

  export interface SortState {
    key: string;
    direction: 'ascending' | 'descending' | '';
  }

  export interface SearchFieldConfig {
    displayName: string;
    queryKey: string;
    currentQuery: string;
    // Optional: placeholder?: string; type?: string;
  }
</script>

<script lang="ts">
  export let headers: HeaderConfig[] = [];
  export let dataRows: Record<string, any>[] = [];
  export let emptyMessage: string = "No data available.";
  export let onHeaderClick: (headerKey: string) => void = () => {}; 
  export let currentSort: SortState = { key: '', direction: '' };

  // New props for grid-top functionality
  export let searchFields: SearchFieldConfig[] = [];
  export let itemsPerPageOptions: number[] = [10, 25, 50, 100];
  export let itemsPerPage: number = 25;
  export let currentPage: number = 1;
  export let totalItems: number = 0;

  export let onSearch: (searchQueries: Record<string, string>) => void = () => {};
  export let onItemsPerPageChange: (newItemsPerPage: number) => void = () => {};
  export let onPageChange: (newPage: number) => void = () => {};

  // Reactive calculations
  $: totalPages = Math.ceil(totalItems / itemsPerPage) || 1;

  // Generate page numbers to display (e.g., max 5 pages around current)
  $: displayedPageNumbers = (() => {
    const maxPagesToShow = 5;
    const halfMax = Math.floor(maxPagesToShow / 2);
    let start = Math.max(1, currentPage - halfMax);
    let end = Math.min(totalPages, currentPage + halfMax);

    if (currentPage - halfMax < 1) {
      end = Math.min(totalPages, start + maxPagesToShow - 1);
    }
    if (currentPage + halfMax > totalPages) {
      start = Math.max(1, end - maxPagesToShow + 1);
    }

    const pages = [];
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    return pages;
  })();

  function getCellValue(row: Record<string, any>, headerKey: string): any {
    if (!headerKey) return '';
    return headerKey.split('.').reduce((obj: any, part: string) => obj && obj[part], row);
  }

  function handleSearchFieldChange(index: number, value: string) {
    if (searchFields[index]) {
      searchFields[index].currentQuery = value;
      // Optionally trigger search on every keystroke, or wait for button click
      // triggerSearch(); // if live search is desired
    }
  }

  function triggerSearch() {
    const queries: Record<string, string> = {};
    searchFields.forEach(field => {
      if (field.currentQuery && field.currentQuery.trim() !== '') {
        queries[field.queryKey] = field.currentQuery;
      }
    });
    onSearch(queries);
  }

  function handleItemsPerPageSelectChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    onItemsPerPageChange(parseInt(target.value, 10));
  }

  function navigateToPage(page: number | 'first' | 'prev' | 'next' | 'last') {
    let newPage = currentPage;
    if (page === 'first') newPage = 1;
    else if (page === 'prev') newPage = Math.max(1, currentPage - 1);
    else if (page === 'next') newPage = Math.min(totalPages, currentPage + 1);
    else if (page === 'last') newPage = totalPages;
    else if (typeof page === 'number') newPage = Math.max(1, Math.min(totalPages, page));

    if (newPage !== currentPage) {
      onPageChange(newPage);
    }
  }

</script>

<div class="grid" style={`--grid-column-count: ${headers.length || 1}`}>
  {#if searchFields.length > 0 || itemsPerPageOptions.length > 0}
  <div class="grid-top">
    <div class="grid-search">
      {#each searchFields as field, index (field.queryKey)}
        <label for={`search-${field.queryKey}`}>{field.displayName}</label>
        <input 
          type="text" 
          id={`search-${field.queryKey}`} 
          value={field.currentQuery} 
          on:input={(e) => handleSearchFieldChange(index, e.currentTarget.value)}
          on:keypress={(e) => e.key === 'Enter' && triggerSearch()} 
        />
      {/each}
      {#if searchFields.length > 0}
        <button type="button" class="button secondary" on:click={triggerSearch}>Search</button>
      {/if}
    </div>

    <div class="grid-search-and-items-per-page">
        <select value={itemsPerPage} on:change={handleItemsPerPageSelectChange} title="Records per Page">
            {#each itemsPerPageOptions as option (option)}
                <option value={option}>{option} per page</option>
            {/each}
        </select>
        {#if totalItems > 0}
          <div>{currentPage} / {totalPages}</div>
        {/if}
    </div>

    <div class="grid-pager">
        {#if currentPage !== 1}
            <button type="button" title="First Page" on:click={() => navigateToPage('first')}>«</button>
            <button type="button" title="Previous Page" on:click={() => navigateToPage('prev')}>Prev</button>
        {/if}

        {#each displayedPageNumbers as pageNum (pageNum)}
            <button 
              type="button"
              class:selected={pageNum === currentPage} 
              class="page_number" 
              on:click={() => navigateToPage(pageNum)}
            >
                {pageNum}
            </button>
        {/each}
        
        {#if currentPage !== totalPages && totalPages > 0}
            <button type="button" class="navigate_next" title="Next Page" on:click={() => navigateToPage('next')}>Next</button>
            <button type="button" class="last_page" title="Last Page" on:click={() => navigateToPage('last')}>»</button>
        {/if}
    </div>
    <div>{totalItems} results</div>
  </div>
  {/if}

  {#if headers.length > 0}
    <div class="grid-row-header" style={`grid-template-columns: repeat(${headers.length}, 1fr);`}>
      {#each headers as header (header.key)}
        <button 
          type="button"
          class="header-cell"
          role="columnheader"
          on:click={() => header.sortable && onHeaderClick(header.key)}
          class:is-sorting={currentSort.key === header.key}
          class:ascending={currentSort.key === header.key && currentSort.direction === 'ascending'}
          class:descending={currentSort.key === header.key && currentSort.direction === 'descending'}
          aria-sort={currentSort.key === header.key ? (currentSort.direction === 'ascending' ? 'ascending' : 'descending') : 'none'}
          disabled={!header.sortable}
        >
          {header.text}
        </button>
      {/each}
    </div>
  {/if}

  {#if dataRows.length > 0}
    {#each dataRows as row, rowIndex (row.id || rowIndex)}
      <div class="grid-row" style={`grid-template-columns: repeat(${headers.length}, 1fr);`}>
        {#each headers as header (header.key)}
          <div class="cell">
            <slot name="cell" row={row} headerKey={header.key} value={getCellValue(row, header.key)}>
              {getCellValue(row, header.key)}
            </slot>
          </div>
        {/each}
      </div>
    {/each}
  {:else}
    <div class="grid-row-empty">
      {emptyMessage}
    </div>
  {/if}
</div>

<style lang="less">
  .grid {
    display: grid;
    width: 100%;
    border-radius: var(--br);
    border: 1px solid var(--border);
    background: var(--white);
    overflow: hidden;

    .grid-row-empty {
      padding: 100px 20px;
      box-sizing: border-box;
      color: var(--grey);
      text-align: center;
      grid-column: 1 / -1; 
    }

    .grid-row,
    .grid-row-header {
      display: grid;
      grid-column: 1 / -1;
    }

    .grid-row {
      border-bottom: 1px solid var(--border);

      &:last-child {
        border-bottom: none;
      }

      .cell { 
        padding: 20px;
        color: var(--black);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .grid-row-header {
      border-bottom: 2px solid var(--border);
      font-weight: 600;
      background: var(--bg);
      font-size: 14px;

      .header-cell { 
        background: none;
        border: none;
        padding: 10px 20px;
        margin: 0;
        font: inherit;
        color: var(--black);
        cursor: pointer;
        text-align: left;
        transition: background 0.2s;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%; 

        &:hover:not(:disabled) {
          background: var(--secondary-fade);
        }

        &:disabled {
          cursor: default;
          color: var(--grey); 
        }

        &.is-sorting {
          background: var(--secondary-fade3);

          &.ascending::after {
            content: '↑';
            margin-left: 10px;
            color: var(--primary);
          }

          &.descending::after {
            content: '↓';
            margin-left: 10px;
            color: var(--primary);
          }
        }
      }
    }
  }

  .grid-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--border);
    width: 100%;
    grid-column: 1 / -1; 
    border-radius: var(--br) var(--br) 0 0;
    background: var(--bg);
    box-sizing: border-box;
    gap: 20px;

    label {
        color: var(--primary);
        font-size: 14px;
        white-space: nowrap;
    }

    input[type='text'] {
        height: 30px;
        width: 140px;
        margin-right: 5px;
        padding: 0 5px; 
        font-size: 14px;
        border: 1px solid var(--border, #ccc);
        border-radius: var(--br, 4px);
    }

    select {
        height: 30px;
        padding: 0 5px;
        border: 1px solid var(--border, #ccc);
        border-radius: var(--br, 4px);
        background-color: var(--white, #fff);
    }

    .grid-search {
        flex-grow: 1;
        display: flex;
        align-items: center;
        gap: 5px;

        .button {
            padding: 0 10px; 
            height: 30px;
            line-height: 30px; 
            color: var(--primary);
            font-size: 14px;
            font-weight: 600;
            background-color: transparent; 
            border: 1px solid var(--primary);
            border-radius: var(--br);
            cursor: pointer;
            &:hover {
                background-color: var(--primary-fade, rgba(0,0,0,0.05));
            }
        }
    }

    .grid-search-and-items-per-page {
        display: flex;
        gap: 20px;
        align-items: center;
        white-space: nowrap;
    }

    .grid-pager {
        display: flex;
        gap: 5px;
        align-items: center;

        button { 
            background: var(--button-bg, var(--bg));
            border-radius: var(--br);
            padding: 5px 10px;
            border: 1px solid var(--border);
            color: var(--black);
            cursor: pointer;
            font-size: 14px;
            line-height: 1.2; 

            &:hover:not(.selected) {
                background: var(--secondary-fade, #f0f0f0);
            }

            &.selected {
                background: var(--primary, #007bff);
                color: var(--white, #fff);
                border-color: var(--primary, #007bff);
            }
        }
    }
    
    & > div:last-child { 
        white-space: nowrap;
        font-size: 14px;
        color: var(--grey, #555);
    }
  }
</style>
