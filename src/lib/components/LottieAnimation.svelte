<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { browser } from '$app/environment';
  import type { AnimationItem } from 'lottie-web';

  // Props
  export let animationData: any;
  export let loop = true;
  export let autoplay = true;
  export let width = '100%';
  export let height = '100%';

  let container: HTMLElement;
  let animation: AnimationItem | undefined;
  let lottie: any;

  onMount(async () => {
    if (browser && container && animationData) {
      // Import lottie-web dynamically to avoid SSR issues
      lottie = (await import('lottie-web')).default;

      animation = lottie.loadAnimation({
        container,
        renderer: 'svg',
        loop,
        autoplay,
        animationData
      });
    }
  });

  onDestroy(() => {
    if (browser && animation) {
      animation.destroy();
    }
  });
</script>

<div
  bind:this={container}
  class="lottie-container"
  style="width: {width}; height: {height};"
>
</div>

<style>
  .lottie-container {
    display: block;
    overflow: hidden;
  }
</style>
