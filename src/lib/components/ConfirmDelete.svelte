<!-- Placeholder for ConfirmDelete.svelte -->
<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '$lib/components/Button.svelte'; // Assuming Button component exists
  // import Modal from '$lib/components/Modal.svelte'; // Could use the Modal component

  export let showConfirm = false; // To control visibility
  export let itemName = 'this item'; // Item to be deleted, for the message

  const dispatch = createEventDispatcher();

  function confirm() {
    dispatch('confirm');
    showConfirm = false;
  }

  function cancel() {
    dispatch('cancel');
    showConfirm = false;
  }
</script>

{#if showConfirm}
  <div class="confirm-delete-backdrop" on:click={cancel} role="presentation">
    <div class="confirm-delete-dialog" role="dialog" aria-modal="true" on:click|stopPropagation>
      <h3>Confirm Deletion</h3>
      <p>Are you sure you want to delete {itemName}?</p>
      <div class="actions">
        <Button on:click={cancel} variant="secondary" type="button">Cancel</Button>
        <Button on:click={confirm} variant="danger" type="button">Delete</Button>
      </div>
    </div>
  </div>
{/if}

<style lang="less">
  .confirm-delete-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1010; // Higher than modal if used separately
  }
  .confirm-delete-dialog {
    background-color: white;
    padding: 25px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);

    h3 {
      margin-top: 0;
      color: #333;
    }
    p {
      margin-bottom: 20px;
      color: #555;
    }
    .actions {
      display: flex;
      justify-content: center;
      gap: 10px;
    }
  }
</style>

<!-- console.log('Placeholder ConfirmDelete.svelte loaded'); -->
