<script lang="ts">
  import { createEventDispatcher, onMount, onDestroy } from 'svelte';
  import { api, ApiError } from '$lib/utils/api';
  import type { Contact } from '$lib/api/contacts';

  const dispatch = createEventDispatcher();

  export let customerId: string = '';
  export let customerSearch: string = '';
  export let hasError: boolean = false; // Prop to indicate validation error
  export let errorMessage: string = ''; // Error message to display
  export let showLabel: boolean = true; // Prop to control label visibility

  let showCustomerDropdown = false;
  let highlightedIndex = -1;
  let customers: Contact[] = [];
  let isLoading = false;
  let isSearching = false; // Separate loading state for search
  let apiError: string | null = null;
  let searchTimeout: ReturnType<typeof setTimeout>;
  let lastSearchTerm = ''; // Track last search to avoid duplicate calls
  let inputElement: HTMLInputElement; // Reference to input element

  // Load customers on component mount
  onMount(async () => {
    await loadCustomers();
  });

  // Clean up timeout on component destroy
  onDestroy(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
  });

  // Load customers from API with optional search parameter
  async function loadCustomers(searchTerm?: string) {
    // Don't make duplicate calls for the same search term
    if (searchTerm === lastSearchTerm) {
      return;
    }
    
    // Use different loading states for initial load vs search
    if (searchTerm === undefined) {
      isLoading = true;
    } else {
      isSearching = true;
    }
    
    apiError = null;
    lastSearchTerm = searchTerm || '';
    
    try {
      // Build endpoint with search parameter if provided
      let endpoint = '/customers';
      if (searchTerm && searchTerm.trim()) {
        const params = new URLSearchParams({ query: searchTerm.trim() });
        endpoint = `/customers/search?${params.toString()}`;
      }
      
      const response = await api.get<any[]>(endpoint);
      
      if (Array.isArray(response)) {
        // Transform API response to Contact format
        customers = response.map((customer: any) => ({
          id: customer.id,
          fullName: customer.name || customer.fullName || 'Unknown Customer',
          companyName: customer.companyName || '',
          emails: customer.emails || [{ 
            id: '1', 
            email: customer.email || '', 
            type: 'Work', 
            isPrimary: true 
          }],
          phones: customer.phones || [{ 
            id: '1', 
            phone: customer.phone || '', 
            type: 'Work', 
            isPrimary: true 
          }],
          addresses: customer.addresses || [],
          status: customer.status || 'Customer',
          notes: customer.notes || [],
          checklists: customer.checklists || [],
          communicationTimeline: customer.communicationTimeline || [],
          createdAt: customer.createdAt || new Date().toISOString(),
          updatedAt: customer.updatedAt || new Date().toISOString()
        }));
      } else {
        console.error('API response was not an array:', response);
        customers = [];
        apiError = 'Received unexpected data format from the server.';
      }
    } catch (err) {
      console.error('Error loading customers:', err);
      if (err instanceof ApiError) {
        apiError = `Failed to load customers: ${err.message}`;
      } else {
        apiError = 'An unexpected error occurred while loading customers.';
      }
      customers = [];
    } finally {
      isLoading = false;
      isSearching = false;
    }
  }

  // Debounced search function
  function debouncedSearch(searchTerm: string) {
    clearTimeout(searchTimeout);
    
    // Reset highlighted index when searching
    highlightedIndex = -1;
    
    searchTimeout = setTimeout(() => {
      // Only search if the term has changed and meets criteria
      if (searchTerm !== lastSearchTerm && (searchTerm.trim().length >= 2 || searchTerm.trim().length === 0)) {
        loadCustomers(searchTerm);
      }
    }, 300); // 300ms delay
  }

  // Helper function to filter customers (client-side filtering for immediate feedback)
  function filterCustomers(searchTerm: string): Contact[] {
    if (!searchTerm.trim()) {
      return customers;
    }
    const term = searchTerm.toLowerCase();
    return customers.filter(customer =>
      customer.fullName.toLowerCase().includes(term) ||
      (customer.companyName && customer.companyName.toLowerCase().includes(term)) ||
      customer.emails.some(email => email.email.toLowerCase().includes(term))
    );
  }

  function handleCustomerSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    customerSearch = target.value;
    
    // Always show dropdown when typing
    if (!showCustomerDropdown) {
      showCustomerDropdown = true;
    }
    
    highlightedIndex = -1;
    
    // Use debounced search for API calls
    debouncedSearch(customerSearch);
  }

  function selectCustomer(customer: Contact) {
    customerId = customer.id;
    customerSearch = customer.companyName || customer.fullName;
    showCustomerDropdown = false;
    highlightedIndex = -1;
    
    // Clear any pending search
    clearTimeout(searchTimeout);
    
    dispatch('selectcustomer', customer);
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (!showCustomerDropdown) return;

    const filteredCustomers = filterCustomers(customerSearch);

    if (event.key === 'ArrowDown') {
      event.preventDefault();
      highlightedIndex = Math.min(highlightedIndex + 1, filteredCustomers.length - 1);
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      highlightedIndex = Math.max(highlightedIndex - 1, -1);
    } else if (event.key === 'Enter' && highlightedIndex >= 0) {
      event.preventDefault();
      selectCustomer(filteredCustomers[highlightedIndex]);
    } else if (event.key === 'Escape') {
      showCustomerDropdown = false;
      highlightedIndex = -1;
    }
  }

  function handleInputBlur(event: FocusEvent) {
    // Only close dropdown if focus is not moving to a dropdown item
    const relatedTarget = event.relatedTarget as HTMLElement;
    if (!relatedTarget || !relatedTarget.closest('.dropdown-menu')) {
      setTimeout(() => {
        showCustomerDropdown = false;
        highlightedIndex = -1;
      }, 150); // Reduced delay
    }
  }

  function handleInputFocus() {
    showCustomerDropdown = true;
    // Reload customers if there was an error previously
    if (apiError && customers.length === 0) {
      loadCustomers();
    }
  }

  // Retry function for error state
  function handleRetry() {
    lastSearchTerm = ''; // Reset to force reload
    loadCustomers(customerSearch);
  }

  // Reactive statement for filtered customers
  $: filteredCustomers = filterCustomers(customerSearch);
  
  // Show loading in dropdown only when we have no results and are searching
  $: showLoadingInDropdown = isSearching && showCustomerDropdown && customers.length === 0;
</script>

<div class="form-group customer-search">
  {#if showLabel}
    <label for="customerSearch">Customer</label>
  {/if}
  <div class="search-container" class:has-error={hasError}>
    <input
      bind:this={inputElement}
      type="text"
      id="customerSearch"
      bind:value={customerSearch}
      on:input={handleCustomerSearch}
      on:keydown={handleKeyDown}
      on:blur={handleInputBlur}
      on:focus={handleInputFocus}
      placeholder={isLoading ? "Loading customers..." : "Search customers..."}
      autocomplete="off"
      class="search-input"
      class:searching={isSearching}
      disabled={isLoading}
    />
    <input type="hidden" bind:value={customerId} name="customerId" />
    
    <!-- Subtle search indicator -->
    {#if isSearching}
      <div class="search-indicator">
        <div class="search-spinner"></div>
      </div>
    {/if}
    
    {#if showCustomerDropdown}
      <div class="dropdown-menu">
        {#if showLoadingInDropdown}
          <div class="loading-item">
            <div class="loading-spinner"></div>
            <span>Searching...</span>
          </div>
        {:else if apiError}
          <div class="error-item">
            <div class="error-message">{apiError}</div>
            <button class="retry-button" on:click={handleRetry}>
              Retry
            </button>
          </div>
        {:else if filteredCustomers.length > 0}
          <ul>
            {#each filteredCustomers as customer, i}
              <li
                class:highlighted={i === highlightedIndex}
                on:mousedown|preventDefault={() => selectCustomer(customer)}
                on:mouseenter={() => highlightedIndex = i}
              >
                <div class="customer-option">
                  <div class="customer-name">{customer.companyName || customer.fullName}</div>
                  {#if customer.emails.length > 0}
                    <div class="customer-email">{customer.emails.find(e => e.isPrimary)?.email || customer.emails[0].email}</div>
                  {/if}
                </div>
              </li>
            {/each}
          </ul>
        {:else}
          <div class="no-results">
            {customerSearch.trim() ? 'No customers found' : 'No customers available'}
          </div>
        {/if}
      </div>
      
      <!-- Absolute positioned search indicator outside dropdown -->
      {#if isSearching && filteredCustomers.length > 0}
        <div class="dropdown-search-indicator">
          <div class="dropdown-search-spinner"></div>
          <span>Updating...</span>
        </div>
      {/if}
    {/if}
  </div>

  <!-- Error message -->
  {#if errorMessage}
    <div class="error-message">{errorMessage}</div>
  {/if}
</div>

<style lang="less">
  .form-group {
    position: relative;

    &.customer-search {
      .search-container {
        position: relative;
        width: 100%;

        &.has-error {
          .search-input {
            border-color: var(--error);
          }
        }
      }

      .search-input {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border);
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        background-color: white;
        color: var(--text);

        &::placeholder {
          color: var(--grey-light);
        }

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
        }

        &:disabled {
          background-color: var(--bg);
          cursor: not-allowed;
          opacity: 0.6;
        }

        &.searching {
          padding-right: 35px; /* Make room for search indicator */
        }
      }

      .search-indicator {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;

        .search-spinner {
          width: 14px;
          height: 14px;
          border: 2px solid var(--border);
          border-top: 2px solid var(--primary);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }

      .dropdown-menu {
        position: absolute;
        top: calc(100% + 4px);
        left: 0;
        right: 0;
        max-height: 300px;
        overflow-y: auto;
        margin: 0;
        padding: 6px 0;
        background: white;
        border: 1px solid var(--border);
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        animation: fadeIn 0.15s ease-out;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: var(--bg);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--grey-light);
          border-radius: 3px;
        }

        ul {
          list-style: none;
          margin: 0;
          padding: 0;
        }

        li {
          padding: 0;
          margin: 0;
          cursor: pointer;
          transition: background-color 0.1s ease;
          position: relative;

          &:hover,
          &.highlighted {
            background-color: var(--bg-hover);
          }

          &.highlighted {
            background-color: rgba(var(--primary-rgb), 0.1);
          }
        }

        .customer-option {
          padding: 10px 16px;
          position: relative;
          z-index: 1;
        }

        .customer-name {
          font-weight: 500;
          color: var(--text);
          margin-bottom: 2px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .customer-email {
          font-size: 12px;
          color: var(--grey);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .no-results {
          padding: 12px 16px;
          color: var(--grey);
          font-size: 14px;
          text-align: center;
        }

        .loading-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 16px;
          color: var(--grey);
          font-size: 14px;

          .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid var(--border);
            border-top: 2px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
        }

        .error-item {
          padding: 12px 16px;
          text-align: center;

          .error-message {
            color: var(--error);
            font-size: 12px;
            margin-bottom: 8px;
          }

          .retry-button {
            background: var(--primary);
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
              background: var(--primary-dark);
            }

            &:focus {
              outline: none;
              box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.3);
            }
          }
        }
      }

      .dropdown-search-indicator {
        position: absolute;
        top: 8px;
        right: -120px;
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 4px 8px;
        background: rgba(var(--primary-rgb), 0.1);
        border: 1px solid rgba(var(--primary-rgb), 0.2);
        border-radius: 4px;
        color: var(--primary);
        font-size: 11px;
        font-weight: 500;
        white-space: nowrap;
        z-index: 1001;
        animation: fadeIn 0.2s ease-out;

        .dropdown-search-spinner {
          width: 10px;
          height: 10px;
          border: 1.5px solid rgba(var(--primary-rgb), 0.3);
          border-top: 1.5px solid var(--primary);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .error-message {
    color: var(--error);
    font-size: 12px;
    margin-top: 5px;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>