import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  getCurrentCurrency,
  setCurrency,
  formatCurrency,
  formatCurrencyWithOptions,
  DEFAULT_CURRENCY,
  SUPPORTED_CURRENCIES
} from './currency';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('Currency Configuration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getCurrentCurrency', () => {
    it('should return default currency (GBP) when no stored currency', () => {
      localStorageMock.getItem.mockReturnValue(null);

      const currency = getCurrentCurrency();

      expect(currency).toEqual(DEFAULT_CURRENCY);
      expect(currency.code).toBe('GBP');
      expect(currency.symbol).toBe('£');
    });

    it('should return stored currency when valid', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify({ code: 'USD' }));

      const currency = getCurrentCurrency();

      expect(currency).toEqual(SUPPORTED_CURRENCIES.USD);
    });

    it('should return default currency when stored currency is invalid', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify({ code: 'INVALID' }));

      const currency = getCurrentCurrency();

      expect(currency).toEqual(DEFAULT_CURRENCY);
    });

    it('should return default currency when stored data is corrupted', () => {
      localStorageMock.getItem.mockReturnValue('invalid json');

      const currency = getCurrentCurrency();

      expect(currency).toEqual(DEFAULT_CURRENCY);
    });
  });

  describe('setCurrency', () => {
    it('should store valid currency', () => {
      setCurrency('USD');

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'ejp_currency_config',
        JSON.stringify(SUPPORTED_CURRENCIES.USD)
      );
    });

    it('should not store invalid currency', () => {
      setCurrency('INVALID');

      expect(localStorageMock.setItem).not.toHaveBeenCalled();
    });
  });

  describe('formatCurrency', () => {
    it('should format currency using default GBP configuration', () => {
      localStorageMock.getItem.mockReturnValue(null);

      const formatted = formatCurrency(123.45);

      expect(formatted).toBe('£123.45');
    });

    it('should format currency using USD configuration', () => {
      const formatted = formatCurrency(123.45, SUPPORTED_CURRENCIES.USD);

      expect(formatted).toBe('$123.45');
    });

    it('should format currency using EUR configuration', () => {
      const formatted = formatCurrency(123.45, SUPPORTED_CURRENCIES.EUR);

      expect(formatted).toBe('€123.45');
    });

    it('should handle zero amount', () => {
      const formatted = formatCurrency(0);

      expect(formatted).toBe('£0.00');
    });

    it('should handle negative amounts', () => {
      const formatted = formatCurrency(-50.25);

      expect(formatted).toBe('-£50.25');
    });
  });

  describe('formatCurrencyWithOptions', () => {
    it('should format with custom fraction digits', () => {
      const formatted = formatCurrencyWithOptions(123.456, {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
      });

      expect(formatted).toBe('£123.456');
    });

    it('should format with currency code display', () => {
      const formatted = formatCurrencyWithOptions(123.45, {
        currencyDisplay: 'code'
      });

      // The exact format may vary by browser/locale, just check it contains GBP and the amount
      expect(formatted).toContain('GBP');
      expect(formatted).toContain('123.45');
    });
  });

  describe('SUPPORTED_CURRENCIES', () => {
    it('should contain GBP as default', () => {
      expect(SUPPORTED_CURRENCIES.GBP).toBeDefined();
      expect(SUPPORTED_CURRENCIES.GBP.code).toBe('GBP');
      expect(SUPPORTED_CURRENCIES.GBP.symbol).toBe('£');
      expect(SUPPORTED_CURRENCIES.GBP.locale).toBe('en-GB');
    });

    it('should contain USD', () => {
      expect(SUPPORTED_CURRENCIES.USD).toBeDefined();
      expect(SUPPORTED_CURRENCIES.USD.code).toBe('USD');
      expect(SUPPORTED_CURRENCIES.USD.symbol).toBe('$');
    });

    it('should contain EUR', () => {
      expect(SUPPORTED_CURRENCIES.EUR).toBeDefined();
      expect(SUPPORTED_CURRENCIES.EUR.code).toBe('EUR');
      expect(SUPPORTED_CURRENCIES.EUR.symbol).toBe('€');
    });
  });
});
