// Currency Configuration
export interface CurrencyConfig {
  code: string;
  symbol: string;
  name: string;
  locale: string;
}

export const SUPPORTED_CURRENCIES: Record<string, CurrencyConfig> = {
  GBP: {
    code: 'GBP',
    symbol: '£',
    name: 'British Pound Sterling',
    locale: 'en-GB'
  },
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    locale: 'en-US'
  },
  EUR: {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    locale: 'en-EU'
  }
};

// Default currency configuration
export const DEFAULT_CURRENCY = SUPPORTED_CURRENCIES.GBP;

// Get currency configuration from localStorage or use default
export function getCurrentCurrency(): CurrencyConfig {
  const stored = localStorage.getItem('ejp_currency_config');
  if (stored) {
    try {
      const parsed = JSON.parse(stored);
      return SUPPORTED_CURRENCIES[parsed.code] || DEFAULT_CURRENCY;
    } catch {
      return DEFAULT_CURRENCY;
    }
  }
  return DEFAULT_CURRENCY;
}

// Set currency configuration
export function setCurrency(currencyCode: string): void {
  const currency = SUPPORTED_CURRENCIES[currencyCode];
  if (currency) {
    localStorage.setItem('ejp_currency_config', JSON.stringify(currency));
  }
}

// Format currency amount using current configuration
export function formatCurrency(amount: number, currencyConfig?: CurrencyConfig): string {
  const config = currencyConfig || getCurrentCurrency();
  return new Intl.NumberFormat(config.locale, {
    style: 'currency',
    currency: config.code
  }).format(amount);
}

// Format currency amount with custom options
export function formatCurrencyWithOptions(
  amount: number, 
  options: {
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    currencyDisplay?: 'symbol' | 'code' | 'name';
  } = {},
  currencyConfig?: CurrencyConfig
): string {
  const config = currencyConfig || getCurrentCurrency();
  return new Intl.NumberFormat(config.locale, {
    style: 'currency',
    currency: config.code,
    ...options
  }).format(amount);
}
