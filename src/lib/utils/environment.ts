import { browser } from '$app/environment';

/**
 * Detects if the application is running on localhost/development environment
 * @returns true if running on localhost, false if on production
 */
export function isLocalhost(): boolean {
  if (!browser) return false;
  
  const hostname = window.location.hostname;
  return hostname === 'localhost' || 
         hostname === '127.0.0.1' || 
         hostname.startsWith('192.168.') ||
         hostname.endsWith('.local');
}

/**
 * Detects if the application is running in development mode
 * @returns true if in development, false if in production
 */
export function isDevelopment(): boolean {
  return import.meta.env.DEV || isLocalhost();
}

/**
 * Determines if a dev-only feature should be shown
 * @param devOnly - Whether the feature is dev-only
 * @returns true if the feature should be shown, false otherwise
 */
export function shouldShowDevFeature(devOnly: boolean): boolean {
  if (!devOnly) return true; // Always show non-dev features
  return isDevelopment(); // Only show dev features in development
}

/**
 * Global environment configuration
 */
export const ENV = {
  isLocalhost: isLocalhost(),
  isDevelopment: isDevelopment(),
  isProduction: !isDevelopment()
} as const; 