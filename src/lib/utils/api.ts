import { authenticatedFetch } from '$lib/stores/auth';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://api-test.easyjobplanner.com';

export class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

// Generic API call function
export async function apiCall<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  try {
    const response = await authenticatedFetch(url, options);
    
    if (!response.ok) {
      throw new ApiError(response.status, `API call failed: ${response.statusText}`);
    }
    
    // Handle NoContent responses (204) which have no body
    if (response.status === 204) {
      return null as T;
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(500, 'Network error occurred');
  }
}

// Convenience methods for common HTTP verbs
export const api = {
  get: <T>(endpoint: string) => apiCall<T>(endpoint, { method: 'GET' }),
  
  post: <T>(endpoint: string, data?: any) => apiCall<T>(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  }),
  
  put: <T>(endpoint: string, data?: any) => apiCall<T>(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  }),
  
  patch: <T>(endpoint: string, data?: any) => apiCall<T>(endpoint, {
    method: 'PATCH',
    body: data ? JSON.stringify(data) : undefined,
  }),
  
  delete: <T>(endpoint: string) => apiCall<T>(endpoint, { method: 'DELETE' }),
}; 