export interface JWTClaims {
  UserId?: string;
  TenantId?: string;
  RoleType?: string;
  exp?: number;
  iat?: number;
  // Add other standard JWT claims as needed
  [key: string]: any;
}

export enum RoleType {
  None = 0,
  SuperAdmin = 1,
  Admin = 2,
  TeamMember = 3
}

/**
 * Decodes a JWT token and returns the claims payload
 * @param token - The JWT token string
 * @returns The decoded claims object or null if invalid
 */
export function decodeJWT(token: string): JWTClaims | null {
  try {
    // JWT tokens have 3 parts separated by dots
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    // The payload is the second part (index 1)
    const payload = parts[1];
    
    // Add padding if needed for proper base64 decoding
    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
    
    // Decode the base64 payload
    const decodedPayload = atob(paddedPayload);
    
    // Parse the JSON
    const claims: JWTClaims = JSON.parse(decodedPayload);
    
    return claims;
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

/**
 * Gets the role type name from the numeric role type value
 * @param roleTypeValue - The numeric role type from JWT claims
 * @returns The role type name as a string
 */
export function getRoleTypeName(roleTypeValue: string | number): string {
  const numericValue = typeof roleTypeValue === 'string' ? parseInt(roleTypeValue, 10) : roleTypeValue;
  
  switch (numericValue) {
    case RoleType.SuperAdmin:
      return 'Super Admin';
    case RoleType.Admin:
      return 'Admin';
    case RoleType.TeamMember:
      return 'Team Member';
    case RoleType.None:
    default:
      return 'None';
  }
}

/**
 * Checks if a token is expired
 * @param token - The JWT token string
 * @returns true if the token is expired, false otherwise
 */
export function isTokenExpired(token: string): boolean {
  const claims = decodeJWT(token);
  if (!claims || !claims.exp) {
    return true;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  return claims.exp < currentTime;
} 