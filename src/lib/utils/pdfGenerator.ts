import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import type { Invoice, InvoiceLineItem, ApiInvoice } from '$lib/api/invoices';
import { formatCurrency } from '$lib/config/currency';
import { getStatusDisplay } from '$lib/api/invoices';

export interface InvoiceFormData {
  invoiceNumber: string;
  customerId: string;
  issueDate: string;
  dueDate: string;
  status: { id: string; name: string; color: string };
  lineItems: InvoiceLineItem[];
  customHeaderFields: Array<{ id: string; label: string; value: string; type: string }>;
  notes: string;
  terms: string;
  templateId: string;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
}

export async function generateInvoicePDF(
  invoiceData: Invoice | InvoiceFormData,
  customerName?: string,
  customerAddress?: any
): Promise<void> {
  try {
    // Create a temporary container for the invoice HTML
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.width = '794px'; // A4 width in pixels at 96 DPI
    container.style.backgroundColor = 'white';
    container.style.padding = '40px';
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.fontSize = '12px';
    container.style.lineHeight = '1.4';
    container.style.color = '#000000';

    // Generate the invoice HTML
    container.innerHTML = generateInvoiceHTML(invoiceData, customerName, customerAddress);

    // Add to DOM temporarily
    document.body.appendChild(container);

    try {
      // Convert HTML to canvas
      const canvas = await html2canvas(container, {
        scale: 2, // Higher resolution
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 794,
        height: container.scrollHeight
      });

      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'px',
        format: [794, 1123] // A4 size in pixels
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = 794;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Add image to PDF
      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

      // Generate filename
      const invoiceNumber = 'invoiceNumber' in invoiceData 
        ? invoiceData.invoiceNumber 
        : `INV-${new Date().getFullYear()}-DRAFT`;
      const filename = `${invoiceNumber}.pdf`;

      // Download the PDF
      pdf.save(filename);
    } finally {
      // Clean up
      document.body.removeChild(container);
    }
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Helper function to generate line items HTML for PDF
function generateLineItemsHTML(lineItems: any[]): string {
  return `
    <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
      <thead>
        <tr style="background-color: #f3f4f6;">
          <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: left; font-weight: bold;">Description</th>
          <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: center; font-weight: bold; width: 80px;">Qty</th>
          <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: bold; width: 100px;">Unit Price</th>
          <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: bold; width: 80px;">Tax %</th>
          <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: bold; width: 100px;">Total</th>
        </tr>
      </thead>
      <tbody>
        ${lineItems.map(item => `
          <tr>
            <td style="border: 1px solid #e5e7eb; padding: 12px; vertical-align: top;">
              <div style="font-weight: 500;">${item.description}</div>
              ${item.additionalInfo ? `<div style="font-size: 10px; color: #6b7280; margin-top: 4px;">${item.additionalInfo}</div>` : ''}
            </td>
            <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: center;">${item.quantity}</td>
            <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: right;">${formatCurrency(item.unitPrice || item.price)}</td>
            <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: right;">${item.taxRate ? (item.taxRate * 100).toFixed(1) : '0.0'}%</td>
            <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: 500;">${formatCurrency(item.total || item.lineTotal)}</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;
}

function generateInvoiceHTML(
  invoiceData: Invoice | InvoiceFormData,
  customerName?: string,
  customerAddress?: any
): string {
  const isFormData = !('invoiceNumber' in invoiceData);
  const invoiceNumber = isFormData ? 'DRAFT' : invoiceData.invoiceNumber;
  const issueDate = new Date(invoiceData.issueDate).toLocaleDateString();
  const dueDate = new Date(invoiceData.dueDate).toLocaleDateString();
  
  // Get customer info
  const custName = customerName || (isFormData ? 'Customer Name' : (invoiceData as Invoice).customerName) || 'Customer Name';
  const custAddress = customerAddress || (isFormData ? null : (invoiceData as Invoice).customerAddress);

  return `
    <div style="max-width: 794px; margin: 0 auto; background: white; padding: 0;">
      <!-- Header -->
      <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 40px; border-bottom: 2px solid #2563eb; padding-bottom: 20px;">
        <div>
          <h1 style="margin: 0; font-size: 32px; font-weight: bold; color: #2563eb;">INVOICE</h1>
        </div>
        <div style="text-align: right;">
          <div style="font-size: 14px; margin-bottom: 5px;"><strong>Invoice #:</strong> ${invoiceNumber}</div>
          <div style="font-size: 14px; margin-bottom: 5px;"><strong>Date:</strong> ${issueDate}</div>
          <div style="font-size: 14px; margin-bottom: 5px;"><strong>Due Date:</strong> ${dueDate}</div>
          <div style="font-size: 14px;"><strong>Status:</strong> <span style="color: ${invoiceData.status.color};">${invoiceData.status.name}</span></div>
        </div>
      </div>

      <!-- Company and Customer Info -->
      <div style="display: flex; justify-content: space-between; margin-bottom: 40px;">
        <div style="width: 45%;">
          <h3 style="margin: 0 0 10px 0; font-size: 16px; color: #374151;">From:</h3>
          <div style="font-size: 12px; line-height: 1.6;">
            <strong>Your Company Name</strong><br>
            123 Business Street<br>
            City, State 12345<br>
            Phone: (*************<br>
            Email: <EMAIL>
          </div>
        </div>
        <div style="width: 45%;">
          <h3 style="margin: 0 0 10px 0; font-size: 16px; color: #374151;">Bill To:</h3>
          <div style="font-size: 12px; line-height: 1.6;">
            <strong>${custName}</strong><br>
            ${custAddress ? `
              ${custAddress.street}<br>
              ${custAddress.city}, ${custAddress.state} ${custAddress.zipCode}<br>
              ${custAddress.country}
            ` : 'Customer Address'}
          </div>
        </div>
      </div>

      <!-- Custom Header Fields -->
      ${invoiceData.customHeaderFields && invoiceData.customHeaderFields.length > 0 ? `
        <div style="margin-bottom: 30px;">
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
            ${invoiceData.customHeaderFields.map(field => `
              <div style="font-size: 12px;">
                <strong>${field.label}:</strong> ${field.value}
              </div>
            `).join('')}
          </div>
        </div>
      ` : ''}

      <!-- Line Items Table -->
      <div style="margin-bottom: 30px;">
        ${generateLineItemsHTML(invoiceData.lineItems)}
      </div>

      <!-- Totals -->
      <div style="display: flex; justify-content: flex-end; margin-bottom: 40px;">
        <div style="
          width: 350px;
          margin-top: 24px;
          padding: 20px;
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
        ">
          <div style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
          ">
            <span style="color: #6b7280; font-weight: 500;">Subtotal:</span>
            <span style="font-weight: 500; color: #000; text-align: right;">${formatCurrency(invoiceData.subtotal)}</span>
          </div>
          <div style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
          ">
            <span style="color: #6b7280; font-weight: 500;">Tax:</span>
            <span style="font-weight: 500; color: #000; text-align: right;">${formatCurrency(invoiceData.taxAmount)}</span>
          </div>
          ${invoiceData.discountAmount > 0 ? `
            <div style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 0;
              border-bottom: 1px solid #e5e7eb;
            ">
              <span style="color: #6b7280; font-weight: 500;">Discount:</span>
              <span style="font-weight: 500; color: #000; text-align: right;">-${formatCurrency(invoiceData.discountAmount)}</span>
            </div>
          ` : ''}
          <div style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0 8px 0;
            border-top: 1px solid #e5e7eb;
            margin-top: 8px;
            font-weight: 600;
            font-size: 1.1em;
            color: #000;
          ">
            <span style="color: #6b7280; font-weight: 500;">Total:</span>
            <span style="font-weight: 500; color: #000; text-align: right;">${formatCurrency(invoiceData.totalAmount)}</span>
          </div>
        </div>
      </div>

      <!-- Notes and Terms -->
      <div style="display: flex; justify-content: space-between; gap: 30px;">
        ${invoiceData.notes ? `
          <div style="width: 48%;">
            <h4 style="margin: 0 0 10px 0; font-size: 14px; color: #374151;">Notes:</h4>
            <div style="font-size: 11px; line-height: 1.5; color: #6b7280;">
              ${invoiceData.notes.replace(/\n/g, '<br>')}
            </div>
          </div>
        ` : ''}
        ${invoiceData.terms ? `
          <div style="width: 48%;">
            <h4 style="margin: 0 0 10px 0; font-size: 14px; color: #374151;">Terms & Conditions:</h4>
            <div style="font-size: 11px; line-height: 1.5; color: #6b7280;">
              ${invoiceData.terms.replace(/\n/g, '<br>')}
            </div>
          </div>
        ` : ''}
      </div>

      <!-- Footer -->
      <div style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; font-size: 10px; color: #9ca3af;">
        Thank you for your business!
      </div>
    </div>
  `;
}

// New function for generating PDFs with layout format and text selectability
export async function generateInvoicePDFFromLayout(
  invoice: ApiInvoice,
  customerName?: string,
  customerAddress?: any
): Promise<void> {
  try {
    // Create PDF with A4 dimensions
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Set up fonts and colors
    pdf.setFont('helvetica');
    pdf.setFontSize(12);

    let yPosition = 20;
    const leftMargin = 20;
    const rightMargin = 190;
    const pageWidth = 210;
    const columnWidth = (pageWidth - 40) / 2;

    // Row 1: Invoice Header with border
    // Add header border line
    pdf.setDrawColor(37, 99, 235); // Primary color
    pdf.setLineWidth(0.5);
    pdf.line(leftMargin, yPosition + 15, rightMargin, yPosition + 15);

    pdf.setFontSize(32);
    pdf.setTextColor(37, 99, 235); // Primary color
    pdf.text('INVOICE', leftMargin, yPosition);

    // Right side header info
    pdf.setFontSize(14);
    pdf.setTextColor(0, 0, 0); // Black color
    const invoiceNumberLabel = 'Invoice #:';
    const invoiceNumberStr = String(invoice.invoiceNumber || 'Draft');
    pdf.setFont('helvetica', 'bold');
    pdf.text(invoiceNumberLabel, rightMargin - pdf.getTextWidth(invoiceNumberLabel + ' ' + invoiceNumberStr), yPosition - 10);
    pdf.setFont('helvetica', 'normal');
    pdf.text(invoiceNumberStr, rightMargin - pdf.getTextWidth(invoiceNumberStr), yPosition - 10);

    // Date info
    const issueDateStr = invoice.issueDate ? new Date(invoice.issueDate).toLocaleDateString() : 'N/A';
    const dueDateStr = invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString() : 'N/A';

    pdf.setFont('helvetica', 'bold');
    pdf.text('Date:', rightMargin - pdf.getTextWidth('Date: ' + issueDateStr), yPosition - 5);
    pdf.setFont('helvetica', 'normal');
    pdf.text(issueDateStr, rightMargin - pdf.getTextWidth(issueDateStr), yPosition - 5);

    pdf.setFont('helvetica', 'bold');
    pdf.text('Due Date:', rightMargin - pdf.getTextWidth('Due Date: ' + dueDateStr), yPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(dueDateStr, rightMargin - pdf.getTextWidth(dueDateStr), yPosition);

    // Status
    const statusDisplay = getStatusDisplay(invoice.status);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Status:', rightMargin - pdf.getTextWidth('Status: ' + statusDisplay.name), yPosition + 5);
    pdf.setTextColor(statusDisplay.color);
    pdf.setFont('helvetica', 'normal');
    pdf.text(statusDisplay.name, rightMargin - pdf.getTextWidth(statusDisplay.name), yPosition + 5);

    yPosition += 40;

    // Row 2: Company and Customer Info
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0);

    // Company details (left column)
    pdf.setFont('helvetica', 'bold');
    pdf.text('From:', leftMargin, yPosition);
    pdf.setFont('helvetica', 'normal');
    yPosition += 5;
    pdf.text('Your Company Name', leftMargin, yPosition);
    yPosition += 4;
    pdf.text('123 Business Street', leftMargin, yPosition);
    yPosition += 4;
    pdf.text('City, State 12345', leftMargin, yPosition);
    yPosition += 4;
    pdf.text('Phone: (*************', leftMargin, yPosition);
    yPosition += 4;
    pdf.text('Email: <EMAIL>', leftMargin, yPosition);

    // Customer details (right column)
    const customerYStart = yPosition - 20;
    pdf.setFont('helvetica', 'bold');
    pdf.text('Bill To:', leftMargin + columnWidth, customerYStart);
    pdf.setFont('helvetica', 'normal');
    const custName = customerName || 'Customer Name';
    pdf.text(custName, leftMargin + columnWidth, customerYStart + 5);

    if (customerAddress) {
      pdf.text(customerAddress.street || 'Customer Address', leftMargin + columnWidth, customerYStart + 9);
      pdf.text(`${customerAddress.city || ''}, ${customerAddress.state || ''} ${customerAddress.zipCode || ''}`, leftMargin + columnWidth, customerYStart + 13);
    } else {
      pdf.text('Customer Address', leftMargin + columnWidth, customerYStart + 9);
      pdf.text('Email: N/A', leftMargin + columnWidth, customerYStart + 13);
    }

    yPosition += 20;

    // Row 3: Invoice Details (removed as dates are now in header)

    // Total amount (right side)
    const totalAmount = invoice.invoiceLines.reduce((sum, line) => sum + line.total, 0);
    pdf.setFont('helvetica', 'bold');
    const totalText = `Total Amount: ${formatCurrency(totalAmount)}`;
    pdf.text(totalText, rightMargin - pdf.getTextWidth(totalText), yPosition);

    yPosition += 20;

    // Row 4: Line Items Table
    yPosition += 10;

    // Table headers with borders
    const tableHeaders = ['Description', 'Qty', 'Unit Price', 'Tax %', 'Total'];
    const columnWidths = [80, 20, 30, 25, 30];
    let xPosition = leftMargin;

    // Header background
    pdf.setFillColor(243, 244, 246); // Light grey background
    pdf.rect(leftMargin, yPosition - 5, rightMargin - leftMargin, 12, 'F');

    // Header borders
    pdf.setDrawColor(229, 231, 235); // Border color
    pdf.setLineWidth(0.1);

    // Draw table header borders
    let currentX = leftMargin;
    for (let i = 0; i <= columnWidths.length; i++) {
      pdf.line(currentX, yPosition - 5, currentX, yPosition + 7);
      if (i < columnWidths.length) {
        currentX += columnWidths[i];
      }
    }
    pdf.line(leftMargin, yPosition - 5, rightMargin, yPosition - 5); // Top border
    pdf.line(leftMargin, yPosition + 7, rightMargin, yPosition + 7); // Bottom border

    // Header text
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0);
    xPosition = leftMargin;
    tableHeaders.forEach((header, index) => {
      const textAlign = index === 0 ? 'left' : index === 1 ? 'center' : 'right';
      if (textAlign === 'center') {
        pdf.text(header, xPosition + columnWidths[index] / 2, yPosition + 2, { align: 'center' });
      } else if (textAlign === 'right') {
        pdf.text(header, xPosition + columnWidths[index] - 2, yPosition + 2, { align: 'right' });
      } else {
        pdf.text(header, xPosition + 2, yPosition + 2);
      }
      xPosition += columnWidths[index];
    });

    yPosition += 12;

    // Table rows with borders
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(12);

    invoice.invoiceLines.forEach((item) => {
      const rowHeight = 12;

      // Draw row borders
      pdf.setDrawColor(229, 231, 235);
      pdf.setLineWidth(0.1);

      // Vertical borders
      let currentX = leftMargin;
      for (let i = 0; i <= columnWidths.length; i++) {
        pdf.line(currentX, yPosition, currentX, yPosition + rowHeight);
        if (i < columnWidths.length) {
          currentX += columnWidths[i];
        }
      }
      // Bottom border
      pdf.line(leftMargin, yPosition + rowHeight, rightMargin, yPosition + rowHeight);

      xPosition = leftMargin;

      // Description
      const descriptionText = item.description.length > 35 ? item.description.substring(0, 35) + '...' : item.description;
      pdf.setFont('helvetica', 'normal');
      pdf.text(descriptionText, xPosition + 2, yPosition + 8);
      xPosition += columnWidths[0];

      // Quantity (centered)
      pdf.text(item.quantity.toString(), xPosition + columnWidths[1] / 2, yPosition + 8, { align: 'center' });
      xPosition += columnWidths[1];

      // Unit Price (right aligned)
      pdf.text(formatCurrency(item.unitPrice), xPosition + columnWidths[2] - 2, yPosition + 8, { align: 'right' });
      xPosition += columnWidths[2];

      // Tax Rate (right aligned)
      pdf.text(`${(item.taxRate * 100).toFixed(1)}%`, xPosition + columnWidths[3] - 2, yPosition + 8, { align: 'right' });
      xPosition += columnWidths[3];

      // Total (right aligned, bold)
      pdf.setFont('helvetica', 'bold');
      pdf.text(formatCurrency(item.total), xPosition + columnWidths[4] - 2, yPosition + 8, { align: 'right' });

      yPosition += rowHeight;
    });

    yPosition += 20;

    // Totals section (matching template style)
    const totalsWidth = 75;
    const totalsX = rightMargin - totalsWidth;

    // Calculate totals
    const subtotal = invoice.invoiceLines.reduce((sum, line) => sum + (line.quantity * line.unitPrice), 0);
    const taxAmount = invoice.invoiceLines.reduce((sum, line) => sum + (line.quantity * line.unitPrice * line.taxRate), 0);
    const discountAmount = 0; // Add discount logic if needed
    const total = subtotal + taxAmount - discountAmount;

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(12);

    // Subtotal
    pdf.text('Subtotal:', totalsX - 5, yPosition, { align: 'right' });
    pdf.text(formatCurrency(subtotal), rightMargin, yPosition, { align: 'right' });
    pdf.setDrawColor(229, 231, 235);
    pdf.line(totalsX - 10, yPosition + 2, rightMargin, yPosition + 2);
    yPosition += 8;

    // Tax
    pdf.text('Tax:', totalsX - 5, yPosition, { align: 'right' });
    pdf.text(formatCurrency(taxAmount), rightMargin, yPosition, { align: 'right' });
    pdf.line(totalsX - 10, yPosition + 2, rightMargin, yPosition + 2);
    yPosition += 8;

    // Discount (if applicable)
    if (discountAmount > 0) {
      pdf.setTextColor(220, 38, 38); // Red color for discount
      pdf.text('Discount:', totalsX - 5, yPosition, { align: 'right' });
      pdf.text(`-${formatCurrency(discountAmount)}`, rightMargin, yPosition, { align: 'right' });
      pdf.line(totalsX - 10, yPosition + 2, rightMargin, yPosition + 2);
      yPosition += 8;
      pdf.setTextColor(0, 0, 0); // Reset to black
    }

    // Total
    pdf.setDrawColor(37, 99, 235); // Primary color
    pdf.setLineWidth(0.5);
    pdf.line(totalsX - 10, yPosition, rightMargin, yPosition);
    yPosition += 5;

    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(16);
    pdf.setTextColor(37, 99, 235); // Primary color
    pdf.text('Total:', totalsX - 5, yPosition, { align: 'right' });
    pdf.text(formatCurrency(total), rightMargin, yPosition, { align: 'right' });

    yPosition += 20;

    // Row 5: Notes and Terms
    if (invoice.notes || invoice.paymentTerms) {
      let notesHeight = 0;

      if (invoice.notes) {
        pdf.setFont('helvetica', 'bold');
        pdf.text('Notes:', leftMargin, yPosition);
        pdf.setFont('helvetica', 'normal');
        yPosition += 5;
        const notesLines = pdf.splitTextToSize(invoice.notes, columnWidth - 10);
        pdf.text(notesLines, leftMargin, yPosition);
        notesHeight = notesLines.length * 4;
        yPosition += notesHeight;
      }

      if (invoice.paymentTerms) {
        const termsYPosition = invoice.notes ? yPosition - notesHeight : yPosition;
        pdf.setFont('helvetica', 'bold');
        pdf.text('Payment Terms:', leftMargin + columnWidth, termsYPosition);
        pdf.setFont('helvetica', 'normal');
        const termsLines = pdf.splitTextToSize(invoice.paymentTerms, columnWidth - 10);
        pdf.text(termsLines, leftMargin + columnWidth, termsYPosition + 5);
      }
    }

    // Footer
    yPosition += 30;
    pdf.setDrawColor(229, 231, 235);
    pdf.setLineWidth(0.1);
    pdf.line(leftMargin, yPosition, rightMargin, yPosition);
    yPosition += 10;

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);
    pdf.setTextColor(156, 163, 175); // Light grey color
    const footerText = 'Thank you for your business!';
    const footerX = leftMargin + (rightMargin - leftMargin) / 2;
    pdf.text(footerText, footerX, yPosition, { align: 'center' });

    // Generate filename and save
    const filename = `${invoice.invoiceNumber || 'Draft'}.pdf`;
    pdf.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}