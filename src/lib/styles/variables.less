

// Less variables
@br: 10px;
@shadow: 0 5px 30px 0 @primary-fade;
@shadow-small: 0 1px 2px 0 @primary-fade;
@black: #222;
@white: #fff;
@grey: #4f5561;
@darkgrey: #6e7583;

@solid-secondary-fade: #e1ebfc;
@white-on-dark: #fff;

// Color system using HSL
@primary-hs: 236 46%; // the base color
@primary-l: 20%; // the initial lightness

@secondary-hs: 220 92%; // the base color
@secondary-l: 55%; // the initial lightness

@tertiary-hs: 11 100%; // the base color
@tertiary-l: 68%; // the initial lightness

@bg: hsl(220deg 60% 98.04%);
@border: hsl(240, 55%, 89%);

@cal-cell-h: 36px;

// Red variations
@red-hs: 0 100%; // the base color for red
@red-l: 50%; // the initial lightness for red

@red: hsl(@red-hs @red-l);
@red-darker: hsl(@red-hs (@red-l - 5%));
@red-darkest: hsl(@red-hs (@red-l - 10%));
@red-fade: hsla(@red-hs, @red-l, 10%);
@red-fade2: hsla(@red-hs, @red-l, 20%);
@red-fade3: hsla(@red-hs, @red-l, 5%);

// Yellow variations
@yellow-hs: 50 100%; // Hue for yellow
@yellow-l: 50%; // Lightness for yellow

@yellow: hsl(@yellow-hs @yellow-l);
@yellow-darker: hsl(@yellow-hs (@yellow-l - 5%));
@yellow-darkest: hsl(@yellow-hs (@yellow-l - 10%));
@yellow-fade: hsla(@yellow-hs, @yellow-l, 10%);
@yellow-fade2: hsla(@yellow-hs, @yellow-l, 20%);
@yellow-fade3: hsla(@yellow-hs, @yellow-l, 5%);

// Orange variations
@orange-hs: 30 100%; // Hue for orange
@orange-l: 50%; // Lightness for orange

@orange: hsl(@orange-hs @orange-l);
@orange-darker: hsl(@orange-hs (@orange-l - 5%));
@orange-darkest: hsl(@orange-hs (@orange-l - 10%));
@orange-fade: hsla(@orange-hs, @orange-l, 10%);
@orange-fade2: hsla(@orange-hs, @orange-l, 20%);
@orange-fade3: hsla(@orange-hs, @orange-l, 5%);

// Green variations
@green-hs: 156 100%; // Hue for green
@green-l: 27%; // Lightness for green

@green: hsl(@green-hs @green-l);
@green-darker: hsl(@green-hs (@green-l - 5%));
@green-darkest: hsl(@green-hs (@green-l - 10%));
@green-fade: hsla(@green-hs, @green-l, 10%);
@green-fade2: hsla(@green-hs, @green-l, 20%);
@green-fade3: hsla(@green-hs, @green-l, 5%);

// Primary variations
@primary: hsl(@primary-hs @primary-l);
@primary-darker: hsl(@primary-hs (@primary-l - 5%));
@primary-darkest: hsl(@primary-hs (@primary-l - 10%));
@primary-fade: hsla(@primary-hs, @primary-l, 10%);
@primary-fade2: hsla(@primary-hs, @primary-l, 20%);
@primary-fade3: hsla(@primary-hs, @primary-l, 5%);

// Secondary variations
@secondary: hsl(@secondary-hs @secondary-l);
@secondary-darker: hsl(@secondary-hs (@secondary-l - 5%));
@secondary-darkest: hsl(@secondary-hs (@secondary-l - 10%));
@secondary-fade: hsla(@secondary-hs, @secondary-l, 10%);
@secondary-fade2: hsla(@secondary-hs, @secondary-l, 20%);
@secondary-fade3: hsla(@secondary-hs, @secondary-l, 5%);
@secondary-fade9: hsla(@secondary-hs, @secondary-l, 90%);

@secondary-fade-solid: hsl(@secondary-hs 90%);
@secondary-fade-solid2: hsl(@secondary-hs 90%);

// Tertiary variations
@tertiary: hsl(@tertiary-hs @tertiary-l);
@tertiary-darker: hsl(@tertiary-hs (@tertiary-l - 5%));
@tertiary-darkest: hsl(@tertiary-hs (@tertiary-l - 10%));
@tertiary-fade: hsla(@tertiary-hs, @tertiary-l, 10%);
@tertiary-fade2: hsla(@tertiary-hs, @tertiary-l, 20%);
@tertiary-fade3: hsla(@tertiary-hs, @tertiary-l, 5%);


:root {
        --br: 10px;
    --shadow: 0 5px 30px 0 var(--primary-fade);
    --shadow-small: 0 1px 2px 0 var(--primary-fade);
    --black: #222;
    --white: #fff;
    --grey: #4f5561;
    --darkgrey: #6e7583;

    --solid-secondary-fade: #e1ebfc;
    --white-on-dark: #fff;

    --primary-hs: 236 46%; /*the base color*/
    --primary-l: 20%; /*the initial lightness*/
    
    --secondary-hs: 220 92%; /*the base color*/
    --secondary-l: 55%; /*the initial lightness*/

    --tertiary-hs: 11 100%; /*the base color*/
    --tertiary-l: 68%; /*the initial lightness*/
    
    --bg: hsl(220deg 60% 98.04%);
    --border: hsl(240, 55%, 89%);

    --cal-cell-h: 36px;

    --red-hs: 0 100%; /*the base color for red*/
    --red-l: 50%; /*the initial lightness for red*/
    
    --red: hsl(var(--red-hs) var(--red-l));
    --red-darker: hsl(var(--red-hs) calc(var(--red-l) - 5%));
    --red-darkest: hsl(var(--red-hs) calc(var(--red-l) - 10%));
    --red-fade: hsl(var(--red-hs) var(--red-l) / 10%); 
    --red-fade2: hsl(var(--red-hs) var(--red-l) / 20%); 
    --red-fade3: hsl(var(--red-hs) var(--red-l) / 5%);

    --yellow-hs: 50 100%; /* Hue for yellow */
    --yellow-l: 50%; /* Lightness for yellow */

    --yellow: hsl(var(--yellow-hs) var(--yellow-l));
    --yellow-darker: hsl(var(--yellow-hs) calc(var(--yellow-l) - 5%));
    --yellow-darkest: hsl(var(--yellow-hs) calc(var(--yellow-l) - 10%));
    --yellow-fade: hsl(var(--yellow-hs) var(--yellow-l) / 10%);
    --yellow-fade2: hsl(var(--yellow-hs) var(--yellow-l) / 20%);
    --yellow-fade3: hsl(var(--yellow-hs) var(--yellow-l) / 5%);

    --orange-hs: 30 100%; /* Hue for orange */
    --orange-l: 50%; /* Lightness for orange */

    --orange: hsl(var(--orange-hs) var(--orange-l));
    --orange-darker: hsl(var(--orange-hs) calc(var(--orange-l) - 5%));
    --orange-darkest: hsl(var(--orange-hs) calc(var(--orange-l) - 10%));
    --orange-fade: hsl(var(--orange-hs) var(--orange-l) / 10%);
    --orange-fade2: hsl(var(--orange-hs) var(--orange-l) / 20%);
    --orange-fade3: hsl(var(--orange-hs) var(--orange-l) / 5%);

    --green-hs: 156 100%; /* Hue for green */
    --green-l: 27%; /* Lightness for green */

    --green: hsl(var(--green-hs) var(--green-l));
    --green-darker: hsl(var(--green-hs) calc(var(--green-l) - 5%));
    --green-darkest: hsl(var(--green-hs) calc(var(--green-l) - 10%));
    --green-fade: hsl(var(--green-hs) var(--green-l) / 10%);
    --green-fade2: hsl(var(--green-hs) var(--green-l) / 20%);
    --green-fade3: hsl(var(--green-hs) var(--green-l) / 5%);

    --primary: hsl(var(--primary-hs) var(--primary-l));
    --primary-darker: hsl(var(--primary-hs) calc(var(--primary-l) - 5%));
    --primary-darkest: hsl(var(--primary-hs) calc(var(--primary-l) - 10%));
    --primary-fade: hsl(var(--primary-hs) var(--primary-l) / 10%); 
    --primary-fade2: hsl(var(--primary-hs) var(--primary-l) / 20%); 
    --primary-fade3: hsl(var(--primary-hs) var(--primary-l) / 5%); 

    --secondary: hsl(var(--secondary-hs) var(--secondary-l));
    --secondary-darker: hsl(var(--secondary-hs) calc(var(--secondary-l) - 5%));
    --secondary-darkest: hsl(var(--secondary-hs) calc(var(--secondary-l) - 10%));
    --secondary-fade: hsl(var(--secondary-hs) var(--secondary-l) / 10%); 
    --secondary-fade2: hsl(var(--secondary-hs) var(--secondary-l) / 20%); 
    --secondary-fade3: hsl(var(--secondary-hs) var(--secondary-l) / 5%);
    --secondary-fade9: hsl(var(--secondary-hs) var(--secondary-l) / 90%);

    --secondary-fade-solid: hsl(var(--secondary-hs) 90%); 
    --secondary-fade-solid2: hsl(var(--secondary-hs) 90%); 

    --tertiary: hsl(var(--tertiary-hs) var(--tertiary-l));
    --tertiary-darker: hsl(var(--tertiary-hs) calc(var(--tertiary-l) - 5%));
    --tertiary-darkest: hsl(var(--tertiary-hs) calc(var(--tertiary-l) - 10%));
    --tertiary-fade: hsl(var(--tertiary-hs) var(--tertiary-l) / 10%); 
    --tertiary-fade2: hsl(var(--tertiary-hs) var(--tertiary-l) / 20%); 
    --tertiary-fade3: hsl(var(--tertiary-hs) var(--tertiary-l) / 5%);

    --yellow: hsl(50, 100%, 90%);
    --yellow-darker: hsl(50, 100%, 75%);
    --yellow-darkest: hsl(50, 100%, 25%);
}