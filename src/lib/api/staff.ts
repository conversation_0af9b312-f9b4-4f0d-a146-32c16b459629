// Staff API - Staff Management Functionality
// TODO: API Integration - Replace local storage with actual API endpoints
// Expected request format: { staff: StaffData }
// Expected response format: { success: boolean, data: Staff, message?: string }

export interface Staff {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string; // Computed field
  email: string;
  phone: string;
  address?: StaffAddress;
  position: string;
  department?: string;
  hireDate: string;
  isActive: boolean;
  wageInfo: WageInfo;
  wageHistory: WageHistoryEntry[];
  skills: string[];
  certifications: Certification[];
  availability: StaffAvailability;
  emergencyContact?: EmergencyContact;
  notes?: string;
  profileImageUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface StaffAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface WageInfo {
  type: 'Hourly' | 'Salary' | 'Per Job';
  rate: number;
  currency: string;
  effectiveDate: string;
  overtimeRate?: number; // For hourly workers
  annualSalary?: number; // For salary workers
}

export interface WageHistoryEntry {
  id: string;
  type: 'Hourly' | 'Salary' | 'Per Job';
  rate: number;
  effectiveDate: string;
  endDate?: string;
  reason?: string;
  createdAt: string;
}

export interface Certification {
  id: string;
  name: string;
  issuingOrganization: string;
  issueDate: string;
  expiryDate?: string;
  certificateNumber?: string;
  isActive: boolean;
}

export interface StaffAvailability {
  monday: DayAvailability;
  tuesday: DayAvailability;
  wednesday: DayAvailability;
  thursday: DayAvailability;
  friday: DayAvailability;
  saturday: DayAvailability;
  sunday: DayAvailability;
  timeOff: TimeOffEntry[];
}

export interface DayAvailability {
  isAvailable: boolean;
  startTime?: string; // HH:MM format
  endTime?: string; // HH:MM format
  breaks?: BreakPeriod[];
}

export interface BreakPeriod {
  startTime: string;
  endTime: string;
  type: 'Lunch' | 'Break' | 'Other';
}

export interface TimeOffEntry {
  id: string;
  startDate: string;
  endDate: string;
  type: 'Vacation' | 'Sick' | 'Personal' | 'Holiday' | 'Other';
  status: 'Pending' | 'Approved' | 'Rejected';
  reason?: string;
  approvedBy?: string;
  createdAt: string;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
}

// Staff performance and time tracking
export interface StaffTimeEntry {
  id: string;
  staffId: string;
  jobId?: string;
  date: string;
  startTime: string;
  endTime?: string;
  totalHours?: number;
  breakTime?: number; // in minutes
  description?: string;
  status: 'Active' | 'Completed' | 'Approved';
  createdAt: string;
  updatedAt: string;
}

const STAFF_STORAGE_KEY = 'ejp_staff';
const STAFF_TIME_ENTRIES_STORAGE_KEY = 'ejp_staff_time_entries';

// Helper functions
function getStaffFromStorage(): Staff[] {
  const stored = localStorage.getItem(STAFF_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveStaffToStorage(staff: Staff[]): void {
  localStorage.setItem(STAFF_STORAGE_KEY, JSON.stringify(staff));
}

function getTimeEntriesFromStorage(): StaffTimeEntry[] {
  const stored = localStorage.getItem(STAFF_TIME_ENTRIES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveTimeEntriesToStorage(entries: StaffTimeEntry[]): void {
  localStorage.setItem(STAFF_TIME_ENTRIES_STORAGE_KEY, JSON.stringify(entries));
}

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Staff CRUD operations
export async function getStaff(): Promise<Staff[]> {
  // TODO: API Integration - GET /api/staff
  // Expected response: { success: boolean, data: Staff[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      // Compute fullName for each staff member
      staff.forEach(s => {
        s.fullName = `${s.firstName} ${s.lastName}`;
      });
      resolve(staff);
    }, 100);
  });
}

export async function getStaffById(id: string): Promise<Staff | null> {
  // TODO: API Integration - GET /api/staff/{id}
  // Expected response: { success: boolean, data: Staff }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      const staffMember = staff.find(s => s.id === id) || null;
      if (staffMember) {
        staffMember.fullName = `${staffMember.firstName} ${staffMember.lastName}`;
      }
      resolve(staffMember);
    }, 100);
  });
}

export async function getActiveStaff(): Promise<Staff[]> {
  // TODO: API Integration - GET /api/staff?active=true
  // Expected response: { success: boolean, data: Staff[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      const activeStaff = staff.filter(s => s.isActive);
      activeStaff.forEach(s => {
        s.fullName = `${s.firstName} ${s.lastName}`;
      });
      resolve(activeStaff);
    }, 100);
  });
}

export async function createStaff(staffData: Omit<Staff, 'id' | 'fullName' | 'createdAt' | 'updatedAt'>): Promise<Staff> {
  // TODO: API Integration - POST /api/staff
  // Expected request: { staff: StaffData }
  // Expected response: { success: boolean, data: Staff }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      const newStaff: Staff = {
        ...staffData,
        id: generateId(),
        fullName: `${staffData.firstName} ${staffData.lastName}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      staff.push(newStaff);
      saveStaffToStorage(staff);
      resolve(newStaff);
    }, 100);
  });
}

export async function updateStaff(id: string, updates: Partial<Staff>): Promise<Staff> {
  // TODO: API Integration - PUT /api/staff/{id}
  // Expected request: { staff: Partial<Staff> }
  // Expected response: { success: boolean, data: Staff }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      const index = staff.findIndex(s => s.id === id);
      
      if (index === -1) {
        reject(new Error('Staff member not found'));
        return;
      }
      
      staff[index] = {
        ...staff[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      // Update computed fullName if first or last name changed
      if (updates.firstName || updates.lastName) {
        staff[index].fullName = `${staff[index].firstName} ${staff[index].lastName}`;
      }
      
      saveStaffToStorage(staff);
      resolve(staff[index]);
    }, 100);
  });
}

export async function deleteStaff(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/staff/{id}
  // Expected response: { success: boolean }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      const index = staff.findIndex(s => s.id === id);
      
      if (index === -1) {
        reject(new Error('Staff member not found'));
        return;
      }
      
      staff.splice(index, 1);
      saveStaffToStorage(staff);
      resolve();
    }, 100);
  });
}

// Wage management
export async function updateStaffWage(staffId: string, newWageInfo: WageInfo, reason?: string): Promise<Staff> {
  // TODO: API Integration - PUT /api/staff/{id}/wage
  // Expected request: { wageInfo: WageInfo, reason?: string }
  // Expected response: { success: boolean, data: Staff }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      const staffMember = staff.find(s => s.id === staffId);
      
      if (!staffMember) {
        reject(new Error('Staff member not found'));
        return;
      }
      
      // Add current wage to history
      const historyEntry: WageHistoryEntry = {
        id: generateId(),
        type: staffMember.wageInfo.type,
        rate: staffMember.wageInfo.rate,
        effectiveDate: staffMember.wageInfo.effectiveDate,
        endDate: new Date().toISOString(),
        reason,
        createdAt: new Date().toISOString()
      };
      
      staffMember.wageHistory.push(historyEntry);
      staffMember.wageInfo = newWageInfo;
      staffMember.updatedAt = new Date().toISOString();
      
      saveStaffToStorage(staff);
      resolve(staffMember);
    }, 100);
  });
}

// Time tracking
export async function getStaffTimeEntries(staffId?: string, startDate?: string, endDate?: string): Promise<StaffTimeEntry[]> {
  // TODO: API Integration - GET /api/staff/time-entries?staffId={staffId}&startDate={startDate}&endDate={endDate}
  // Expected response: { success: boolean, data: StaffTimeEntry[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      let entries = getTimeEntriesFromStorage();
      
      if (staffId) {
        entries = entries.filter(e => e.staffId === staffId);
      }
      
      if (startDate) {
        entries = entries.filter(e => e.date >= startDate);
      }
      
      if (endDate) {
        entries = entries.filter(e => e.date <= endDate);
      }
      
      resolve(entries);
    }, 100);
  });
}

export async function createTimeEntry(entryData: Omit<StaffTimeEntry, 'id' | 'createdAt' | 'updatedAt'>): Promise<StaffTimeEntry> {
  // TODO: API Integration - POST /api/staff/time-entries
  // Expected request: { timeEntry: StaffTimeEntryData }
  // Expected response: { success: boolean, data: StaffTimeEntry }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const entries = getTimeEntriesFromStorage();
      const newEntry: StaffTimeEntry = {
        ...entryData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      entries.push(newEntry);
      saveTimeEntriesToStorage(entries);
      resolve(newEntry);
    }, 100);
  });
}

export async function updateTimeEntry(id: string, updates: Partial<StaffTimeEntry>): Promise<StaffTimeEntry> {
  // TODO: API Integration - PUT /api/staff/time-entries/{id}
  // Expected request: { timeEntry: Partial<StaffTimeEntry> }
  // Expected response: { success: boolean, data: StaffTimeEntry }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const entries = getTimeEntriesFromStorage();
      const index = entries.findIndex(e => e.id === id);
      
      if (index === -1) {
        reject(new Error('Time entry not found'));
        return;
      }
      
      entries[index] = {
        ...entries[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      saveTimeEntriesToStorage(entries);
      resolve(entries[index]);
    }, 100);
  });
}

// Staff availability
export async function updateStaffAvailability(staffId: string, availability: StaffAvailability): Promise<Staff> {
  // TODO: API Integration - PUT /api/staff/{id}/availability
  // Expected request: { availability: StaffAvailability }
  // Expected response: { success: boolean, data: Staff }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      const staffMember = staff.find(s => s.id === staffId);
      
      if (!staffMember) {
        reject(new Error('Staff member not found'));
        return;
      }
      
      staffMember.availability = availability;
      staffMember.updatedAt = new Date().toISOString();
      
      saveStaffToStorage(staff);
      resolve(staffMember);
    }, 100);
  });
}

// Time off management
export async function requestTimeOff(staffId: string, timeOffData: Omit<TimeOffEntry, 'id' | 'status' | 'createdAt'>): Promise<TimeOffEntry> {
  // TODO: API Integration - POST /api/staff/{id}/time-off
  // Expected request: { timeOff: TimeOffData }
  // Expected response: { success: boolean, data: TimeOffEntry }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      const staffMember = staff.find(s => s.id === staffId);
      
      if (!staffMember) {
        reject(new Error('Staff member not found'));
        return;
      }
      
      const newTimeOff: TimeOffEntry = {
        ...timeOffData,
        id: generateId(),
        status: 'Pending',
        createdAt: new Date().toISOString()
      };
      
      staffMember.availability.timeOff.push(newTimeOff);
      staffMember.updatedAt = new Date().toISOString();
      
      saveStaffToStorage(staff);
      resolve(newTimeOff);
    }, 100);
  });
}

export async function approveTimeOff(staffId: string, timeOffId: string, approvedBy: string): Promise<TimeOffEntry> {
  // TODO: API Integration - PUT /api/staff/{id}/time-off/{timeOffId}/approve
  // Expected request: { approvedBy: string }
  // Expected response: { success: boolean, data: TimeOffEntry }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      const staffMember = staff.find(s => s.id === staffId);
      
      if (!staffMember) {
        reject(new Error('Staff member not found'));
        return;
      }
      
      const timeOff = staffMember.availability.timeOff.find(t => t.id === timeOffId);
      if (!timeOff) {
        reject(new Error('Time off request not found'));
        return;
      }
      
      timeOff.status = 'Approved';
      timeOff.approvedBy = approvedBy;
      staffMember.updatedAt = new Date().toISOString();
      
      saveStaffToStorage(staff);
      resolve(timeOff);
    }, 100);
  });
}

// Get staff available for a specific date/time
export async function getAvailableStaff(date: string, startTime?: string, endTime?: string): Promise<Staff[]> {
  // TODO: API Integration - GET /api/staff/available?date={date}&startTime={startTime}&endTime={endTime}
  // Expected response: { success: boolean, data: Staff[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const staff = getStaffFromStorage();
      const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase() as keyof Omit<StaffAvailability, 'timeOff'>;
      
      const availableStaff = staff.filter(s => {
        if (!s.isActive) return false;
        
        // Check if staff has time off on this date
        const hasTimeOff = s.availability.timeOff.some(timeOff => 
          timeOff.status === 'Approved' &&
          date >= timeOff.startDate &&
          date <= timeOff.endDate
        );
        
        if (hasTimeOff) return false;
        
        // Check day availability
        const dayAvailability = s.availability[dayOfWeek] as DayAvailability;
        if (!dayAvailability || !dayAvailability.isAvailable) return false;
        
        // If specific times are provided, check time availability
        if (startTime && endTime && dayAvailability.startTime && dayAvailability.endTime) {
          return startTime >= dayAvailability.startTime && endTime <= dayAvailability.endTime;
        }
        
        return true;
      });
      
      availableStaff.forEach(s => {
        s.fullName = `${s.firstName} ${s.lastName}`;
      });
      
      resolve(availableStaff);
    }, 100);
  });
} 