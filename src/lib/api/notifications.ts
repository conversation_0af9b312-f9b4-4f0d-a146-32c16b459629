// Email and SMS notification management API

// Helper function to generate unique IDs
function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  type: 'appointment_confirmation' | 'appointment_reminder' | 'appointment_cancellation' | 'invoice_new' | 'invoice_reminder' | 'custom';
  isDefault: boolean;
  variables: TemplateVariable[];
  createdAt: string;
  updatedAt: string;
}

export interface SMSTemplate {
  id: string;
  name: string;
  content: string;
  type: 'appointment_confirmation' | 'appointment_reminder' | 'appointment_cancellation' | 'custom';
  isDefault: boolean;
  variables: TemplateVariable[];
  createdAt: string;
  updatedAt: string;
}

export interface TemplateVariable {
  key: string;
  label: string;
  description: string;
  example: string;
}

export interface NotificationSettings {
  id: string;
  emailEnabled: boolean;
  smsEnabled: boolean;
  appointmentConfirmation: {
    enabled: boolean;
    emailTemplateId?: string;
    smsTemplateId?: string;
    sendImmediately: boolean;
  };
  appointmentReminder: {
    enabled: boolean;
    emailTemplateId?: string;
    smsTemplateId?: string;
    hoursBeforeAppointment: number;
  };
  appointmentCancellation: {
    enabled: boolean;
    emailTemplateId?: string;
    smsTemplateId?: string;
  };
  invoiceNotifications: {
    enabled: boolean;
    emailTemplateId?: string;
    sendOnCreate: boolean;
    reminderEnabled: boolean;
    reminderDaysAfterDue: number;
    reminderTemplateId?: string;
  };
  updatedAt: string;
}

export interface EmailSendRequest {
  to: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  attachments?: EmailAttachment[];
}

export interface SMSSendRequest {
  to: string;
  content: string;
}

export interface EmailAttachment {
  filename: string;
  content: string; // Base64 encoded
  contentType: string;
}

export interface NotificationHistory {
  id: string;
  type: 'email' | 'sms';
  to: string;
  subject?: string;
  content: string;
  status: 'sent' | 'failed' | 'pending';
  sentAt?: string;
  errorMessage?: string;
  relatedId?: string; // ID of related appointment, invoice, etc.
  relatedType?: 'appointment' | 'invoice' | 'job';
}

// Default template variables
export const DEFAULT_TEMPLATE_VARIABLES: TemplateVariable[] = [
  { key: 'customer_name', label: 'Customer Name', description: 'Full name of the customer', example: 'John Smith' },
  { key: 'customer_first_name', label: 'Customer First Name', description: 'First name of the customer', example: 'John' },
  { key: 'company_name', label: 'Company Name', description: 'Name of your company', example: 'Easy Job Planner' },
  { key: 'appointment_date', label: 'Appointment Date', description: 'Date of the appointment', example: 'January 15, 2024' },
  { key: 'appointment_time', label: 'Appointment Time', description: 'Time of the appointment', example: '2:00 PM' },
  { key: 'appointment_duration', label: 'Appointment Duration', description: 'Duration of the appointment', example: '2 hours' },
  { key: 'service_description', label: 'Service Description', description: 'Description of the service', example: 'House cleaning' },
  { key: 'staff_name', label: 'Staff Name', description: 'Name of assigned staff member', example: 'Jane Doe' },
  { key: 'invoice_number', label: 'Invoice Number', description: 'Invoice number', example: 'INV-001' },
  { key: 'invoice_amount', label: 'Invoice Amount', description: 'Total invoice amount', example: '$150.00' },
  { key: 'invoice_due_date', label: 'Invoice Due Date', description: 'Invoice due date', example: 'February 15, 2024' },
  { key: 'payment_link', label: 'Payment Link', description: 'Link to pay the invoice', example: 'https://pay.example.com/inv123' }
];

// Storage keys
const EMAIL_TEMPLATES_STORAGE_KEY = 'ejp_email_templates';
const SMS_TEMPLATES_STORAGE_KEY = 'ejp_sms_templates';
const NOTIFICATION_SETTINGS_STORAGE_KEY = 'ejp_notification_settings';
const NOTIFICATION_HISTORY_STORAGE_KEY = 'ejp_notification_history';

// Helper functions
function getEmailTemplatesFromStorage(): EmailTemplate[] {
  const stored = localStorage.getItem(EMAIL_TEMPLATES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveEmailTemplatesToStorage(templates: EmailTemplate[]): void {
  localStorage.setItem(EMAIL_TEMPLATES_STORAGE_KEY, JSON.stringify(templates));
}

function getSMSTemplatesFromStorage(): SMSTemplate[] {
  const stored = localStorage.getItem(SMS_TEMPLATES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveSMSTemplatesToStorage(templates: SMSTemplate[]): void {
  localStorage.setItem(SMS_TEMPLATES_STORAGE_KEY, JSON.stringify(templates));
}

function getNotificationSettingsFromStorage(): NotificationSettings | null {
  const stored = localStorage.getItem(NOTIFICATION_SETTINGS_STORAGE_KEY);
  return stored ? JSON.parse(stored) : null;
}

function saveNotificationSettingsToStorage(settings: NotificationSettings): void {
  localStorage.setItem(NOTIFICATION_SETTINGS_STORAGE_KEY, JSON.stringify(settings));
}

function getNotificationHistoryFromStorage(): NotificationHistory[] {
  const stored = localStorage.getItem(NOTIFICATION_HISTORY_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveNotificationHistoryToStorage(history: NotificationHistory[]): void {
  localStorage.setItem(NOTIFICATION_HISTORY_STORAGE_KEY, JSON.stringify(history));
}

// Initialize default templates
async function initializeDefaultTemplates() {
  const emailTemplates = getEmailTemplatesFromStorage();
  const smsTemplates = getSMSTemplatesFromStorage();

  if (emailTemplates.length === 0) {
    const defaultEmailTemplates: Omit<EmailTemplate, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'Appointment Confirmation',
        subject: 'Appointment Confirmed - {{appointment_date}} at {{appointment_time}}',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">Appointment Confirmed</h2>
            <p>Dear {{customer_name}},</p>
            <p>Your appointment has been confirmed for:</p>
            <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p><strong>Date:</strong> {{appointment_date}}</p>
              <p><strong>Time:</strong> {{appointment_time}}</p>
              <p><strong>Duration:</strong> {{appointment_duration}}</p>
              <p><strong>Service:</strong> {{service_description}}</p>
              <p><strong>Staff:</strong> {{staff_name}}</p>
            </div>
            <p>If you need to reschedule or cancel, please contact us as soon as possible.</p>
            <p>Thank you for choosing {{company_name}}!</p>
          </div>
        `,
        textContent: 'Dear {{customer_name}}, your appointment has been confirmed for {{appointment_date}} at {{appointment_time}}. Service: {{service_description}}. Staff: {{staff_name}}. Thank you for choosing {{company_name}}!',
        type: 'appointment_confirmation',
        isDefault: true,
        variables: DEFAULT_TEMPLATE_VARIABLES
      },
      {
        name: 'Appointment Reminder',
        subject: 'Reminder: Appointment Tomorrow at {{appointment_time}}',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #f59e0b;">Appointment Reminder</h2>
            <p>Dear {{customer_name}},</p>
            <p>This is a friendly reminder about your upcoming appointment:</p>
            <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p><strong>Date:</strong> {{appointment_date}}</p>
              <p><strong>Time:</strong> {{appointment_time}}</p>
              <p><strong>Service:</strong> {{service_description}}</p>
              <p><strong>Staff:</strong> {{staff_name}}</p>
            </div>
            <p>We look forward to seeing you!</p>
            <p>Best regards,<br>{{company_name}}</p>
          </div>
        `,
        textContent: 'Reminder: Your appointment is scheduled for {{appointment_date}} at {{appointment_time}}. Service: {{service_description}}. See you soon! - {{company_name}}',
        type: 'appointment_reminder',
        isDefault: true,
        variables: DEFAULT_TEMPLATE_VARIABLES
      },
      {
        name: 'New Invoice',
        subject: 'Invoice {{invoice_number}} - {{invoice_amount}}',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">New Invoice</h2>
            <p>Dear {{customer_name}},</p>
            <p>Please find your invoice attached.</p>
            <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p><strong>Invoice Number:</strong> {{invoice_number}}</p>
              <p><strong>Amount:</strong> {{invoice_amount}}</p>
              <p><strong>Due Date:</strong> {{invoice_due_date}}</p>
            </div>
            <p>You can pay online using this link: <a href="{{payment_link}}">Pay Now</a></p>
            <p>Thank you for your business!</p>
            <p>Best regards,<br>{{company_name}}</p>
          </div>
        `,
        textContent: 'Dear {{customer_name}}, please find your invoice {{invoice_number}} for {{invoice_amount}} attached. Due date: {{invoice_due_date}}. Pay online: {{payment_link}}. Thank you! - {{company_name}}',
        type: 'invoice_new',
        isDefault: true,
        variables: DEFAULT_TEMPLATE_VARIABLES
      }
    ];

    for (const template of defaultEmailTemplates) {
      await createEmailTemplate(template);
    }
  }

  if (smsTemplates.length === 0) {
    const defaultSMSTemplates: Omit<SMSTemplate, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'Appointment Confirmation SMS',
        content: 'Hi {{customer_first_name}}! Your appointment is confirmed for {{appointment_date}} at {{appointment_time}}. Service: {{service_description}}. Thanks! - {{company_name}}',
        type: 'appointment_confirmation',
        isDefault: true,
        variables: DEFAULT_TEMPLATE_VARIABLES
      },
      {
        name: 'Appointment Reminder SMS',
        content: 'Reminder: Your appointment with {{company_name}} is tomorrow at {{appointment_time}}. See you then!',
        type: 'appointment_reminder',
        isDefault: true,
        variables: DEFAULT_TEMPLATE_VARIABLES
      }
    ];

    for (const template of defaultSMSTemplates) {
      await createSMSTemplate(template);
    }
  }
}

// Email Template API functions
export async function getEmailTemplates(): Promise<EmailTemplate[]> {
  // TODO: API Integration - GET /api/email-templates
  await initializeDefaultTemplates();
  return getEmailTemplatesFromStorage();
}

export async function createEmailTemplate(templateData: Omit<EmailTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<EmailTemplate> {
  // TODO: API Integration - POST /api/email-templates
  return new Promise((resolve) => {
    setTimeout(() => {
      const templates = getEmailTemplatesFromStorage();
      const newTemplate: EmailTemplate = {
        ...templateData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      templates.push(newTemplate);
      saveEmailTemplatesToStorage(templates);
      resolve(newTemplate);
    }, 100);
  });
}

export async function updateEmailTemplate(id: string, templateData: Partial<EmailTemplate>): Promise<EmailTemplate> {
  // TODO: API Integration - PUT /api/email-templates/{id}
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const templates = getEmailTemplatesFromStorage();
      const index = templates.findIndex(t => t.id === id);
      
      if (index === -1) {
        reject(new Error('Template not found'));
        return;
      }
      
      templates[index] = {
        ...templates[index],
        ...templateData,
        updatedAt: new Date().toISOString()
      };
      
      saveEmailTemplatesToStorage(templates);
      resolve(templates[index]);
    }, 100);
  });
}

export async function deleteEmailTemplate(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/email-templates/{id}
  return new Promise((resolve) => {
    setTimeout(() => {
      const templates = getEmailTemplatesFromStorage();
      const filteredTemplates = templates.filter(t => t.id !== id);
      saveEmailTemplatesToStorage(filteredTemplates);
      resolve();
    }, 100);
  });
}

// SMS Template API functions
export async function getSMSTemplates(): Promise<SMSTemplate[]> {
  // TODO: API Integration - GET /api/sms-templates
  await initializeDefaultTemplates();
  return getSMSTemplatesFromStorage();
}

export async function createSMSTemplate(templateData: Omit<SMSTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<SMSTemplate> {
  // TODO: API Integration - POST /api/sms-templates
  return new Promise((resolve) => {
    setTimeout(() => {
      const templates = getSMSTemplatesFromStorage();
      const newTemplate: SMSTemplate = {
        ...templateData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      templates.push(newTemplate);
      saveSMSTemplatesToStorage(templates);
      resolve(newTemplate);
    }, 100);
  });
}

export async function updateSMSTemplate(id: string, templateData: Partial<SMSTemplate>): Promise<SMSTemplate> {
  // TODO: API Integration - PUT /api/sms-templates/{id}
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const templates = getSMSTemplatesFromStorage();
      const index = templates.findIndex(t => t.id === id);
      
      if (index === -1) {
        reject(new Error('Template not found'));
        return;
      }
      
      templates[index] = {
        ...templates[index],
        ...templateData,
        updatedAt: new Date().toISOString()
      };
      
      saveSMSTemplatesToStorage(templates);
      resolve(templates[index]);
    }, 100);
  });
}

export async function deleteSMSTemplate(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/sms-templates/{id}
  return new Promise((resolve) => {
    setTimeout(() => {
      const templates = getSMSTemplatesFromStorage();
      const filteredTemplates = templates.filter(t => t.id !== id);
      saveSMSTemplatesToStorage(filteredTemplates);
      resolve();
    }, 100);
  });
}

// Notification Settings API functions
export async function getNotificationSettings(): Promise<NotificationSettings> {
  // TODO: API Integration - GET /api/notification-settings
  const settings = getNotificationSettingsFromStorage();
  
  if (!settings) {
    // Create default settings
    const defaultSettings: NotificationSettings = {
      id: generateId(),
      emailEnabled: true,
      smsEnabled: false,
      appointmentConfirmation: {
        enabled: true,
        sendImmediately: true
      },
      appointmentReminder: {
        enabled: true,
        hoursBeforeAppointment: 24
      },
      appointmentCancellation: {
        enabled: true
      },
      invoiceNotifications: {
        enabled: true,
        sendOnCreate: true,
        reminderEnabled: true,
        reminderDaysAfterDue: 7
      },
      updatedAt: new Date().toISOString()
    };
    
    await updateNotificationSettings(defaultSettings);
    return defaultSettings;
  }
  
  return settings;
}

export async function updateNotificationSettings(settings: NotificationSettings): Promise<NotificationSettings> {
  // TODO: API Integration - PUT /api/notification-settings
  return new Promise((resolve) => {
    setTimeout(() => {
      const updatedSettings = {
        ...settings,
        updatedAt: new Date().toISOString()
      };
      saveNotificationSettingsToStorage(updatedSettings);
      resolve(updatedSettings);
    }, 100);
  });
}

// Email sending functions
export async function sendEmail(request: EmailSendRequest): Promise<NotificationHistory> {
  // TODO: API Integration - POST /api/notifications/email
  return new Promise((resolve) => {
    setTimeout(() => {
      const history = getNotificationHistoryFromStorage();
      const newEntry: NotificationHistory = {
        id: generateId(),
        type: 'email',
        to: request.to,
        subject: request.subject,
        content: request.htmlContent,
        status: 'sent', // In real implementation, this would be based on actual sending result
        sentAt: new Date().toISOString()
      };
      
      history.push(newEntry);
      saveNotificationHistoryToStorage(history);
      resolve(newEntry);
    }, 1000); // Simulate network delay
  });
}

// SMS sending functions
export async function sendSMS(request: SMSSendRequest): Promise<NotificationHistory> {
  // TODO: API Integration - POST /api/notifications/sms
  return new Promise((resolve) => {
    setTimeout(() => {
      const history = getNotificationHistoryFromStorage();
      const newEntry: NotificationHistory = {
        id: generateId(),
        type: 'sms',
        to: request.to,
        content: request.content,
        status: 'sent', // In real implementation, this would be based on actual sending result
        sentAt: new Date().toISOString()
      };
      
      history.push(newEntry);
      saveNotificationHistoryToStorage(history);
      resolve(newEntry);
    }, 1000); // Simulate network delay
  });
}

// Template variable replacement
export function replaceTemplateVariables(content: string, variables: Record<string, string>): string {
  let result = content;
  
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{${key}}}`, 'g');
    result = result.replace(regex, value || '');
  }
  
  return result;
}

// Get notification history
export async function getNotificationHistory(): Promise<NotificationHistory[]> {
  // TODO: API Integration - GET /api/notification-history
  return getNotificationHistoryFromStorage();
} 