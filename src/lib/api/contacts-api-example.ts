// Example: How to update contacts API to use authenticated API calls
// This shows how to replace localStorage with real API calls using the new auth system

import { api, ApiError } from '$lib/utils/api';
import type { Contact } from './contacts';

// Get all contacts
export async function getContacts(): Promise<Contact[]> {
  try {
    const response = await api.get<{ contacts: Contact[] }>('/api/contacts');
    return response.contacts;
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('Failed to fetch contacts:', error.message);
      throw new Error('Failed to load contacts');
    }
    throw error;
  }
}

// Get contact by ID
export async function getContactById(id: string): Promise<Contact | null> {
  try {
    const response = await api.get<{ contact: Contact }>(`/api/contacts/${id}`);
    return response.contact;
  } catch (error) {
    if (error instanceof ApiError && error.status === 404) {
      return null;
    }
    throw error;
  }
}

// Create new contact
export async function createContact(contactData: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>): Promise<Contact> {
  try {
    const response = await api.post<{ contact: Contact }>('/api/contacts', {
      contact: contactData
    });
    return response.contact;
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('Failed to create contact:', error.message);
      throw new Error('Failed to create contact');
    }
    throw error;
  }
}

// Update contact
export async function updateContact(id: string, updates: Partial<Contact>): Promise<Contact> {
  try {
    const response = await api.put<{ contact: Contact }>(`/api/contacts/${id}`, {
      contact: updates
    });
    return response.contact;
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('Failed to update contact:', error.message);
      throw new Error('Failed to update contact');
    }
    throw error;
  }
}

// Delete contact
export async function deleteContact(id: string): Promise<void> {
  try {
    await api.delete(`/api/contacts/${id}`);
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('Failed to delete contact:', error.message);
      throw new Error('Failed to delete contact');
    }
    throw error;
  }
}

// Example of how to handle different response formats
export async function getContactsWithPagination(page: number = 1, limit: number = 10): Promise<{
  contacts: Contact[];
  total: number;
  page: number;
  totalPages: number;
}> {
  try {
    const response = await api.get<{
      contacts: Contact[];
      pagination: {
        total: number;
        page: number;
        totalPages: number;
      };
    }>(`/api/contacts?page=${page}&limit=${limit}`);
    
    return {
      contacts: response.contacts,
      total: response.pagination.total,
      page: response.pagination.page,
      totalPages: response.pagination.totalPages,
    };
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('Failed to fetch contacts:', error.message);
      throw new Error('Failed to load contacts');
    }
    throw error;
  }
}

// Example of how to handle file uploads (if needed)
export async function uploadContactAvatar(contactId: string, file: File): Promise<{ avatarUrl: string }> {
  try {
    const formData = new FormData();
    formData.append('avatar', file);
    
    // For file uploads, we need to use the authenticatedFetch directly
    // since we can't use JSON.stringify on FormData
    const response = await fetch(`https://app-ejp-api.azurewebsites.net/api/contacts/${contactId}/avatar`, {
      method: 'POST',
      body: formData,
      // Note: Don't set Content-Type for FormData, let the browser set it
    });
    
    if (!response.ok) {
      throw new ApiError(response.status, 'Failed to upload avatar');
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('Failed to upload avatar:', error.message);
      throw new Error('Failed to upload avatar');
    }
    throw error;
  }
} 