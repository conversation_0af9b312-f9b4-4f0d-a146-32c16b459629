// Client-side docs API using static JSON files

export interface DocFile {
  id: string;
  label: string;
  filename: string;
}

export interface DocsContent {
  [filename: string]: string;
}

// Cache for the docs data
let docsIndexCache: DocFile[] | null = null;
let docsContentCache: DocsContent | null = null;

/**
 * Get the list of available documentation files
 */
export async function getDocsList(): Promise<DocFile[]> {
  if (docsIndexCache) {
    return docsIndexCache;
  }

  try {
    const response = await fetch('/docs-index.json');
    if (!response.ok) {
      throw new Error(`Failed to fetch docs index: ${response.statusText}`);
    }
    
    docsIndexCache = await response.json();
    return docsIndexCache!;
  } catch (error) {
    console.error('Error loading docs index:', error);
    throw new Error('Failed to load documentation index');
  }
}

/**
 * Get the content of a specific documentation file
 */
export async function getDocContent(filename: string): Promise<string> {
  // Load content cache if not already loaded
  if (!docsContentCache) {
    try {
      const response = await fetch('/docs-content.json');
      if (!response.ok) {
        throw new Error(`Failed to fetch docs content: ${response.statusText}`);
      }
      
      docsContentCache = await response.json();
    } catch (error) {
      console.error('Error loading docs content:', error);
      throw new Error('Failed to load documentation content');
    }
  }

  if (!docsContentCache || !docsContentCache[filename]) {
    throw new Error(`Documentation file "${filename}" not found`);
  }

  return docsContentCache[filename];
}

/**
 * Search documentation content (client-side search)
 */
export async function searchDocs(query: string): Promise<Array<{ file: DocFile; snippet: string }>> {
  const [docsList, docsContent] = await Promise.all([
    getDocsList(),
    getAllDocsContent()
  ]);

  const results: Array<{ file: DocFile; snippet: string }> = [];
  const searchTerm = query.toLowerCase();

  for (const file of docsList) {
    const content = docsContent[file.filename];
    if (content && content.toLowerCase().includes(searchTerm)) {
      // Find the snippet around the match
      const contentLower = content.toLowerCase();
      const matchIndex = contentLower.indexOf(searchTerm);
      const start = Math.max(0, matchIndex - 50);
      const end = Math.min(content.length, matchIndex + searchTerm.length + 50);
      const snippet = content.substring(start, end);
      
      results.push({
        file,
        snippet: start > 0 ? '...' + snippet : snippet
      });
    }
  }

  return results;
}

/**
 * Get all docs content (for search functionality)
 */
async function getAllDocsContent(): Promise<DocsContent> {
  if (!docsContentCache) {
    const response = await fetch('/docs-content.json');
    if (!response.ok) {
      throw new Error(`Failed to fetch docs content: ${response.statusText}`);
    }
    docsContentCache = await response.json();
  }
  return docsContentCache!;
} 