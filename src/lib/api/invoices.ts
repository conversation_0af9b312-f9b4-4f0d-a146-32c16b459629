// Invoices API - Invoice Management Functionality
// Updated to work directly with backend API format

// API format interfaces - these are the primary interfaces now
export interface ApiInvoice {
  id?: string; // Optional for create, required for update
  invoiceNumber?: number; // 0 or omitted for new invoices (server assigns)
  status: number; // 0 = Draft, 1 = Sent, 2 = Paid, etc.
  issueDate: string;
  dueDate: string;
  notes?: string;
  paymentTerms?: string;
  invoiceLines: ApiInvoiceLine[];
}

export interface ApiInvoiceLine {
  id?: string; // Optional for create, required for update
  lineNumber: number;
  description: string;
  quantity: number;
  unitPrice: number;
  discountType: number; // 0 = None, 1 = Percentage, 2 = Fixed
  discountValue: number;
  discountAmount: number;
  subtotal: number;
  taxRate: number; // As decimal (0.2 = 20%)
  taxAmount: number;
  total: number;
}

// Frontend display interfaces (for UI components that need additional data)
export interface InvoiceDisplay extends ApiInvoice {
  // Additional fields for display purposes only
  customerName?: string;
  customerEmail?: string;
  customerAddress?: InvoiceAddress;
  customerId?: string; // For linking to customer data
  statusDisplay?: InvoiceStatus; // For display formatting
  createdAt?: string;
  updatedAt?: string;
  paidAt?: string;
  sentAt?: string;
}

export interface InvoiceLineDisplay extends ApiInvoiceLine {
  // Additional fields for display/editing
  productId?: string;
  additionalInfo?: string;
  relatedJobId?: string;
  relatedQuoteId?: string;
}

// Legacy interfaces for backward compatibility (will be phased out)
export interface Invoice {
  id: string;
  invoiceNumber: string; // Keep as string for legacy compatibility
  customerId: string;
  customerName?: string;
  customerEmail?: string;
  customerAddress?: InvoiceAddress;
  issueDate: string;
  dueDate: string;
  status: InvoiceStatus;
  lineItems: InvoiceLineItem[];
  customHeaderFields: CustomHeaderField[];
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  balanceAmount: number;
  notes?: string;
  terms?: string;
  templateId?: string;
  createdAt: string;
  updatedAt: string;
  paidAt?: string;
  sentAt?: string;
}

export interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  taxRate: number; // Keep as percentage for UI
  taxAmount: number;
  lineTotal: number; // Alias for total
  productId?: string;
  additionalInfo?: string;
  relatedJobId?: string;
  relatedQuoteId?: string;
}

export interface InvoiceAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface CustomHeaderField {
  id: string;
  label: string;
  value: string;
  type: 'text' | 'date' | 'number';
}

export interface InvoiceStatus {
  id: string;
  name: 'Draft' | 'Sent' | 'Paid' | 'Overdue' | 'Cancelled';
  color: string;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  taxRate: number;
  category?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceTemplate {
  id: string;
  name: string;
  logoUrl?: string;
  colorScheme?: {
    primary: string;
    secondary: string;
    accent: string;
  };
  sections?: TemplateSection[];
  components?: TemplateComponents;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateComponent {
  id: string;
  type: 'title' | 'logo' | 'text' | 'fields' | 'table';
  content: string;
  x: number;
  y: number;
  width: number;
  height: number;
  fontSize?: number;
  fontWeight?: string;
  color?: string;
  fontFamily?: string;
  logoUrl?: string;
  fields?: Array<{ label: string; value: string }>;
  headerColor?: string;
  borderColor?: string;
}

export interface TemplateComponents {
  [key: string]: TemplateComponent;
}

export interface TemplateSection {
  id: string;
  type: 'header' | 'footer' | 'terms' | 'notes' | 'custom';
  content: string;
  order: number;
  isVisible: boolean;
  name?: string;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold' | 'lighter';
  textAlign?: 'left' | 'center' | 'right';
}

const INVOICES_STORAGE_KEY = 'ejp_invoices';
const PRODUCTS_STORAGE_KEY = 'ejp_products';
const INVOICE_TEMPLATES_STORAGE_KEY = 'ejp_invoice_templates';

// Default invoice statuses
const DEFAULT_INVOICE_STATUSES: InvoiceStatus[] = [
  { id: '1', name: 'Draft', color: '#6B7280' },
  { id: '2', name: 'Sent', color: '#3B82F6' },
  { id: '3', name: 'Paid', color: '#10B981' },
  { id: '4', name: 'Overdue', color: '#EF4444' },
  { id: '5', name: 'Cancelled', color: '#6B7280' }
];

// Status mapping between frontend and API
const STATUS_MAPPING = {
  // Frontend status name to API status number
  'Draft': 0,
  'Sent': 1,
  'Paid': 2,
  'Overdue': 3,
  'Cancelled': 4
} as const;

const API_STATUS_MAPPING = {
  // API status number to frontend status
  0: { id: '1', name: 'Draft' as const, color: '#6B7280' },
  1: { id: '2', name: 'Sent' as const, color: '#3B82F6' },
  2: { id: '3', name: 'Paid' as const, color: '#10B981' },
  3: { id: '4', name: 'Overdue' as const, color: '#EF4444' },
  4: { id: '5', name: 'Cancelled' as const, color: '#6B7280' }
} as const;

// Helper functions for status conversion
export function getStatusDisplay(statusNumber: number): InvoiceStatus {
  return API_STATUS_MAPPING[statusNumber as keyof typeof API_STATUS_MAPPING] || DEFAULT_INVOICE_STATUSES[0];
}

export function getStatusNumber(statusName: string): number {
  return STATUS_MAPPING[statusName as keyof typeof STATUS_MAPPING] ?? 0;
}

// Helper functions for local storage (fallback only)
function getInvoicesFromStorage(): ApiInvoice[] {
  const stored = localStorage.getItem(INVOICES_STORAGE_KEY);
  if (stored) {
    return JSON.parse(stored);
  }

  // Initialize with sample data if none exists
  initializeSampleInvoices();
  const newStored = localStorage.getItem(INVOICES_STORAGE_KEY);
  return newStored ? JSON.parse(newStored) : [];
}

function saveInvoicesToStorage(invoices: ApiInvoice[]): void {
  localStorage.setItem(INVOICES_STORAGE_KEY, JSON.stringify(invoices));
}

function getProductsFromStorage(): Product[] {
  const stored = localStorage.getItem(PRODUCTS_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveProductsToStorage(products: Product[]): void {
  localStorage.setItem(PRODUCTS_STORAGE_KEY, JSON.stringify(products));
}

function getInvoiceTemplatesFromStorage(): InvoiceTemplate[] {
  const stored = localStorage.getItem(INVOICE_TEMPLATES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveInvoiceTemplatesToStorage(templates: InvoiceTemplate[]): void {
  localStorage.setItem(INVOICE_TEMPLATES_STORAGE_KEY, JSON.stringify(templates));
}

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Invoice CRUD operations - now working directly with API format
export async function getInvoices(): Promise<ApiInvoice[]> {
  try {
    // Import the api utility for proper API calls
    const { api } = await import('$lib/utils/api');
    const invoices = await api.get<ApiInvoice[]>('/Invoices');

    // Ensure invoiceLines is always an array
    return invoices.map(invoice => ({
      ...invoice,
      invoiceLines: invoice.invoiceLines || []
    }));
  } catch (error) {
    console.error('Error fetching invoices:', error);
    // Fallback to local storage for development
    return new Promise((resolve) => {
      setTimeout(() => {
        const invoices = getInvoicesFromStorage();
        // Ensure invoiceLines is always an array for fallback data too
        const safeInvoices = invoices.map(invoice => ({
          ...invoice,
          invoiceLines: invoice.invoiceLines || []
        }));
        resolve(safeInvoices);
      }, 100);
    });
  }
}

export async function getInvoiceById(id: string): Promise<ApiInvoice | null> {
  try {
    const { api } = await import('$lib/utils/api');
    const invoice = await api.get<ApiInvoice>(`/Invoices/${id}`);

    // Ensure invoiceLines is always an array
    return {
      ...invoice,
      invoiceLines: invoice.invoiceLines || []
    };
  } catch (error) {
    console.error('Error fetching invoice:', error);
    // Fallback to local storage for development
    return new Promise((resolve) => {
      setTimeout(() => {
        const invoices = getInvoicesFromStorage();
        const invoice = invoices.find(inv => inv.id === id) || null;
        if (invoice) {
          // Ensure invoiceLines is always an array for fallback data too
          invoice.invoiceLines = invoice.invoiceLines || [];
        }
        resolve(invoice);
      }, 100);
    });
  }
}

export async function getInvoicesByCustomer(customerId: string): Promise<ApiInvoice[]> {
  try {
    const { api } = await import('$lib/utils/api');
    const invoices = await api.get<ApiInvoice[]>(`/Invoices?customerId=${customerId}`);

    // Ensure invoiceLines is always an array for each invoice
    return invoices.map(invoice => ({
      ...invoice,
      invoiceLines: invoice.invoiceLines || []
    }));
  } catch (error) {
    console.error('Error fetching customer invoices:', error);
    // Fallback to local storage for development
    return new Promise((resolve) => {
      setTimeout(() => {
        const invoices = getInvoicesFromStorage();
        const customerInvoices = invoices.filter(inv => {
          // Note: ApiInvoice doesn't have customerId, so this is a placeholder
          // In a real implementation, you'd need to store customer info or look it up
          return false; // Return empty for now
        }).map(invoice => ({
          ...invoice,
          invoiceLines: invoice.invoiceLines || []
        }));
        resolve(customerInvoices);
      }, 100);
    });
  }
}

export async function saveInvoice(invoiceData: ApiInvoice): Promise<ApiInvoice> {
  try {
    const { api } = await import('$lib/utils/api');

    // Ensure invoiceLines is always an array before saving
    const safeInvoiceData = {
      ...invoiceData,
      invoiceLines: invoiceData.invoiceLines || []
    };

    const savedInvoice = await api.post<ApiInvoice>('/Invoices', safeInvoiceData);

    // Update local storage for development fallback
    const invoices = getInvoicesFromStorage();
    const existingIndex = invoices.findIndex(inv => inv.id === savedInvoice.id);
    if (existingIndex >= 0) {
      invoices[existingIndex] = savedInvoice;
    } else {
      invoices.push(savedInvoice);
    }
    saveInvoicesToStorage(invoices);

    return {
      ...savedInvoice,
      invoiceLines: savedInvoice.invoiceLines || []
    };
  } catch (error) {
    console.error('Error saving invoice:', error);
    // Fallback to local storage for development
    return new Promise((resolve) => {
      setTimeout(() => {
        const invoices = getInvoicesFromStorage();

        // Generate ID if creating new invoice
        if (!invoiceData.id) {
          invoiceData.id = generateId();
          invoiceData.invoiceNumber = invoices.length + 1;
        }

        // Ensure invoiceLines is always an array
        invoiceData.invoiceLines = invoiceData.invoiceLines || [];

        const existingIndex = invoices.findIndex(inv => inv.id === invoiceData.id);
        if (existingIndex >= 0) {
          invoices[existingIndex] = invoiceData;
        } else {
          invoices.push(invoiceData);
        }

        saveInvoicesToStorage(invoices);
        resolve(invoiceData);
      }, 100);
    });
  }
}

// Legacy functions for backward compatibility
export async function createInvoice(invoiceData: Omit<Invoice, 'id' | 'invoiceNumber' | 'createdAt' | 'updatedAt'>): Promise<Invoice> {
  // Convert legacy format to API format
  const apiInvoice: ApiInvoice = {
    status: getStatusNumber(invoiceData.status.name),
    issueDate: invoiceData.issueDate,
    dueDate: invoiceData.dueDate,
    notes: invoiceData.notes,
    paymentTerms: invoiceData.terms,
    invoiceLines: invoiceData.lineItems.map((item, index) => ({
      lineNumber: index + 1,
      description: item.description,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      discountType: 0,
      discountValue: 0,
      discountAmount: 0,
      subtotal: item.quantity * item.unitPrice,
      taxRate: item.taxRate / 100, // Convert percentage to decimal
      taxAmount: item.taxAmount,
      total: item.lineTotal + item.taxAmount
    }))
  };

  const savedInvoice = await saveInvoice(apiInvoice);
  
  // Convert back to legacy format for compatibility
  return {
    ...invoiceData,
    id: savedInvoice.id!,
    invoiceNumber: savedInvoice.invoiceNumber?.toString() || '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
}

export async function updateInvoice(id: string, updates: Partial<Invoice>): Promise<Invoice> {
  const existingInvoice = await getInvoiceById(id);
  if (!existingInvoice) {
    throw new Error('Invoice not found');
  }

  // Convert updates to API format and merge
  const updatedApiInvoice: ApiInvoice = {
    ...existingInvoice,
    id,
    // Apply updates if provided
    ...(updates.status && { status: getStatusNumber(updates.status.name) }),
    ...(updates.issueDate && { issueDate: updates.issueDate }),
    ...(updates.dueDate && { dueDate: updates.dueDate }),
    ...(updates.notes && { notes: updates.notes }),
    ...(updates.terms && { paymentTerms: updates.terms }),
    ...(updates.lineItems && {
      invoiceLines: updates.lineItems.map((item, index) => ({
        id: item.id,
        lineNumber: index + 1,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        discountType: 0,
        discountValue: 0,
        discountAmount: 0,
        subtotal: item.quantity * item.unitPrice,
        taxRate: item.taxRate / 100,
        taxAmount: item.taxAmount,
        total: item.lineTotal + item.taxAmount
      }))
    })
  };

  const savedInvoice = await saveInvoice(updatedApiInvoice);
  
  // Convert back to legacy format
  return {
    id: savedInvoice.id!,
    invoiceNumber: savedInvoice.invoiceNumber?.toString() || '',
    customerId: updates.customerId || '',
    customerName: updates.customerName,
    customerEmail: updates.customerEmail,
    customerAddress: updates.customerAddress,
    issueDate: savedInvoice.issueDate,
    dueDate: savedInvoice.dueDate,
    status: getStatusDisplay(savedInvoice.status),
    lineItems: savedInvoice.invoiceLines.map(line => ({
      id: line.id || generateId(),
      description: line.description,
      quantity: line.quantity,
      unitPrice: line.unitPrice,
      taxRate: line.taxRate * 100, // Convert back to percentage
      taxAmount: line.taxAmount,
      lineTotal: line.subtotal,
      productId: updates.lineItems?.find(item => item.description === line.description)?.productId,
      additionalInfo: updates.lineItems?.find(item => item.description === line.description)?.additionalInfo,
      relatedJobId: updates.lineItems?.find(item => item.description === line.description)?.relatedJobId,
      relatedQuoteId: updates.lineItems?.find(item => item.description === line.description)?.relatedQuoteId
    })),
    customHeaderFields: updates.customHeaderFields || [],
    subtotal: savedInvoice.invoiceLines.reduce((sum, line) => sum + line.subtotal, 0),
    taxAmount: savedInvoice.invoiceLines.reduce((sum, line) => sum + line.taxAmount, 0),
    discountAmount: updates.discountAmount || 0,
    totalAmount: savedInvoice.invoiceLines.reduce((sum, line) => sum + line.total, 0),
    paidAmount: updates.paidAmount || 0,
    balanceAmount: savedInvoice.invoiceLines.reduce((sum, line) => sum + line.total, 0) - (updates.paidAmount || 0),
    notes: savedInvoice.notes,
    terms: savedInvoice.paymentTerms,
    templateId: updates.templateId,
    createdAt: updates.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    paidAt: updates.paidAt,
    sentAt: updates.sentAt
  };
}

export async function deleteInvoice(id: string): Promise<void> {
  try {
    const { api } = await import('$lib/utils/api');
    await api.delete(`/Invoices/${id}`);

    // Update local storage for development fallback
    const invoices = getInvoicesFromStorage();
    const index = invoices.findIndex(inv => inv.id === id);
    if (index >= 0) {
      invoices.splice(index, 1);
      saveInvoicesToStorage(invoices);
    }
  } catch (error) {
    console.error('Error deleting invoice:', error);
    // Fallback to local storage for development
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const invoices = getInvoicesFromStorage();
        const index = invoices.findIndex(inv => inv.id === id);

        if (index === -1) {
          reject(new Error('Invoice not found'));
          return;
        }

        invoices.splice(index, 1);
        saveInvoicesToStorage(invoices);
        resolve();
      }, 100);
    });
  }
}

// Helper function to create empty API line item
export function createEmptyApiLineItem(lineNumber: number = 1): ApiInvoiceLine {
  return {
    lineNumber,
    description: '',
    quantity: 1,
    unitPrice: 0,
    discountType: 0,
    discountValue: 0,
    discountAmount: 0,
    subtotal: 0,
    taxRate: 0.1, // 10% as decimal
    taxAmount: 0,
    total: 0
  };
}

// Calculate totals for API invoice
export function calculateApiInvoiceTotals(invoiceLines: ApiInvoiceLine[]): {
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
} {
  const subtotal = invoiceLines.reduce((sum, line) => sum + line.subtotal, 0);
  const taxAmount = invoiceLines.reduce((sum, line) => sum + line.taxAmount, 0);
  const totalAmount = invoiceLines.reduce((sum, line) => sum + line.total, 0);
  
  return {
    subtotal,
    taxAmount,
    totalAmount
  };
}

// Update line item calculations
export function calculateLineItemTotals(line: ApiInvoiceLine): ApiInvoiceLine {
  const subtotal = line.quantity * line.unitPrice;
  const discountAmount = line.discountType === 1 
    ? subtotal * (line.discountValue / 100) 
    : line.discountValue;
  const subtotalAfterDiscount = subtotal - discountAmount;
  const taxAmount = subtotalAfterDiscount * line.taxRate;
  const total = subtotalAfterDiscount + taxAmount;

  return {
    ...line,
    subtotal,
    discountAmount,
    taxAmount,
    total
  };
}

// Initialize sample invoice data if none exists
function initializeSampleInvoices() {
  const existingInvoices = localStorage.getItem(INVOICES_STORAGE_KEY);
  if (!existingInvoices) {
    const sampleInvoices: ApiInvoice[] = [
      {
        id: generateId(),
        invoiceNumber: 1001,
        status: 1, // Sent
        issueDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days ago
        dueDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 23 days from now
        notes: 'Thank you for your business!',
        paymentTerms: 'Payment due within 30 days',
        invoiceLines: [
          {
            lineNumber: 1,
            description: 'Plumbing Service - Basic',
            quantity: 2,
            unitPrice: 150.00,
            discountType: 0,
            discountValue: 0,
            discountAmount: 0,
            subtotal: 300.00,
            taxRate: 0.1,
            taxAmount: 30.00,
            total: 330.00
          },
          {
            lineNumber: 2,
            description: 'Emergency Call-out',
            quantity: 1,
            unitPrice: 100.00,
            discountType: 0,
            discountValue: 0,
            discountAmount: 0,
            subtotal: 100.00,
            taxRate: 0.1,
            taxAmount: 10.00,
            total: 110.00
          }
        ]
      },
      {
        id: generateId(),
        invoiceNumber: 1002,
        status: 2, // Paid
        issueDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 14 days ago
        dueDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 day ago
        notes: 'Electrical work completed successfully.',
        paymentTerms: 'Payment due within 30 days',
        invoiceLines: [
          {
            lineNumber: 1,
            description: 'Electrical Installation',
            quantity: 1,
            unitPrice: 200.00,
            discountType: 0,
            discountValue: 0,
            discountAmount: 0,
            subtotal: 200.00,
            taxRate: 0.1,
            taxAmount: 20.00,
            total: 220.00
          }
        ]
      },
      {
        id: generateId(),
        invoiceNumber: 1003,
        status: 3, // Overdue
        issueDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 45 days ago
        dueDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 15 days ago
        notes: 'HVAC maintenance service.',
        paymentTerms: 'Payment due within 30 days',
        invoiceLines: [
          {
            lineNumber: 1,
            description: 'HVAC Maintenance',
            quantity: 1,
            unitPrice: 180.00,
            discountType: 0,
            discountValue: 0,
            discountAmount: 0,
            subtotal: 180.00,
            taxRate: 0.1,
            taxAmount: 18.00,
            total: 198.00
          }
        ]
      },
      {
        id: generateId(),
        status: 0, // Draft (no invoice number yet)
        issueDate: new Date().toISOString().split('T')[0], // Today
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
        notes: 'Draft invoice for general labor.',
        paymentTerms: 'Payment due within 30 days',
        invoiceLines: [
          {
            lineNumber: 1,
            description: 'General Labor',
            quantity: 4,
            unitPrice: 75.00,
            discountType: 0,
            discountValue: 0,
            discountAmount: 0,
            subtotal: 300.00,
            taxRate: 0.1,
            taxAmount: 30.00,
            total: 330.00
          }
        ]
      }
    ];

    localStorage.setItem(INVOICES_STORAGE_KEY, JSON.stringify(sampleInvoices));
  }
}

// Initialize sample data if none exists
async function initializeSampleProducts() {
  const existingProducts = getProductsFromStorage();
  if (existingProducts.length === 0) {
    const sampleProducts: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'Plumbing Service - Basic',
        description: 'Basic plumbing service including inspection and minor repairs',
        price: 150.00,
        taxRate: 10,
        category: 'Plumbing',
        isActive: true
      },
      {
        name: 'Electrical Installation',
        description: 'Standard electrical installation and wiring',
        price: 200.00,
        taxRate: 10,
        category: 'Electrical',
        isActive: true
      },
      {
        name: 'HVAC Maintenance',
        description: 'Heating, ventilation, and air conditioning maintenance service',
        price: 180.00,
        taxRate: 10,
        category: 'HVAC',
        isActive: true
      },
      {
        name: 'General Labor',
        description: 'General labor and maintenance work',
        price: 75.00,
        taxRate: 10,
        category: 'General',
        isActive: true
      },
      {
        name: 'Emergency Call-out',
        description: 'Emergency service call-out fee',
        price: 100.00,
        taxRate: 10,
        category: 'Emergency',
        isActive: true
      }
    ];

    const products = getProductsFromStorage();
    for (const productData of sampleProducts) {
      const newProduct: Product = {
        ...productData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      products.push(newProduct);
    }
    saveProductsToStorage(products);
  }
}

async function initializeSampleTemplates() {
  const existingTemplates = getInvoiceTemplatesFromStorage();
  if (existingTemplates.length === 0) {
    const sampleTemplates: Omit<InvoiceTemplate, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'Default Template',
        colorScheme: {
          primary: '#3B82F6',
          secondary: '#6B7280',
          accent: '#10B981'
        },
        sections: [
          {
            id: generateId(),
            type: 'header',
            content: 'Easy Job Planner Invoice',
            order: 1,
            isVisible: true
          },
          {
            id: generateId(),
            type: 'terms',
            content: 'Payment due within 30 days. Please make payment via bank transfer.',
            order: 2,
            isVisible: true
          },
          {
            id: generateId(),
            type: 'footer',
            content: 'Thank you for your business!',
            order: 3,
            isVisible: true
          }
        ],
        isDefault: true
      },
      {
        name: 'Professional Template',
        colorScheme: {
          primary: '#1F2937',
          secondary: '#374151',
          accent: '#059669'
        },
        sections: [
          {
            id: generateId(),
            type: 'header',
            content: 'Professional Services Invoice',
            order: 1,
            isVisible: true
          },
          {
            id: generateId(),
            type: 'terms',
            content: 'Payment terms: Net 30 days. Late payments subject to 1.5% monthly service charge.',
            order: 2,
            isVisible: true
          }
        ],
        isDefault: false
      }
    ];

    const templates = getInvoiceTemplatesFromStorage();
    for (const templateData of sampleTemplates) {
      const newTemplate: InvoiceTemplate = {
        ...templateData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      templates.push(newTemplate);
    }
    saveInvoiceTemplatesToStorage(templates);
  }
}

// Product management
export async function getProducts(): Promise<Product[]> {
  // TODO: API Integration - GET /api/products
  // Expected response: { success: boolean, data: Product[] }
  
  return new Promise(async (resolve) => {
    setTimeout(async () => {
      await initializeSampleProducts();
      resolve(getProductsFromStorage());
    }, 100);
  });
}

export async function createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {
  // TODO: API Integration - POST /api/products
  // Expected request: { product: ProductData }
  // Expected response: { success: boolean, data: Product }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const products = getProductsFromStorage();
      const newProduct: Product = {
        ...productData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      products.push(newProduct);
      saveProductsToStorage(products);
      resolve(newProduct);
    }, 100);
  });
}

export async function updateProduct(id: string, updates: Partial<Product>): Promise<Product> {
  // TODO: API Integration - PUT /api/products/{id}
  // Expected request: { product: Partial<Product> }
  // Expected response: { success: boolean, data: Product }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const products = getProductsFromStorage();
      const index = products.findIndex(p => p.id === id);
      
      if (index === -1) {
        reject(new Error('Product not found'));
        return;
      }
      
      products[index] = {
        ...products[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      saveProductsToStorage(products);
      resolve(products[index]);
    }, 100);
  });
}

// Invoice template management
export async function getInvoiceTemplates(): Promise<InvoiceTemplate[]> {
  // TODO: API Integration - GET /api/invoice-templates
  // Expected response: { success: boolean, data: InvoiceTemplate[] }
  
  return new Promise(async (resolve) => {
    setTimeout(async () => {
      await initializeSampleTemplates();
      resolve(getInvoiceTemplatesFromStorage());
    }, 100);
  });
}

export async function createInvoiceTemplate(templateData: Omit<InvoiceTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<InvoiceTemplate> {
  // TODO: API Integration - POST /api/invoice-templates
  // Expected request: { template: InvoiceTemplateData }
  // Expected response: { success: boolean, data: InvoiceTemplate }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const templates = getInvoiceTemplatesFromStorage();
      const newTemplate: InvoiceTemplate = {
        ...templateData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      templates.push(newTemplate);
      saveInvoiceTemplatesToStorage(templates);
      resolve(newTemplate);
    }, 100);
  });
}

export async function updateInvoiceTemplate(id: string, updates: Partial<InvoiceTemplate>): Promise<InvoiceTemplate> {
  // TODO: API Integration - PUT /api/invoice-templates/{id}
  // Expected request: { template: Partial<InvoiceTemplate> }
  // Expected response: { success: boolean, data: InvoiceTemplate }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const templates = getInvoiceTemplatesFromStorage();
      const index = templates.findIndex(t => t.id === id);
      
      if (index === -1) {
        reject(new Error('Template not found'));
        return;
      }
      
      templates[index] = {
        ...templates[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      saveInvoiceTemplatesToStorage(templates);
      resolve(templates[index]);
    }, 100);
  });
}

// Calculate invoice totals
export function calculateInvoiceTotals(lineItems: InvoiceLineItem[], discountAmount: number = 0): {
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  balanceAmount: number;
} {
  const subtotal = lineItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const taxAmount = lineItems.reduce((sum, item) => sum + item.taxAmount, 0);
  const totalAmount = subtotal + taxAmount - discountAmount;
  
  return {
    subtotal,
    taxAmount,
    totalAmount,
    balanceAmount: totalAmount // Will be updated when payments are applied
  };
}

export async function deleteInvoiceTemplate(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/invoice-templates/{id}
  // Expected response: { success: boolean }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const templates = getInvoiceTemplatesFromStorage();
      const index = templates.findIndex(t => t.id === id);
      
      if (index === -1) {
        reject(new Error('Template not found'));
        return;
      }
      
      templates.splice(index, 1);
      saveInvoiceTemplatesToStorage(templates);
      resolve();
    }, 100);
  });
}

// Get invoice statuses
export async function getInvoiceStatuses(): Promise<InvoiceStatus[]> {
  // TODO: API Integration - GET /api/invoice-statuses
  // Expected response: { success: boolean, data: InvoiceStatus[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(DEFAULT_INVOICE_STATUSES);
    }, 100);
  });
} 