// Jobs API - Job Management Functionality
// TODO: API Integration - Replace local storage with actual API endpoints
// Expected request format: { job: JobData }
// Expected response format: { success: boolean, data: Job, message?: string }

export interface Job {
  id: string;
  title: string;
  customerId: string;
  customerName?: string; // Denormalized for display
  jobTypeId: string;
  jobType?: JobType; // Populated for display
  status: JobStatus;
  description: string;
  scheduledDateTime?: string;
  assignedStaff: AssignedStaff[];
  jobAddress: JobAddress;
  customFields: CustomField[];
  estimatedCost: JobCostEstimate;
  actualCost?: JobActualCost;
  resources: JobResource[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  estimatedDuration?: number; // in minutes
  actualDuration?: number; // in minutes
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  tags: string[];
  attachments: JobAttachment[];
  notes: string;
}

export interface JobCostEstimate {
  laborCost: number;
  materialCost: number;
  resourceCost: number;
  totalCost: number;
  breakdown: CostBreakdownItem[];
}

export interface JobActualCost {
  laborCost: number;
  materialCost: number;
  resourceCost: number;
  totalCost: number;
  breakdown: CostBreakdownItem[];
}

export interface CostBreakdownItem {
  id: string;
  type: 'labor' | 'material' | 'resource' | 'other';
  description: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  staffId?: string;
  resourceId?: string;
}

export interface JobResource {
  id: string;
  resourceId: string;
  resourceName: string;
  resourceType: 'Equipment' | 'Vehicle' | 'Tool' | 'Material';
  quantity: number;
  costPerUnit: number;
  totalCost: number;
  startTime?: string;
  endTime?: string;
}

export interface JobStatus {
  id: string;
  name: string;
  color: string;
  order: number;
  isCompleted: boolean;
}

export interface AssignedStaff {
  staffId: string;
  staffName: string;
  hourlyRate: number;
  startTime?: string;
  endTime?: string;
  estimatedHours: number;
  actualHours?: number;
  role?: string;
}

export interface JobAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface CustomField {
  id: string;
  key: string;
  value: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'select';
  options?: string[]; // For select type
}

export interface JobAttachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: string;
}

export interface JobType {
  id: string;
  name: string;
  description?: string;
  pricingModel: 'hourly' | 'fixed' | 'per_unit';
  defaultDuration?: number; // in minutes
  defaultFixedPrice?: number; // for fixed pricing
  defaultHourlyRate?: number; // for hourly pricing
  defaultUnitPrice?: number; // for per-unit pricing
  defaultFields: CustomField[];
  requiredSkills: string[];
  defaultResources: DefaultResource[];
  isActive: boolean;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DefaultResource {
  resourceId: string;
  resourceName: string;
  resourceType: 'Equipment' | 'Vehicle' | 'Tool' | 'Material';
  quantity: number;
  isRequired: boolean;
}

// Resource management interfaces
export interface Resource {
  id: string;
  name: string;
  type: 'Equipment' | 'Vehicle' | 'Tool' | 'Material';
  description?: string;
  costPerHour?: number;
  costPerUnit?: number;
  isAvailable: boolean;
  location?: string;
  maintenanceSchedule?: MaintenanceEntry[];
  createdAt: string;
  updatedAt: string;
}

export interface MaintenanceEntry {
  id: string;
  date: string;
  type: 'Routine' | 'Repair' | 'Inspection';
  description: string;
  cost: number;
  performedBy: string;
}

const JOBS_STORAGE_KEY = 'ejp_jobs';
const JOB_STATUSES_STORAGE_KEY = 'ejp_job_statuses';
const JOB_TYPES_STORAGE_KEY = 'ejp_job_types';
const RESOURCES_STORAGE_KEY = 'ejp_resources';

// Default job statuses (Kanban pipeline)
const DEFAULT_JOB_STATUSES: JobStatus[] = [
  { id: '1', name: 'Backlog', color: '#6B7280', order: 1, isCompleted: false },
  { id: '2', name: 'Scheduled', color: '#3B82F6', order: 2, isCompleted: false },
  { id: '3', name: 'In Progress', color: '#F59E0B', order: 3, isCompleted: false },
  { id: '4', name: 'Completed', color: '#10B981', order: 4, isCompleted: true },
  { id: '5', name: 'Cancelled', color: '#EF4444', order: 5, isCompleted: true }
];

// Sample job types for different cleaning services
const DEFAULT_JOB_TYPES: JobType[] = [
  {
    id: '1',
    name: 'Standard House Cleaning',
    description: 'Regular house cleaning service including all rooms',
    pricingModel: 'hourly',
    defaultDuration: 180, // 3 hours
    defaultHourlyRate: 35,
    defaultFields: [
      { id: '1', key: 'rooms', value: '', type: 'number', options: [] },
      { id: '2', key: 'bathrooms', value: '', type: 'number', options: [] },
      { id: '3', key: 'pets', value: '', type: 'boolean', options: [] }
    ],
    requiredSkills: ['General Cleaning'],
    defaultResources: [
      { resourceId: '1', resourceName: 'Vacuum Cleaner', resourceType: 'Equipment', quantity: 1, isRequired: true },
      { resourceId: '2', resourceName: 'Cleaning Supplies', resourceType: 'Material', quantity: 1, isRequired: true }
    ],
    isActive: true,
    category: 'Residential',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Deep Cleaning',
    description: 'Comprehensive deep cleaning service',
    pricingModel: 'fixed',
    defaultDuration: 360, // 6 hours
    defaultFixedPrice: 250,
    defaultFields: [
      { id: '1', key: 'rooms', value: '', type: 'number', options: [] },
      { id: '2', key: 'appliances', value: '', type: 'boolean', options: [] },
      { id: '3', key: 'windows', value: '', type: 'boolean', options: [] }
    ],
    requiredSkills: ['Deep Cleaning', 'Appliance Cleaning'],
    defaultResources: [
      { resourceId: '1', resourceName: 'Vacuum Cleaner', resourceType: 'Equipment', quantity: 1, isRequired: true },
      { resourceId: '2', resourceName: 'Steam Cleaner', resourceType: 'Equipment', quantity: 1, isRequired: true },
      { resourceId: '3', resourceName: 'Specialized Cleaning Supplies', resourceType: 'Material', quantity: 1, isRequired: true }
    ],
    isActive: true,
    category: 'Residential',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    name: 'Office Cleaning',
    description: 'Commercial office cleaning service',
    pricingModel: 'per_unit',
    defaultDuration: 120, // 2 hours
    defaultUnitPrice: 2.50, // per square foot
    defaultFields: [
      { id: '1', key: 'square_feet', value: '', type: 'number', options: [] },
      { id: '2', key: 'floors', value: '', type: 'number', options: [] },
      { id: '3', key: 'after_hours', value: '', type: 'boolean', options: [] }
    ],
    requiredSkills: ['Commercial Cleaning'],
    defaultResources: [
      { resourceId: '1', resourceName: 'Commercial Vacuum', resourceType: 'Equipment', quantity: 1, isRequired: true },
      { resourceId: '4', resourceName: 'Office Cleaning Supplies', resourceType: 'Material', quantity: 1, isRequired: true }
    ],
    isActive: true,
    category: 'Commercial',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Sample resources
const DEFAULT_RESOURCES: Resource[] = [
  {
    id: '1',
    name: 'Vacuum Cleaner',
    type: 'Equipment',
    description: 'Professional grade vacuum cleaner',
    costPerHour: 5,
    isAvailable: true,
    location: 'Main Office',
    maintenanceSchedule: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Cleaning Supplies',
    type: 'Material',
    description: 'Standard cleaning supplies kit',
    costPerUnit: 15,
    isAvailable: true,
    location: 'Storage Room',
    maintenanceSchedule: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    name: 'Steam Cleaner',
    type: 'Equipment',
    description: 'Professional steam cleaning equipment',
    costPerHour: 10,
    isAvailable: true,
    location: 'Equipment Room',
    maintenanceSchedule: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '4',
    name: 'Office Cleaning Supplies',
    type: 'Material',
    description: 'Commercial office cleaning supplies',
    costPerUnit: 20,
    isAvailable: true,
    location: 'Storage Room',
    maintenanceSchedule: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Helper functions
function getJobsFromStorage(): Job[] {
  const stored = localStorage.getItem(JOBS_STORAGE_KEY);
  const jobs = stored ? JSON.parse(stored) : [];
  
  // Normalize jobs to ensure all required array properties exist
  return jobs.map((job: any) => ({
    ...job,
    customFields: job.customFields || [],
    assignedStaff: job.assignedStaff || [],
    resources: job.resources || [],
    tags: job.tags || [],
    attachments: job.attachments || [],
    estimatedCost: job.estimatedCost || { laborCost: 0, materialCost: 0, resourceCost: 0, totalCost: 0, breakdown: [] }
  }));
}

function saveJobsToStorage(jobs: Job[]): void {
  localStorage.setItem(JOBS_STORAGE_KEY, JSON.stringify(jobs));
}

function getJobStatusesFromStorage(): JobStatus[] {
  const stored = localStorage.getItem(JOB_STATUSES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : DEFAULT_JOB_STATUSES;
}

function saveJobStatusesToStorage(statuses: JobStatus[]): void {
  localStorage.setItem(JOB_STATUSES_STORAGE_KEY, JSON.stringify(statuses));
}

function getJobTypesFromStorage(): JobType[] {
  const stored = localStorage.getItem(JOB_TYPES_STORAGE_KEY);
  if (stored) {
    return JSON.parse(stored);
  } else {
    // Initialize with default job types
    saveJobTypesToStorage(DEFAULT_JOB_TYPES);
    return DEFAULT_JOB_TYPES;
  }
}

function saveJobTypesToStorage(types: JobType[]): void {
  localStorage.setItem(JOB_TYPES_STORAGE_KEY, JSON.stringify(types));
}

function getResourcesFromStorage(): Resource[] {
  const stored = localStorage.getItem(RESOURCES_STORAGE_KEY);
  if (stored) {
    return JSON.parse(stored);
  } else {
    // Initialize with default resources
    saveResourcesToStorage(DEFAULT_RESOURCES);
    return DEFAULT_RESOURCES;
  }
}

function saveResourcesToStorage(resources: Resource[]): void {
  localStorage.setItem(RESOURCES_STORAGE_KEY, JSON.stringify(resources));
}

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Cost calculation functions
export function calculateJobCostEstimate(
  jobType: JobType,
  assignedStaff: AssignedStaff[],
  resources: JobResource[],
  customFields: CustomField[] = []
): JobCostEstimate {
  let laborCost = 0;
  let materialCost = 0;
  let resourceCost = 0;
  const breakdown: CostBreakdownItem[] = [];

  // Calculate labor cost based on pricing model
  if (jobType.pricingModel === 'hourly') {
    assignedStaff.forEach(staff => {
      const cost = staff.hourlyRate * staff.estimatedHours;
      laborCost += cost;
      breakdown.push({
        id: generateId(),
        type: 'labor',
        description: `${staff.staffName} - ${staff.estimatedHours}h @ $${staff.hourlyRate}/h`,
        quantity: staff.estimatedHours,
        unitCost: staff.hourlyRate,
        totalCost: cost,
        staffId: staff.staffId
      });
    });
  } else if (jobType.pricingModel === 'fixed') {
    laborCost = jobType.defaultFixedPrice || 0;
    breakdown.push({
      id: generateId(),
      type: 'labor',
      description: `Fixed price for ${jobType.name}`,
      quantity: 1,
      unitCost: laborCost,
      totalCost: laborCost
    });
  } else if (jobType.pricingModel === 'per_unit') {
    const unitField = customFields.find(f => f.key === 'square_feet' || f.key === 'units');
    const units = unitField ? parseFloat(unitField.value) || 1 : 1;
    const unitPrice = jobType.defaultUnitPrice || 0;
    laborCost = units * unitPrice;
    breakdown.push({
      id: generateId(),
      type: 'labor',
      description: `${units} units @ $${unitPrice}/unit`,
      quantity: units,
      unitCost: unitPrice,
      totalCost: laborCost
    });
  }

  // Calculate resource costs
  resources.forEach(resource => {
    const cost = resource.quantity * resource.costPerUnit;
    if (resource.resourceType === 'Material') {
      materialCost += cost;
    } else {
      resourceCost += cost;
    }
    breakdown.push({
      id: generateId(),
      type: resource.resourceType === 'Material' ? 'material' : 'resource',
      description: `${resource.resourceName} - ${resource.quantity} @ $${resource.costPerUnit}`,
      quantity: resource.quantity,
      unitCost: resource.costPerUnit,
      totalCost: cost,
      resourceId: resource.resourceId
    });
  });

  const totalCost = laborCost + materialCost + resourceCost;

  return {
    laborCost,
    materialCost,
    resourceCost,
    totalCost,
    breakdown
  };
}

// Job CRUD operations
export async function getJobs(): Promise<Job[]> {
  // TODO: API Integration - GET /api/jobs
  // Expected response: { success: boolean, data: Job[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const jobTypes = getJobTypesFromStorage();
      
      // Populate job type information
      jobs.forEach(job => {
        job.jobType = jobTypes.find(jt => jt.id === job.jobTypeId);
      });
      
      resolve(jobs);
    }, 100);
  });
}

export async function getJobById(id: string): Promise<Job | null> {
  // TODO: API Integration - GET /api/jobs/{id}
  // Expected response: { success: boolean, data: Job }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const jobTypes = getJobTypesFromStorage();
      const job = jobs.find(j => j.id === id) || null;
      
      if (job) {
        job.jobType = jobTypes.find(jt => jt.id === job.jobTypeId);
      }
      
      resolve(job);
    }, 100);
  });
}

export async function getJobsByCustomer(customerId: string): Promise<Job[]> {
  // TODO: API Integration - GET /api/jobs?customerId={customerId}
  // Expected response: { success: boolean, data: Job[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const jobTypes = getJobTypesFromStorage();
      const customerJobs = jobs.filter(j => j.customerId === customerId);
      
      // Populate job type information
      customerJobs.forEach(job => {
        job.jobType = jobTypes.find(jt => jt.id === job.jobTypeId);
      });
      
      resolve(customerJobs);
    }, 100);
  });
}

export async function createJob(jobData: Omit<Job, 'id' | 'createdAt' | 'updatedAt'>): Promise<Job> {
  // TODO: API Integration - POST /api/jobs
  // Expected request: { job: JobData }
  // Expected response: { success: boolean, data: Job }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const newJob: Job = {
        ...jobData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      jobs.push(newJob);
      saveJobsToStorage(jobs);
      resolve(newJob);
    }, 100);
  });
}

export async function updateJob(id: string, updates: Partial<Job>): Promise<Job> {
  // TODO: API Integration - PUT /api/jobs/{id}
  // Expected request: { job: Partial<Job> }
  // Expected response: { success: boolean, data: Job }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const index = jobs.findIndex(j => j.id === id);
      
      if (index === -1) {
        reject(new Error('Job not found'));
        return;
      }
      
      jobs[index] = {
        ...jobs[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      saveJobsToStorage(jobs);
      resolve(jobs[index]);
    }, 100);
  });
}

export async function deleteJob(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/jobs/{id}
  // Expected response: { success: boolean }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const index = jobs.findIndex(j => j.id === id);
      
      if (index === -1) {
        reject(new Error('Job not found'));
        return;
      }
      
      jobs.splice(index, 1);
      saveJobsToStorage(jobs);
      resolve();
    }, 100);
  });
}

// Job Status management
export async function getJobStatuses(): Promise<JobStatus[]> {
  // TODO: API Integration - GET /api/job-statuses
  // Expected response: { success: boolean, data: JobStatus[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getJobStatusesFromStorage());
    }, 100);
  });
}

export async function updateJobStatus(id: string, statusId: string): Promise<Job> {
  // TODO: API Integration - PUT /api/jobs/{id}/status
  // Expected request: { statusId: string }
  // Expected response: { success: boolean, data: Job }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const statuses = getJobStatusesFromStorage();
      const jobIndex = jobs.findIndex(j => j.id === id);
      const status = statuses.find(s => s.id === statusId);
      
      if (jobIndex === -1) {
        reject(new Error('Job not found'));
        return;
      }
      
      if (!status) {
        reject(new Error('Status not found'));
        return;
      }
      
      jobs[jobIndex].status = status;
      jobs[jobIndex].updatedAt = new Date().toISOString();
      
      if (status.isCompleted && !jobs[jobIndex].completedAt) {
        jobs[jobIndex].completedAt = new Date().toISOString();
      } else if (!status.isCompleted) {
        jobs[jobIndex].completedAt = undefined;
      }
      
      saveJobsToStorage(jobs);
      resolve(jobs[jobIndex]);
    }, 100);
  });
}

export async function createJobStatus(statusData: Omit<JobStatus, 'id'>): Promise<JobStatus> {
  // TODO: API Integration - POST /api/job-statuses
  // Expected request: { status: JobStatusData }
  // Expected response: { success: boolean, data: JobStatus }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const statuses = getJobStatusesFromStorage();
      const newStatus: JobStatus = {
        ...statusData,
        id: generateId()
      };
      
      statuses.push(newStatus);
      statuses.sort((a, b) => a.order - b.order);
      saveJobStatusesToStorage(statuses);
      resolve(newStatus);
    }, 100);
  });
}

export async function updateJobStatusOrder(statusUpdates: { id: string; order: number }[]): Promise<JobStatus[]> {
  // TODO: API Integration - PUT /api/job-statuses/reorder
  // Expected request: { statusUpdates: { id: string; order: number }[] }
  // Expected response: { success: boolean, data: JobStatus[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const statuses = getJobStatusesFromStorage();
      
      statusUpdates.forEach(update => {
        const status = statuses.find(s => s.id === update.id);
        if (status) {
          status.order = update.order;
        }
      });
      
      statuses.sort((a, b) => a.order - b.order);
      saveJobStatusesToStorage(statuses);
      resolve(statuses);
    }, 100);
  });
}

// Job Types management
export async function getJobTypes(): Promise<JobType[]> {
  // TODO: API Integration - GET /api/job-types
  // Expected response: { success: boolean, data: JobType[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getJobTypesFromStorage());
    }, 100);
  });
}

export async function createJobType(typeData: Omit<JobType, 'id'>): Promise<JobType> {
  // TODO: API Integration - POST /api/job-types
  // Expected request: { jobType: JobTypeData }
  // Expected response: { success: boolean, data: JobType }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const types = getJobTypesFromStorage();
      const newType: JobType = {
        ...typeData,
        id: generateId()
      };
      
      types.push(newType);
      saveJobTypesToStorage(types);
      resolve(newType);
    }, 100);
  });
}

export async function updateJobType(id: string, updates: Partial<JobType>): Promise<JobType> {
  // TODO: API Integration - PUT /api/job-types/{id}
  // Expected request: { jobType: Partial<JobType> }
  // Expected response: { success: boolean, data: JobType }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const types = getJobTypesFromStorage();
      const index = types.findIndex(t => t.id === id);
      
      if (index === -1) {
        reject(new Error('Job type not found'));
        return;
      }
      
      types[index] = { ...types[index], ...updates };
      saveJobTypesToStorage(types);
      resolve(types[index]);
    }, 100);
  });
}

export async function deleteJobType(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/job-types/{id}
  // Expected response: { success: boolean }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const types = getJobTypesFromStorage();
      const index = types.findIndex(t => t.id === id);
      
      if (index === -1) {
        reject(new Error('Job type not found'));
        return;
      }
      
      types.splice(index, 1);
      saveJobTypesToStorage(types);
      resolve();
    }, 100);
  });
}

// Get uninvoiced jobs for a customer
export async function getUninvoicedJobs(customerId: string): Promise<Job[]> {
  // TODO: API Integration - GET /api/jobs/uninvoiced?customerId={customerId}
  // Expected response: { success: boolean, data: Job[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const jobTypes = getJobTypesFromStorage();
      
      // Filter for completed jobs that haven't been invoiced
      const uninvoicedJobs = jobs.filter(job => 
        job.customerId === customerId && 
        job.status.isCompleted &&
        !job.completedAt // This would be replaced with actual invoice tracking
      );
      
      // Populate job type information
      uninvoicedJobs.forEach(job => {
        job.jobType = jobTypes.find(jt => jt.id === job.jobTypeId);
      });
      
      resolve(uninvoicedJobs);
    }, 100);
  });
}

// Resource management functions
export async function getResources(): Promise<Resource[]> {
  // TODO: API Integration - GET /api/resources
  // Expected response: { success: boolean, data: Resource[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getResourcesFromStorage());
    }, 100);
  });
}

export async function getResourceById(id: string): Promise<Resource | null> {
  // TODO: API Integration - GET /api/resources/{id}
  // Expected response: { success: boolean, data: Resource }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const resources = getResourcesFromStorage();
      const resource = resources.find(r => r.id === id) || null;
      resolve(resource);
    }, 100);
  });
}

export async function createResource(resourceData: Omit<Resource, 'id' | 'createdAt' | 'updatedAt'>): Promise<Resource> {
  // TODO: API Integration - POST /api/resources
  // Expected request: { resource: ResourceData }
  // Expected response: { success: boolean, data: Resource }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const resources = getResourcesFromStorage();
      const newResource: Resource = {
        ...resourceData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      resources.push(newResource);
      saveResourcesToStorage(resources);
      resolve(newResource);
    }, 100);
  });
}

export async function updateResource(id: string, updates: Partial<Resource>): Promise<Resource> {
  // TODO: API Integration - PUT /api/resources/{id}
  // Expected request: { resource: Partial<Resource> }
  // Expected response: { success: boolean, data: Resource }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const resources = getResourcesFromStorage();
      const index = resources.findIndex(r => r.id === id);
      
      if (index === -1) {
        reject(new Error('Resource not found'));
        return;
      }
      
      resources[index] = {
        ...resources[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      saveResourcesToStorage(resources);
      resolve(resources[index]);
    }, 100);
  });
}

export async function deleteResource(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/resources/{id}
  // Expected response: { success: boolean }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const resources = getResourcesFromStorage();
      const index = resources.findIndex(r => r.id === id);
      
      if (index === -1) {
        reject(new Error('Resource not found'));
        return;
      }
      
      resources.splice(index, 1);
      saveResourcesToStorage(resources);
      resolve();
    }, 100);
  });
}

export async function checkResourceAvailability(
  resourceId: string, 
  startDateTime: string, 
  endDateTime: string
): Promise<{ isAvailable: boolean; conflicts: Job[] }> {
  // TODO: API Integration - GET /api/resources/{id}/availability?start={startDateTime}&end={endDateTime}
  // Expected response: { success: boolean, data: { isAvailable: boolean, conflicts: Job[] } }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      
      // Find jobs that use this resource and overlap with the requested time
      const conflicts = jobs.filter(job => 
        job.resources.some(r => r.resourceId === resourceId) &&
        job.scheduledDateTime &&
        job.scheduledDateTime < endDateTime &&
        // Assuming job end time is start time + duration
        new Date(new Date(job.scheduledDateTime).getTime() + (job.estimatedDuration || 0) * 60000).toISOString() > startDateTime
      );
      
      resolve({
        isAvailable: conflicts.length === 0,
        conflicts
      });
    }, 100);
  });
}

// Job assignment and staff management
export async function getJobsByStaff(staffId: string, startDate?: string, endDate?: string): Promise<Job[]> {
  // TODO: API Integration - GET /api/jobs/by-staff/{staffId}?startDate={startDate}&endDate={endDate}
  // Expected response: { success: boolean, data: Job[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const jobTypes = getJobTypesFromStorage();
      
      let staffJobs = jobs.filter(job => 
        job.assignedStaff.some(staff => staff.staffId === staffId)
      );
      
      // Filter by date range if provided
      if (startDate && endDate) {
        staffJobs = staffJobs.filter(job => 
          job.scheduledDateTime &&
          job.scheduledDateTime >= startDate &&
          job.scheduledDateTime <= endDate
        );
      }
      
      // Populate job type information
      staffJobs.forEach(job => {
        job.jobType = jobTypes.find(jt => jt.id === job.jobTypeId);
      });
      
      resolve(staffJobs);
    }, 100);
  });
}

// Recurring jobs functionality
export interface RecurringJobTemplate {
  id: string;
  name: string;
  jobTypeId: string;
  customerId: string;
  description: string;
  assignedStaff: AssignedStaff[];
  jobAddress: JobAddress;
  customFields: CustomField[];
  resources: JobResource[];
  recurrencePattern: RecurrencePattern;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface RecurrencePattern {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number; // Every X days/weeks/months/years
  daysOfWeek?: number[]; // 0-6, Sunday = 0 (for weekly)
  dayOfMonth?: number; // 1-31 (for monthly)
  endDate?: string;
  maxOccurrences?: number;
}

const RECURRING_JOBS_STORAGE_KEY = 'ejp_recurring_jobs';

function getRecurringJobsFromStorage(): RecurringJobTemplate[] {
  const stored = localStorage.getItem(RECURRING_JOBS_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveRecurringJobsToStorage(templates: RecurringJobTemplate[]): void {
  localStorage.setItem(RECURRING_JOBS_STORAGE_KEY, JSON.stringify(templates));
}

export async function createRecurringJob(templateData: Omit<RecurringJobTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<RecurringJobTemplate> {
  // TODO: API Integration - POST /api/recurring-jobs
  // Expected request: { template: RecurringJobTemplateData }
  // Expected response: { success: boolean, data: RecurringJobTemplate }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const templates = getRecurringJobsFromStorage();
      const newTemplate: RecurringJobTemplate = {
        ...templateData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      templates.push(newTemplate);
      saveRecurringJobsToStorage(templates);
      resolve(newTemplate);
    }, 100);
  });
}

export async function generateRecurringJobInstances(
  templateId: string, 
  startDate: string, 
  endDate: string
): Promise<Job[]> {
  // TODO: API Integration - POST /api/recurring-jobs/{id}/generate
  // Expected request: { startDate: string, endDate: string }
  // Expected response: { success: boolean, data: Job[] }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const templates = getRecurringJobsFromStorage();
      const template = templates.find(t => t.id === templateId);
      
      if (!template) {
        reject(new Error('Recurring job template not found'));
        return;
      }
      
      const instances: Job[] = [];
      const start = new Date(startDate);
      const end = new Date(endDate);
      let current = new Date(start);
      let occurrenceCount = 0;
      
      while (current <= end) {
        // Check if we've reached max occurrences
        if (template.recurrencePattern.maxOccurrences && 
            occurrenceCount >= template.recurrencePattern.maxOccurrences) {
          break;
        }
        
        // Check if we've reached end date
        if (template.recurrencePattern.endDate && 
            current > new Date(template.recurrencePattern.endDate)) {
          break;
        }
        
        // Create job instance
        const jobInstance: Job = {
          id: generateId(),
          title: `${template.name} - ${current.toLocaleDateString()}`,
          customerId: template.customerId,
          jobTypeId: template.jobTypeId,
          status: { id: '2', name: 'Scheduled', color: '#3B82F6', order: 2, isCompleted: false },
          description: template.description,
          scheduledDateTime: current.toISOString(),
          assignedStaff: template.assignedStaff,
          jobAddress: template.jobAddress,
          customFields: template.customFields,
          resources: template.resources,
          estimatedCost: { laborCost: 0, materialCost: 0, resourceCost: 0, totalCost: 0, breakdown: [] },
          priority: 'Medium',
          tags: ['recurring'],
          attachments: [],
          notes: `Generated from recurring template: ${template.name}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        instances.push(jobInstance);
        occurrenceCount++;
        
        // Calculate next occurrence
        switch (template.recurrencePattern.type) {
          case 'daily':
            current.setDate(current.getDate() + template.recurrencePattern.interval);
            break;
          case 'weekly':
            current.setDate(current.getDate() + (7 * template.recurrencePattern.interval));
            break;
          case 'monthly':
            current.setMonth(current.getMonth() + template.recurrencePattern.interval);
            break;
          case 'yearly':
            current.setFullYear(current.getFullYear() + template.recurrencePattern.interval);
            break;
        }
      }
      
      // Save generated instances
      const existingJobs = getJobsFromStorage();
      existingJobs.push(...instances);
      saveJobsToStorage(existingJobs);
      
      resolve(instances);
    }, 100);
  });
}

/*
API ENDPOINTS DOCUMENTATION FOR DEVELOPERS

The following API endpoints need to be implemented to replace the localStorage functionality:

=== JOB MANAGEMENT ===

GET /api/jobs
- Returns: { success: boolean, data: Job[] }
- Query params: customerId?, staffId?, startDate?, endDate?, status?

GET /api/jobs/{id}
- Returns: { success: boolean, data: Job }

POST /api/jobs
- Body: { job: Omit<Job, 'id' | 'createdAt' | 'updatedAt'> }
- Returns: { success: boolean, data: Job }

PUT /api/jobs/{id}
- Body: { job: Partial<Job> }
- Returns: { success: boolean, data: Job }

DELETE /api/jobs/{id}
- Returns: { success: boolean }

GET /api/jobs/by-customer/{customerId}
- Returns: { success: boolean, data: Job[] }

GET /api/jobs/by-staff/{staffId}
- Query params: startDate?, endDate?
- Returns: { success: boolean, data: Job[] }

GET /api/jobs/uninvoiced
- Query params: customerId?
- Returns: { success: boolean, data: Job[] }

=== JOB TYPES ===

GET /api/job-types
- Returns: { success: boolean, data: JobType[] }

POST /api/job-types
- Body: { jobType: Omit<JobType, 'id' | 'createdAt' | 'updatedAt'> }
- Returns: { success: boolean, data: JobType }

PUT /api/job-types/{id}
- Body: { jobType: Partial<JobType> }
- Returns: { success: boolean, data: JobType }

DELETE /api/job-types/{id}
- Returns: { success: boolean }

=== JOB STATUSES ===

GET /api/job-statuses
- Returns: { success: boolean, data: JobStatus[] }

POST /api/job-statuses
- Body: { status: Omit<JobStatus, 'id'> }
- Returns: { success: boolean, data: JobStatus }

PUT /api/job-statuses/order
- Body: { updates: { id: string, order: number }[] }
- Returns: { success: boolean, data: JobStatus[] }

=== RESOURCES ===

GET /api/resources
- Returns: { success: boolean, data: Resource[] }

GET /api/resources/{id}
- Returns: { success: boolean, data: Resource }

POST /api/resources
- Body: { resource: Omit<Resource, 'id' | 'createdAt' | 'updatedAt'> }
- Returns: { success: boolean, data: Resource }

PUT /api/resources/{id}
- Body: { resource: Partial<Resource> }
- Returns: { success: boolean, data: Resource }

DELETE /api/resources/{id}
- Returns: { success: boolean }

GET /api/resources/{id}/availability
- Query params: startDateTime, endDateTime
- Returns: { success: boolean, data: { isAvailable: boolean, conflicts: Job[] } }

=== RECURRING JOBS ===

GET /api/recurring-jobs
- Returns: { success: boolean, data: RecurringJobTemplate[] }

POST /api/recurring-jobs
- Body: { template: Omit<RecurringJobTemplate, 'id' | 'createdAt' | 'updatedAt'> }
- Returns: { success: boolean, data: RecurringJobTemplate }

POST /api/recurring-jobs/{id}/generate
- Body: { startDate: string, endDate: string }
- Returns: { success: boolean, data: Job[] }

=== COST CALCULATION ===

POST /api/jobs/calculate-cost
- Body: { jobTypeId: string, assignedStaff: AssignedStaff[], resources: JobResource[], customFields: CustomField[] }
- Returns: { success: boolean, data: JobCostEstimate }

*/ 