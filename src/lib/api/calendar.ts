// Calendar API - Job Calendar Management Functionality
// TODO: API Integration - Replace local storage with actual API endpoints
// Expected request format: { calendar: CalendarData }
// Expected response format: { success: boolean, data: CalendarEvent, message?: string }

import type { Job } from './jobs';
import type { Staff } from './staff';

export interface CalendarEvent {
  id: string;
  jobId: string;
  title: string;
  description?: string;
  startDateTime: string;
  endDateTime: string;
  allDay: boolean;
  assignedStaff: CalendarStaffAssignment[];
  customerId: string;
  customerName?: string;
  jobAddress?: string;
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled';
  color?: string;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  estimatedDuration?: number; // in minutes
  actualDuration?: number; // in minutes
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CalendarStaffAssignment {
  staffId: string;
  staffName: string;
  startTime?: string; // Can be different from job start time
  endTime?: string; // Can be different from job end time
  role?: string;
  confirmed: boolean;
}

export interface CalendarView {
  type: 'day' | 'week' | 'month' | 'year';
  startDate: string;
  endDate: string;
}

export interface CalendarFilter {
  staffIds?: string[];
  customerIds?: string[];
  statuses?: string[];
  priorities?: string[];
  jobTypes?: string[];
}

// Recurring event interfaces
export interface RecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number; // Every X days/weeks/months/years
  daysOfWeek?: number[]; // 0-6, Sunday = 0
  dayOfMonth?: number; // 1-31
  weekOfMonth?: number; // 1-4, or -1 for last week
  endDate?: string;
  occurrences?: number;
}

export interface RecurringEvent extends Omit<CalendarEvent, 'id' | 'startDateTime' | 'endDateTime'> {
  id: string;
  pattern: RecurringPattern;
  templateStartTime: string; // Time portion only
  templateEndTime: string; // Time portion only
  duration: number; // in minutes
  exceptions: string[]; // Dates to skip (ISO date strings)
  parentId?: string; // For individual instances
}

const CALENDAR_EVENTS_STORAGE_KEY = 'ejp_calendar_events';
const RECURRING_EVENTS_STORAGE_KEY = 'ejp_recurring_events';

// Helper functions
function getCalendarEventsFromStorage(): CalendarEvent[] {
  const stored = localStorage.getItem(CALENDAR_EVENTS_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveCalendarEventsToStorage(events: CalendarEvent[]): void {
  localStorage.setItem(CALENDAR_EVENTS_STORAGE_KEY, JSON.stringify(events));
}

function getRecurringEventsFromStorage(): RecurringEvent[] {
  const stored = localStorage.getItem(RECURRING_EVENTS_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveRecurringEventsToStorage(events: RecurringEvent[]): void {
  localStorage.setItem(RECURRING_EVENTS_STORAGE_KEY, JSON.stringify(events));
}

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Calendar event CRUD operations
export async function getCalendarEvents(startDate: string, endDate: string, filter?: CalendarFilter): Promise<CalendarEvent[]> {
  // TODO: API Integration - GET /api/calendar/events?startDate={startDate}&endDate={endDate}&filter={filter}
  // Expected response: { success: boolean, data: CalendarEvent[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      let events = getCalendarEventsFromStorage();
      
      // Filter by date range
      events = events.filter(event => 
        event.startDateTime >= startDate && event.startDateTime <= endDate
      );
      
      // Apply additional filters
      if (filter) {
        if (filter.staffIds && filter.staffIds.length > 0) {
          events = events.filter(event => 
            event.assignedStaff.some(staff => filter.staffIds!.includes(staff.staffId))
          );
        }
        
        if (filter.customerIds && filter.customerIds.length > 0) {
          events = events.filter(event => filter.customerIds!.includes(event.customerId));
        }
        
        if (filter.statuses && filter.statuses.length > 0) {
          events = events.filter(event => filter.statuses!.includes(event.status));
        }
        
        if (filter.priorities && filter.priorities.length > 0) {
          events = events.filter(event => filter.priorities!.includes(event.priority));
        }
      }
      
      resolve(events);
    }, 100);
  });
}

export async function getCalendarEventById(id: string): Promise<CalendarEvent | null> {
  // TODO: API Integration - GET /api/calendar/events/{id}
  // Expected response: { success: boolean, data: CalendarEvent }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const events = getCalendarEventsFromStorage();
      const event = events.find(e => e.id === id) || null;
      resolve(event);
    }, 100);
  });
}

export async function createCalendarEvent(eventData: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>): Promise<CalendarEvent> {
  // TODO: API Integration - POST /api/calendar/events
  // Expected request: { event: CalendarEventData }
  // Expected response: { success: boolean, data: CalendarEvent }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const events = getCalendarEventsFromStorage();
      const newEvent: CalendarEvent = {
        ...eventData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      events.push(newEvent);
      saveCalendarEventsToStorage(events);
      resolve(newEvent);
    }, 100);
  });
}

export async function updateCalendarEvent(id: string, updates: Partial<CalendarEvent>): Promise<CalendarEvent> {
  // TODO: API Integration - PUT /api/calendar/events/{id}
  // Expected request: { event: Partial<CalendarEvent> }
  // Expected response: { success: boolean, data: CalendarEvent }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const events = getCalendarEventsFromStorage();
      const index = events.findIndex(e => e.id === id);
      
      if (index === -1) {
        reject(new Error('Calendar event not found'));
        return;
      }
      
      events[index] = {
        ...events[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      saveCalendarEventsToStorage(events);
      resolve(events[index]);
    }, 100);
  });
}

export async function deleteCalendarEvent(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/calendar/events/{id}
  // Expected response: { success: boolean }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const events = getCalendarEventsFromStorage();
      const index = events.findIndex(e => e.id === id);
      
      if (index === -1) {
        reject(new Error('Calendar event not found'));
        return;
      }
      
      events.splice(index, 1);
      saveCalendarEventsToStorage(events);
      resolve();
    }, 100);
  });
}

// Drag and drop functionality
export async function moveCalendarEvent(id: string, newStartDateTime: string, newEndDateTime: string): Promise<CalendarEvent> {
  // TODO: API Integration - PUT /api/calendar/events/{id}/move
  // Expected request: { startDateTime: string, endDateTime: string }
  // Expected response: { success: boolean, data: CalendarEvent }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const events = getCalendarEventsFromStorage();
      const event = events.find(e => e.id === id);
      
      if (!event) {
        reject(new Error('Calendar event not found'));
        return;
      }
      
      event.startDateTime = newStartDateTime;
      event.endDateTime = newEndDateTime;
      event.updatedAt = new Date().toISOString();
      
      saveCalendarEventsToStorage(events);
      resolve(event);
    }, 100);
  });
}

// Staff assignment management
export async function assignStaffToEvent(eventId: string, staffAssignment: CalendarStaffAssignment): Promise<CalendarEvent> {
  // TODO: API Integration - POST /api/calendar/events/{id}/assign-staff
  // Expected request: { staffAssignment: CalendarStaffAssignment }
  // Expected response: { success: boolean, data: CalendarEvent }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const events = getCalendarEventsFromStorage();
      const event = events.find(e => e.id === eventId);
      
      if (!event) {
        reject(new Error('Calendar event not found'));
        return;
      }
      
      // Check if staff is already assigned
      const existingIndex = event.assignedStaff.findIndex(s => s.staffId === staffAssignment.staffId);
      
      if (existingIndex >= 0) {
        // Update existing assignment
        event.assignedStaff[existingIndex] = staffAssignment;
      } else {
        // Add new assignment
        event.assignedStaff.push(staffAssignment);
      }
      
      event.updatedAt = new Date().toISOString();
      saveCalendarEventsToStorage(events);
      resolve(event);
    }, 100);
  });
}

export async function removeStaffFromEvent(eventId: string, staffId: string): Promise<CalendarEvent> {
  // TODO: API Integration - DELETE /api/calendar/events/{id}/staff/{staffId}
  // Expected response: { success: boolean, data: CalendarEvent }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const events = getCalendarEventsFromStorage();
      const event = events.find(e => e.id === eventId);
      
      if (!event) {
        reject(new Error('Calendar event not found'));
        return;
      }
      
      event.assignedStaff = event.assignedStaff.filter(s => s.staffId !== staffId);
      event.updatedAt = new Date().toISOString();
      
      saveCalendarEventsToStorage(events);
      resolve(event);
    }, 100);
  });
}

// Job integration
export async function createEventFromJob(job: Job): Promise<CalendarEvent> {
  // TODO: API Integration - POST /api/calendar/events/from-job
  // Expected request: { jobId: string }
  // Expected response: { success: boolean, data: CalendarEvent }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const events = getCalendarEventsFromStorage();
      
      // Calculate end time based on estimated duration or default to 2 hours
      const startTime = new Date(job.scheduledDateTime || new Date());
      const duration = job.estimatedDuration || 120; // Default 2 hours in minutes
      const endTime = new Date(startTime.getTime() + duration * 60000);
      
      const newEvent: CalendarEvent = {
        id: generateId(),
        jobId: job.id,
        title: job.title,
        description: job.description,
        startDateTime: startTime.toISOString(),
        endDateTime: endTime.toISOString(),
        allDay: false,
        assignedStaff: job.assignedStaff.map(staff => ({
          staffId: staff.staffId,
          staffName: staff.staffName,
          startTime: staff.startTime,
          endTime: staff.endTime,
          role: staff.role,
          confirmed: false
        })),
        customerId: job.customerId,
        customerName: job.customerName,
        jobAddress: `${job.jobAddress.street}, ${job.jobAddress.city}`,
        status: 'Scheduled',
        priority: job.priority,
        estimatedDuration: job.estimatedDuration,
        notes: job.notes,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      events.push(newEvent);
      saveCalendarEventsToStorage(events);
      resolve(newEvent);
    }, 100);
  });
}

// Staff availability checking
export async function checkStaffAvailability(staffId: string, startDateTime: string, endDateTime: string): Promise<{
  isAvailable: boolean;
  conflicts: CalendarEvent[];
}> {
  // TODO: API Integration - GET /api/calendar/staff/{staffId}/availability?start={startDateTime}&end={endDateTime}
  // Expected response: { success: boolean, data: { isAvailable: boolean, conflicts: CalendarEvent[] } }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const events = getCalendarEventsFromStorage();
      
      // Find conflicting events for this staff member
      const conflicts = events.filter(event => {
        const hasStaff = event.assignedStaff.some(staff => staff.staffId === staffId);
        if (!hasStaff) return false;
        
        // Check for time overlap
        const eventStart = new Date(event.startDateTime);
        const eventEnd = new Date(event.endDateTime);
        const checkStart = new Date(startDateTime);
        const checkEnd = new Date(endDateTime);
        
        return (checkStart < eventEnd && checkEnd > eventStart);
      });
      
      resolve({
        isAvailable: conflicts.length === 0,
        conflicts
      });
    }, 100);
  });
}

// Recurring events
export async function createRecurringEvent(eventData: Omit<RecurringEvent, 'id' | 'createdAt' | 'updatedAt'>): Promise<RecurringEvent> {
  // TODO: API Integration - POST /api/calendar/recurring-events
  // Expected request: { recurringEvent: RecurringEventData }
  // Expected response: { success: boolean, data: RecurringEvent }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const recurringEvents = getRecurringEventsFromStorage();
      const newRecurringEvent: RecurringEvent = {
        ...eventData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      recurringEvents.push(newRecurringEvent);
      saveRecurringEventsToStorage(recurringEvents);
      resolve(newRecurringEvent);
    }, 100);
  });
}

export async function generateRecurringEventInstances(recurringEventId: string, startDate: string, endDate: string): Promise<CalendarEvent[]> {
  // TODO: API Integration - GET /api/calendar/recurring-events/{id}/instances?start={startDate}&end={endDate}
  // Expected response: { success: boolean, data: CalendarEvent[] }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const recurringEvents = getRecurringEventsFromStorage();
      const recurringEvent = recurringEvents.find(e => e.id === recurringEventId);
      
      if (!recurringEvent) {
        reject(new Error('Recurring event not found'));
        return;
      }
      
      const instances: CalendarEvent[] = [];
      const start = new Date(startDate);
      const end = new Date(endDate);
      let currentDate = new Date(start);
      
      while (currentDate <= end) {
        const dateString = currentDate.toISOString().split('T')[0];
        
        // Skip if this date is in exceptions
        if (recurringEvent.exceptions.includes(dateString)) {
          currentDate = getNextOccurrence(currentDate, recurringEvent.pattern);
          continue;
        }
        
        // Create instance for this date
        const startDateTime = new Date(`${dateString}T${recurringEvent.templateStartTime}`);
        const endDateTime = new Date(`${dateString}T${recurringEvent.templateEndTime}`);
        
        const instance: CalendarEvent = {
          id: `${recurringEvent.id}_${dateString}`,
          jobId: recurringEvent.jobId,
          title: recurringEvent.title,
          description: recurringEvent.description,
          startDateTime: startDateTime.toISOString(),
          endDateTime: endDateTime.toISOString(),
          allDay: recurringEvent.allDay,
          assignedStaff: recurringEvent.assignedStaff,
          customerId: recurringEvent.customerId,
          customerName: recurringEvent.customerName,
          jobAddress: recurringEvent.jobAddress,
          status: recurringEvent.status,
          color: recurringEvent.color,
          priority: recurringEvent.priority,
          estimatedDuration: recurringEvent.estimatedDuration,
          actualDuration: recurringEvent.actualDuration,
          notes: recurringEvent.notes,
          createdAt: recurringEvent.createdAt,
          updatedAt: recurringEvent.updatedAt
        };
        
        instances.push(instance);
        currentDate = getNextOccurrence(currentDate, recurringEvent.pattern);
      }
      
      resolve(instances);
    }, 100);
  });
}

// Helper function to calculate next occurrence
function getNextOccurrence(currentDate: Date, pattern: RecurringPattern): Date {
  const next = new Date(currentDate);
  
  switch (pattern.type) {
    case 'daily':
      next.setDate(next.getDate() + pattern.interval);
      break;
    case 'weekly':
      next.setDate(next.getDate() + (pattern.interval * 7));
      break;
    case 'monthly':
      next.setMonth(next.getMonth() + pattern.interval);
      break;
    case 'yearly':
      next.setFullYear(next.getFullYear() + pattern.interval);
      break;
  }
  
  return next;
}

// Calendar view helpers
export async function getCalendarViewData(view: CalendarView, filter?: CalendarFilter): Promise<{
  events: CalendarEvent[];
  recurringInstances: CalendarEvent[];
}> {
  // TODO: API Integration - GET /api/calendar/view?type={view.type}&start={view.startDate}&end={view.endDate}&filter={filter}
  // Expected response: { success: boolean, data: { events: CalendarEvent[], recurringInstances: CalendarEvent[] } }
  
  return new Promise(async (resolve) => {
    setTimeout(async () => {
      const events = await getCalendarEvents(view.startDate, view.endDate, filter);
      
      // Get all recurring event instances for this view
      const recurringEvents = getRecurringEventsFromStorage();
      let recurringInstances: CalendarEvent[] = [];
      
      for (const recurringEvent of recurringEvents) {
        const instances = await generateRecurringEventInstances(recurringEvent.id, view.startDate, view.endDate);
        recurringInstances = recurringInstances.concat(instances);
      }
      
      resolve({
        events,
        recurringInstances
      });
    }, 100);
  });
} 