// Email Service - Email sending functionality
// TODO: API Integration - Replace mock implementation with actual email service
// Expected request format: { to: string, subject: string, htmlContent: string, attachments?: Attachment[] }
// Expected response format: { success: boolean, messageId?: string, message?: string }

export interface EmailAttachment {
  filename: string;
  content: string; // Base64 encoded content
  contentType: string;
}

export interface EmailRequest {
  to: string;
  cc?: string[];
  bcc?: string[];
  subject: string;
  htmlContent: string;
  textContent?: string;
  attachments?: EmailAttachment[];
  replyTo?: string;
}

export interface EmailResponse {
  success: boolean;
  messageId?: string;
  message?: string;
  error?: string;
}

export interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpUser: string;
  smtpPassword: string;
  fromEmail: string;
  fromName: string;
  replyToEmail?: string;
}

// Mock email settings - replace with actual configuration
const DEFAULT_EMAIL_SETTINGS: EmailSettings = {
  smtpHost: 'smtp.gmail.com',
  smtpPort: 587,
  smtpUser: '<EMAIL>',
  smtpPassword: 'your-app-password',
  fromEmail: '<EMAIL>',
  fromName: 'Easy Job Planner',
  replyToEmail: '<EMAIL>'
};

// Storage keys
const EMAIL_SETTINGS_KEY = 'ejp_email_settings';
const EMAIL_HISTORY_KEY = 'ejp_email_history';

// Email history interface
export interface EmailHistoryItem {
  id: string;
  to: string;
  subject: string;
  status: 'sent' | 'failed' | 'pending';
  sentAt: string;
  error?: string;
  type: 'invoice' | 'appointment_confirmation' | 'appointment_reminder' | 'appointment_cancellation' | 'custom';
  relatedId?: string; // Invoice ID, appointment ID, etc.
}

// Helper functions
function getEmailSettingsFromStorage(): EmailSettings {
  const stored = localStorage.getItem(EMAIL_SETTINGS_KEY);
  return stored ? JSON.parse(stored) : DEFAULT_EMAIL_SETTINGS;
}

function saveEmailSettingsToStorage(settings: EmailSettings): void {
  localStorage.setItem(EMAIL_SETTINGS_KEY, JSON.stringify(settings));
}

function getEmailHistoryFromStorage(): EmailHistoryItem[] {
  const stored = localStorage.getItem(EMAIL_HISTORY_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveEmailHistoryToStorage(history: EmailHistoryItem[]): void {
  localStorage.setItem(EMAIL_HISTORY_KEY, JSON.stringify(history));
}

function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// Main email sending function
export async function sendEmail(emailRequest: EmailRequest, type: EmailHistoryItem['type'] = 'custom', relatedId?: string): Promise<EmailResponse> {
  // TODO: API Integration - POST /api/email/send
  // Expected request: EmailRequest
  // Expected response: EmailResponse
  
  return new Promise((resolve) => {
    setTimeout(() => {
      // Mock implementation - simulate email sending
      const success = Math.random() > 0.1; // 90% success rate for testing
      const messageId = success ? `msg_${generateId()}` : undefined;
      
      // Add to email history
      const historyItem: EmailHistoryItem = {
        id: generateId(),
        to: emailRequest.to,
        subject: emailRequest.subject,
        status: success ? 'sent' : 'failed',
        sentAt: new Date().toISOString(),
        error: success ? undefined : 'Mock error: Email service temporarily unavailable',
        type,
        relatedId
      };
      
      const history = getEmailHistoryFromStorage();
      history.unshift(historyItem); // Add to beginning
      saveEmailHistoryToStorage(history);
      
      const response: EmailResponse = {
        success,
        messageId,
        message: success ? 'Email sent successfully' : 'Failed to send email',
        error: success ? undefined : 'Mock error: Email service temporarily unavailable'
      };
      
      resolve(response);
    }, 1000 + Math.random() * 2000); // Simulate network delay
  });
}

// Send invoice email
export async function sendInvoiceEmail(
  invoiceId: string,
  customerEmail: string,
  customerName: string,
  invoiceNumber: string,
  invoiceAmount: number,
  pdfAttachment?: EmailAttachment
): Promise<EmailResponse> {
  const subject = `Invoice ${invoiceNumber} - ${formatCurrency(invoiceAmount)}`;
  
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
        <h1 style="margin: 0; font-size: 28px;">Invoice</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Easy Job Planner</p>
      </div>
      
      <div style="background: white; padding: 30px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 10px 10px;">
        <h2 style="color: #374151; margin-top: 0;">Dear ${customerName},</h2>
        
        <p style="color: #6b7280; line-height: 1.6;">
          Thank you for choosing our services. Please find your invoice attached to this email.
        </p>
        
        <div style="background: #f9fafb; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #3b82f6;">
          <h3 style="margin: 0 0 15px 0; color: #374151;">Invoice Details</h3>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
            <div><strong>Invoice Number:</strong> ${invoiceNumber}</div>
            <div><strong>Amount:</strong> ${formatCurrency(invoiceAmount)}</div>
            <div><strong>Issue Date:</strong> ${new Date().toLocaleDateString()}</div>
            <div><strong>Due Date:</strong> ${new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}</div>
          </div>
        </div>
        
        <div style="background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 25px 0; border: 1px solid #d1fae5;">
          <h3 style="margin: 0 0 10px 0; color: #065f46;">Payment Options</h3>
          <p style="margin: 0; color: #047857; line-height: 1.6;">
            You can pay this invoice online by clicking the link below, or use the payment details provided in the attached invoice.
          </p>
          <div style="margin-top: 15px;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 600;">
              Pay Online
            </a>
          </div>
        </div>
        
        <p style="color: #6b7280; line-height: 1.6;">
          If you have any questions about this invoice, please don't hesitate to contact us. We appreciate your business!
        </p>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
          <p style="margin: 0; color: #9ca3af; font-size: 14px;">
            Best regards,<br>
            <strong>Easy Job Planner Team</strong><br>
            Email: <EMAIL><br>
            Phone: (*************
          </p>
        </div>
      </div>
    </div>
  `;
  
  const textContent = `
Dear ${customerName},

Thank you for choosing our services. Please find your invoice attached.

Invoice Details:
- Invoice Number: ${invoiceNumber}
- Amount: ${formatCurrency(invoiceAmount)}
- Issue Date: ${new Date().toLocaleDateString()}
- Due Date: ${new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}

You can pay this invoice online or use the payment details provided in the attached invoice.

If you have any questions, please contact <NAME_EMAIL> or (*************.

Best regards,
Easy Job Planner Team
  `;
  
  const emailRequest: EmailRequest = {
    to: customerEmail,
    subject,
    htmlContent,
    textContent,
    attachments: pdfAttachment ? [pdfAttachment] : undefined
  };
  
  return sendEmail(emailRequest, 'invoice', invoiceId);
}

// Send appointment confirmation email
export async function sendAppointmentConfirmationEmail(
  customerEmail: string,
  customerName: string,
  appointmentDate: string,
  appointmentTime: string,
  serviceDescription: string,
  staffName: string
): Promise<EmailResponse> {
  const subject = `Appointment Confirmation - ${new Date(appointmentDate).toLocaleDateString()}`;
  
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
        <h1 style="margin: 0; font-size: 28px;">Appointment Confirmed</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Easy Job Planner</p>
      </div>
      
      <div style="background: white; padding: 30px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 10px 10px;">
        <h2 style="color: #374151; margin-top: 0;">Hello ${customerName},</h2>
        
        <p style="color: #6b7280; line-height: 1.6;">
          Your appointment has been confirmed! We're looking forward to providing you with excellent service.
        </p>
        
        <div style="background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #10b981;">
          <h3 style="margin: 0 0 15px 0; color: #065f46;">Appointment Details</h3>
          <div style="color: #047857; line-height: 1.8;">
            <div><strong>Date:</strong> ${new Date(appointmentDate).toLocaleDateString()}</div>
            <div><strong>Time:</strong> ${appointmentTime}</div>
            <div><strong>Service:</strong> ${serviceDescription}</div>
            <div><strong>Assigned Staff:</strong> ${staffName}</div>
          </div>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 25px 0; border: 1px solid #fbbf24;">
          <h3 style="margin: 0 0 10px 0; color: #92400e;">Important Reminders</h3>
          <ul style="margin: 0; color: #b45309; line-height: 1.6;">
            <li>Please ensure someone is available at the scheduled time</li>
            <li>If you need to reschedule, please contact us at least 24 hours in advance</li>
            <li>Our staff will arrive within the scheduled time window</li>
          </ul>
        </div>
        
        <p style="color: #6b7280; line-height: 1.6;">
          If you have any questions or need to make changes to your appointment, please contact us as soon as possible.
        </p>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
          <p style="margin: 0; color: #9ca3af; font-size: 14px;">
            Best regards,<br>
            <strong>Easy Job Planner Team</strong><br>
            Email: <EMAIL><br>
            Phone: (*************
          </p>
        </div>
      </div>
    </div>
  `;
  
  const emailRequest: EmailRequest = {
    to: customerEmail,
    subject,
    htmlContent
  };
  
  return sendEmail(emailRequest, 'appointment_confirmation');
}

// Send appointment reminder email
export async function sendAppointmentReminderEmail(
  customerEmail: string,
  customerName: string,
  appointmentDate: string,
  appointmentTime: string,
  serviceDescription: string,
  hoursUntilAppointment: number
): Promise<EmailResponse> {
  const subject = `Appointment Reminder - Tomorrow at ${appointmentTime}`;
  
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
        <h1 style="margin: 0; font-size: 28px;">Appointment Reminder</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Easy Job Planner</p>
      </div>
      
      <div style="background: white; padding: 30px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 10px 10px;">
        <h2 style="color: #374151; margin-top: 0;">Hello ${customerName},</h2>
        
        <p style="color: #6b7280; line-height: 1.6;">
          This is a friendly reminder about your upcoming appointment in ${hoursUntilAppointment} hours.
        </p>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #f59e0b;">
          <h3 style="margin: 0 0 15px 0; color: #92400e;">Appointment Details</h3>
          <div style="color: #b45309; line-height: 1.8;">
            <div><strong>Date:</strong> ${new Date(appointmentDate).toLocaleDateString()}</div>
            <div><strong>Time:</strong> ${appointmentTime}</div>
            <div><strong>Service:</strong> ${serviceDescription}</div>
          </div>
        </div>
        
        <p style="color: #6b7280; line-height: 1.6;">
          Please ensure someone is available at the scheduled time. If you need to reschedule or cancel, please contact us immediately.
        </p>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
          <p style="margin: 0; color: #9ca3af; font-size: 14px;">
            Best regards,<br>
            <strong>Easy Job Planner Team</strong><br>
            Email: <EMAIL><br>
            Phone: (*************
          </p>
        </div>
      </div>
    </div>
  `;
  
  const emailRequest: EmailRequest = {
    to: customerEmail,
    subject,
    htmlContent
  };
  
  return sendEmail(emailRequest, 'appointment_reminder');
}

// Email settings management
export async function getEmailSettings(): Promise<EmailSettings> {
  // TODO: API Integration - GET /api/email/settings
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getEmailSettingsFromStorage());
    }, 100);
  });
}

export async function updateEmailSettings(settings: EmailSettings): Promise<EmailSettings> {
  // TODO: API Integration - PUT /api/email/settings
  return new Promise((resolve) => {
    setTimeout(() => {
      saveEmailSettingsToStorage(settings);
      resolve(settings);
    }, 100);
  });
}

// Email history management
export async function getEmailHistory(limit: number = 50): Promise<EmailHistoryItem[]> {
  // TODO: API Integration - GET /api/email/history?limit={limit}
  return new Promise((resolve) => {
    setTimeout(() => {
      const history = getEmailHistoryFromStorage();
      resolve(history.slice(0, limit));
    }, 100);
  });
}

export async function getEmailHistoryForInvoice(invoiceId: string): Promise<EmailHistoryItem[]> {
  // TODO: API Integration - GET /api/email/history?relatedId={invoiceId}&type=invoice
  return new Promise((resolve) => {
    setTimeout(() => {
      const history = getEmailHistoryFromStorage();
      resolve(history.filter(item => item.relatedId === invoiceId && item.type === 'invoice'));
    }, 100);
  });
}

// Test email functionality
export async function sendTestEmail(toEmail: string): Promise<EmailResponse> {
  const emailRequest: EmailRequest = {
    to: toEmail,
    subject: 'Test Email from Easy Job Planner',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #3b82f6;">Test Email</h1>
        <p>This is a test email to verify that your email configuration is working correctly.</p>
        <p>If you received this email, your email service is properly configured!</p>
        <p>Sent at: ${new Date().toLocaleString()}</p>
      </div>
    `,
    textContent: `Test Email - This is a test email to verify your email configuration. Sent at: ${new Date().toLocaleString()}`
  };
  
  return sendEmail(emailRequest, 'custom');
}

// Utility function for currency formatting (if not imported)
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
} 