// Quotes API - Quote/Estimate Management Functionality
// TODO: API Integration - Replace local storage with actual API endpoints
// Expected request format: { quote: QuoteData }
// Expected response format: { success: boolean, data: Quote, message?: string }

export interface Quote {
  id: string;
  quoteNumber: string;
  customerId: string;
  customerName?: string; // Denormalized for display
  customerEmail?: string;
  customerAddress?: QuoteAddress;
  issueDate: string;
  expiryDate: string;
  status: QuoteStatus;
  templateId?: string;
  sections: QuoteSection[];
  lineItems: QuoteLineItem[];
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  notes?: string;
  terms?: string;
  createdAt: string;
  updatedAt: string;
  acceptedAt?: string;
  convertedToInvoiceAt?: string;
  relatedInvoiceId?: string;
}

export interface QuoteSection {
  id: string;
  type: 'cover' | 'observational' | 'recommendations' | 'lineItems' | 'text' | 'images';
  title: string;
  content: string;
  order: number;
  isVisible: boolean;
  images?: QuoteImage[];
  lineItems?: QuoteLineItem[];
}

export interface QuoteImage {
  id: string;
  url: string;
  caption?: string;
  order: number;
}

export interface QuoteLineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  taxRate: number;
  taxAmount: number;
  lineTotal: number;
  images?: QuoteImage[];
  additionalInfo?: string;
}

export interface QuoteAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface QuoteStatus {
  id: string;
  name: 'Draft' | 'Sent' | 'Accepted' | 'Rejected' | 'Expired' | 'Converted';
  color: string;
}

export interface QuoteTemplate {
  id: string;
  name: string;
  description?: string;
  sections: QuoteTemplateSection[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface QuoteTemplateSection {
  id: string;
  type: 'cover' | 'observational' | 'recommendations' | 'lineItems' | 'text';
  title: string;
  defaultContent: string;
  order: number;
  isRequired: boolean;
}

// AI Quote Generation interfaces
export interface AIQuoteRequest {
  customerInfo: {
    name: string;
    address: string;
    propertyType: string;
  };
  jobDetails: {
    jobType: string;
    description: string;
    urgency: 'Low' | 'Medium' | 'High';
    estimatedDuration?: string;
  };
  observations: string[];
  requirements: string[];
  additionalNotes?: string;
}

export interface AIQuoteResponse {
  coverSection: string;
  observationalData: string;
  recommendations: string;
  lineItems: {
    description: string;
    quantity: number;
    unitPrice: number;
    notes?: string;
  }[];
  totalEstimate: number;
  timeframe: string;
  terms: string;
}

const QUOTES_STORAGE_KEY = 'ejp_quotes';
const QUOTE_TEMPLATES_STORAGE_KEY = 'ejp_quote_templates';

// Default quote statuses
const DEFAULT_QUOTE_STATUSES: QuoteStatus[] = [
  { id: '1', name: 'Draft', color: '#6B7280' },
  { id: '2', name: 'Sent', color: '#3B82F6' },
  { id: '3', name: 'Accepted', color: '#10B981' },
  { id: '4', name: 'Rejected', color: '#EF4444' },
  { id: '5', name: 'Expired', color: '#F59E0B' },
  { id: '6', name: 'Converted', color: '#8B5CF6' }
];

// Helper functions
function getQuotesFromStorage(): Quote[] {
  const stored = localStorage.getItem(QUOTES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveQuotesToStorage(quotes: Quote[]): void {
  localStorage.setItem(QUOTES_STORAGE_KEY, JSON.stringify(quotes));
}

function getQuoteTemplatesFromStorage(): QuoteTemplate[] {
  const stored = localStorage.getItem(QUOTE_TEMPLATES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveQuoteTemplatesToStorage(templates: QuoteTemplate[]): void {
  localStorage.setItem(QUOTE_TEMPLATES_STORAGE_KEY, JSON.stringify(templates));
}

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function generateQuoteNumber(): string {
  const quotes = getQuotesFromStorage();
  const year = new Date().getFullYear();
  const count = quotes.filter(quote => quote.quoteNumber.startsWith(`QUO-${year}`)).length + 1;
  return `QUO-${year}-${count.toString().padStart(4, '0')}`;
}

// Initialize sample data if none exists
async function initializeSampleQuotes() {
  const existingQuotes = getQuotesFromStorage();
  if (existingQuotes.length === 0) {
    const sampleQuotes: Omit<Quote, 'id' | 'quoteNumber' | 'createdAt' | 'updatedAt'>[] = [
      {
        customerId: 'customer-1',
        customerName: 'John Smith',
        customerEmail: '<EMAIL>',
        customerAddress: {
          street: '123 Main St',
          city: 'Springfield',
          state: 'IL',
          zipCode: '62701',
          country: 'USA'
        },
        issueDate: new Date().toISOString().split('T')[0],
        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: DEFAULT_QUOTE_STATUSES[1], // Sent
        sections: [
          {
            id: generateId(),
            type: 'cover',
            title: 'Project Overview',
            content: 'Complete plumbing renovation for residential property.',
            order: 1,
            isVisible: true
          },
          {
            id: generateId(),
            type: 'recommendations',
            title: 'Recommendations',
            content: 'We recommend upgrading all fixtures and installing new copper piping throughout.',
            order: 2,
            isVisible: true
          }
        ],
        lineItems: [
          {
            id: generateId(),
            description: 'Plumbing Service - Complete Renovation',
            quantity: 1,
            unitPrice: 2500.00,
            taxRate: 10,
            taxAmount: 250.00,
            lineTotal: 2750.00
          }
        ],
        subtotal: 2500.00,
        taxAmount: 250.00,
        discountAmount: 0,
        totalAmount: 2750.00,
        notes: 'Quote valid for 30 days',
        terms: 'Payment due within 30 days of acceptance'
      },
      {
        customerId: 'customer-2',
        customerName: 'Sarah Johnson',
        customerEmail: '<EMAIL>',
        customerAddress: {
          street: '456 Oak Ave',
          city: 'Springfield',
          state: 'IL',
          zipCode: '62702',
          country: 'USA'
        },
        issueDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        expiryDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: DEFAULT_QUOTE_STATUSES[2], // Accepted
        sections: [
          {
            id: generateId(),
            type: 'cover',
            title: 'Electrical Installation',
            content: 'New electrical panel and wiring installation.',
            order: 1,
            isVisible: true
          }
        ],
        lineItems: [
          {
            id: generateId(),
            description: 'Electrical Panel Upgrade',
            quantity: 1,
            unitPrice: 1800.00,
            taxRate: 10,
            taxAmount: 180.00,
            lineTotal: 1980.00
          }
        ],
        subtotal: 1800.00,
        taxAmount: 180.00,
        discountAmount: 0,
        totalAmount: 1980.00,
        notes: 'Work to be completed within 2 weeks',
        terms: 'Payment due within 30 days of acceptance',
        acceptedAt: new Date().toISOString()
      }
    ];

    const quotes = getQuotesFromStorage();
    for (const quoteData of sampleQuotes) {
      const newQuote: Quote = {
        ...quoteData,
        id: generateId(),
        quoteNumber: generateQuoteNumber(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      quotes.push(newQuote);
    }
    saveQuotesToStorage(quotes);
  }
}

// Quote CRUD operations
export async function getQuotes(): Promise<Quote[]> {
  // TODO: API Integration - GET /api/quotes
  // Expected response: { success: boolean, data: Quote[] }

  return new Promise(async (resolve) => {
    setTimeout(async () => {
      await initializeSampleQuotes();
      resolve(getQuotesFromStorage());
    }, 100);
  });
}

export async function getQuoteById(id: string): Promise<Quote | null> {
  // TODO: API Integration - GET /api/quotes/{id}
  // Expected response: { success: boolean, data: Quote }

  return new Promise((resolve) => {
    setTimeout(() => {
      const quotes = getQuotesFromStorage();
      const quote = quotes.find(q => q.id === id) || null;
      resolve(quote);
    }, 100);
  });
}

export async function getQuotesByCustomer(customerId: string): Promise<Quote[]> {
  // TODO: API Integration - GET /api/quotes?customerId={customerId}
  // Expected response: { success: boolean, data: Quote[] }

  return new Promise((resolve) => {
    setTimeout(() => {
      const quotes = getQuotesFromStorage();
      const customerQuotes = quotes.filter(q => q.customerId === customerId);
      resolve(customerQuotes);
    }, 100);
  });
}

export async function createQuote(quoteData: Omit<Quote, 'id' | 'quoteNumber' | 'createdAt' | 'updatedAt'>): Promise<Quote> {
  // TODO: API Integration - POST /api/quotes
  // Expected request: { quote: QuoteData }
  // Expected response: { success: boolean, data: Quote }

  return new Promise((resolve) => {
    setTimeout(() => {
      const quotes = getQuotesFromStorage();
      const newQuote: Quote = {
        ...quoteData,
        id: generateId(),
        quoteNumber: generateQuoteNumber(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      quotes.push(newQuote);
      saveQuotesToStorage(quotes);
      resolve(newQuote);
    }, 100);
  });
}

export async function updateQuote(id: string, updates: Partial<Quote>): Promise<Quote> {
  // TODO: API Integration - PUT /api/quotes/{id}
  // Expected request: { quote: Partial<Quote> }
  // Expected response: { success: boolean, data: Quote }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const quotes = getQuotesFromStorage();
      const index = quotes.findIndex(q => q.id === id);

      if (index === -1) {
        reject(new Error('Quote not found'));
        return;
      }

      quotes[index] = {
        ...quotes[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };

      saveQuotesToStorage(quotes);
      resolve(quotes[index]);
    }, 100);
  });
}

export async function deleteQuote(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/quotes/{id}
  // Expected response: { success: boolean }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const quotes = getQuotesFromStorage();
      const index = quotes.findIndex(q => q.id === id);

      if (index === -1) {
        reject(new Error('Quote not found'));
        return;
      }

      quotes.splice(index, 1);
      saveQuotesToStorage(quotes);
      resolve();
    }, 100);
  });
}

// Quote template management
export async function getQuoteTemplates(): Promise<QuoteTemplate[]> {
  // TODO: API Integration - GET /api/quote-templates
  // Expected response: { success: boolean, data: QuoteTemplate[] }

  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getQuoteTemplatesFromStorage());
    }, 100);
  });
}

export async function createQuoteTemplate(templateData: Omit<QuoteTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<QuoteTemplate> {
  // TODO: API Integration - POST /api/quote-templates
  // Expected request: { template: QuoteTemplateData }
  // Expected response: { success: boolean, data: QuoteTemplate }

  return new Promise((resolve) => {
    setTimeout(() => {
      const templates = getQuoteTemplatesFromStorage();
      const newTemplate: QuoteTemplate = {
        ...templateData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      templates.push(newTemplate);
      saveQuoteTemplatesToStorage(templates);
      resolve(newTemplate);
    }, 100);
  });
}

export async function updateQuoteTemplate(id: string, updates: Partial<QuoteTemplate>): Promise<QuoteTemplate> {
  // TODO: API Integration - PUT /api/quote-templates/{id}
  // Expected request: { template: Partial<QuoteTemplate> }
  // Expected response: { success: boolean, data: QuoteTemplate }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const templates = getQuoteTemplatesFromStorage();
      const index = templates.findIndex(t => t.id === id);

      if (index === -1) {
        reject(new Error('Template not found'));
        return;
      }

      templates[index] = {
        ...templates[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };

      saveQuoteTemplatesToStorage(templates);
      resolve(templates[index]);
    }, 100);
  });
}

export async function deleteQuoteTemplate(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/quote-templates/{id}
  // Expected response: { success: boolean }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const templates = getQuoteTemplatesFromStorage();
      const index = templates.findIndex(t => t.id === id);

      if (index === -1) {
        reject(new Error('Template not found'));
        return;
      }

      templates.splice(index, 1);
      saveQuoteTemplatesToStorage(templates);
      resolve();
    }, 100);
  });
}

// AI-powered quote generation
export async function generateAIQuote(request: AIQuoteRequest): Promise<AIQuoteResponse> {
  // TODO: API Integration - POST /api/quotes/generate-ai
  // Expected request: { quoteRequest: AIQuoteRequest, knowledgeBase?: string }
  // Expected response: { success: boolean, data: AIQuoteResponse }
  // Integration with Gemini Flash API

  return new Promise((resolve) => {
    setTimeout(() => {
      // Simulated AI response for now
      const mockResponse: AIQuoteResponse = {
        coverSection: `Dear ${request.customerInfo.name},\n\nThank you for considering our services for your ${request.jobDetails.jobType} project at ${request.customerInfo.address}. We have carefully reviewed your requirements and prepared this comprehensive quote.`,
        observationalData: `Property Assessment:\n- Property Type: ${request.customerInfo.propertyType}\n- Job Type: ${request.jobDetails.jobType}\n- Observations: ${request.observations.join(', ')}`,
        recommendations: `Based on our assessment, we recommend:\n${request.requirements.map(req => `- ${req}`).join('\n')}`,
        lineItems: [
          {
            description: `${request.jobDetails.jobType} - Standard Service`,
            quantity: 1,
            unitPrice: 150,
            notes: 'Includes basic materials and labor'
          },
          {
            description: 'Additional Materials',
            quantity: 1,
            unitPrice: 75,
            notes: 'Premium grade materials as specified'
          }
        ],
        totalEstimate: 225,
        timeframe: request.jobDetails.estimatedDuration || '2-3 business days',
        terms: 'Payment due within 30 days of completion. 50% deposit required to commence work.'
      };

      resolve(mockResponse);
    }, 2000); // Simulate AI processing time
  });
}

// Convert quote to invoice
export async function convertQuoteToInvoice(quoteId: string): Promise<string> {
  // TODO: API Integration - POST /api/quotes/{id}/convert-to-invoice
  // Expected response: { success: boolean, data: { invoiceId: string } }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const quotes = getQuotesFromStorage();
      const quote = quotes.find(q => q.id === quoteId);

      if (!quote) {
        reject(new Error('Quote not found'));
        return;
      }

      // Update quote status
      quote.status = { id: '6', name: 'Converted', color: '#8B5CF6' };
      quote.convertedToInvoiceAt = new Date().toISOString();
      quote.updatedAt = new Date().toISOString();

      // Generate mock invoice ID
      const invoiceId = generateId();
      quote.relatedInvoiceId = invoiceId;

      saveQuotesToStorage(quotes);
      resolve(invoiceId);
    }, 100);
  });
}

// Get quote statuses
export async function getQuoteStatuses(): Promise<QuoteStatus[]> {
  // TODO: API Integration - GET /api/quote-statuses
  // Expected response: { success: boolean, data: QuoteStatus[] }

  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(DEFAULT_QUOTE_STATUSES);
    }, 100);
  });
}

// Calculate quote totals
export function calculateQuoteTotals(lineItems: QuoteLineItem[], discountAmount: number = 0): {
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
} {
  const subtotal = lineItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const taxAmount = lineItems.reduce((sum, item) => sum + item.taxAmount, 0);
  const totalAmount = subtotal + taxAmount - discountAmount;

  return {
    subtotal,
    taxAmount,
    totalAmount
  };
}

// Get uninvoiced quotes for a customer
export async function getUninvoicedQuotes(customerId: string): Promise<Quote[]> {
  // TODO: API Integration - GET /api/quotes/uninvoiced?customerId={customerId}
  // Expected response: { success: boolean, data: Quote[] }

  return new Promise((resolve) => {
    setTimeout(() => {
      const quotes = getQuotesFromStorage();
      const uninvoicedQuotes = quotes.filter(q =>
        q.customerId === customerId &&
        q.status.name === 'Accepted' &&
        !q.convertedToInvoiceAt
      );
      resolve(uninvoicedQuotes);
    }, 100);
  });
}