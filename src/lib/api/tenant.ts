// Tenant API - Tenant management functionality

import { authenticatedFetch } from '$lib/stores/auth';

export interface TenantDto {
  id: string; // Using string for Guid in TypeScript
  name: string;
  createdAtUtc: string;
  updatedAtUtc?: string;
}

const API_BASE_URL = 'https://app-ejp-api-test-gpfsbbe0gdgahkhp.uksouth-01.azurewebsites.net';

/**
 * Get tenant by ID
 * @param id - The tenant ID (GUID)
 * @param token - Optional token for authentication (used during login)
 * @returns Promise resolving to TenantDto or null if not found
 */
export async function getTenantById(id: string, token?: string): Promise<TenantDto | null> {
  try {
    let response: Response;
    
    if (token) {
      // Use direct fetch with provided token (for login process)
      response = await fetch(`${API_BASE_URL}/Tenants/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
    } else {
      // Use authenticated fetch (for normal app usage)
      response = await authenticatedFetch(`${API_BASE_URL}/Tenants/${id}`);
    }
    
    if (!response.ok) {
      if (response.status === 404) {
        console.warn(`Tenant with ID ${id} not found`);
        return null;
      }
      throw new Error(`Failed to fetch tenant: ${response.statusText}`);
    }

    const tenant: TenantDto = await response.json();
    return tenant;
  } catch (error) {
    console.error('Error fetching tenant:', error);
    throw error;
  }
} 