// Customer/Contact API - CRM Functionality
// TODO: API Integration - Replace local storage with actual API endpoints
// Expected request format: { contact: ContactData }
// Expected response format: { success: boolean, data: Contact, message?: string }

export interface Contact {
  id: string;
  fullName: string;
  companyName?: string;
  emails: ContactEmail[];
  phones: ContactPhone[];
  addresses: ContactAddress[];
  status: 'Lead' | 'Customer' | 'Archived';
  notes: ContactNote[];
  checklists: ContactChecklist[];
  communicationTimeline: CommunicationEntry[];
  createdAt: string;
  updatedAt: string;
}

export interface ContactEmail {
  id: string;
  email: string;
  type: 'Primary' | 'Work' | 'Personal' | 'Other';
  isPrimary: boolean;
}

export interface ContactPhone {
  id: string;
  phone: string;
  type: 'Mobile' | 'Work' | 'Home' | 'Fax' | 'Other';
  isPrimary: boolean;
}

export interface ContactAddress {
  id: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  type: 'Home' | 'Work' | 'Billing' | 'Shipping' | 'Other';
  isPrimary: boolean;
}

export interface ContactNote {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
}

export interface ContactChecklist {
  id: string;
  title: string;
  items: ChecklistItem[];
  createdAt: string;
  updatedAt: string;
}

export interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
  completedAt?: string;
}

export interface CommunicationEntry {
  id: string;
  type: 'Email' | 'SMS' | 'WhatsApp' | 'Note' | 'Job' | 'Invoice' | 'Quote' | 'Call';
  title: string;
  description?: string;
  date: string;
  relatedId?: string; // ID of related job, invoice, etc.
}

const CUSTOMERS_STORAGE_KEY = 'ejp_customers';

// Helper function to get customers from local storage
function getCustomersFromStorage(): Contact[] {
  const stored = localStorage.getItem(CUSTOMERS_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

// Helper function to save customers to local storage
function saveCustomersToStorage(customers: Contact[]): void {
  localStorage.setItem(CUSTOMERS_STORAGE_KEY, JSON.stringify(customers));
}

// Generate unique ID
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Get all customers/contacts
export async function getContacts(): Promise<Contact[]> {
  // TODO: API Integration - GET /api/customers
  // Expected response: { success: boolean, data: Contact[] }

  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getCustomersFromStorage());
    }, 100); // Simulate API delay
  });
}

// Get contact by ID
export async function getContactById(id: string): Promise<Contact | null> {
  // TODO: API Integration - GET /api/contacts/{id}
  // Expected response: { success: boolean, data: Contact }

  return new Promise((resolve) => {
    setTimeout(() => {
      const customers = getCustomersFromStorage();
      const customer = customers.find(c => c.id === id) || null;
      resolve(customer);
    }, 100);
  });
}

// Create new contact
export async function createContact(contactData: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>): Promise<Contact> {
  // TODO: API Integration - POST /api/contacts
  // Expected request: { contact: ContactData }
  // Expected response: { success: boolean, data: Contact }

  return new Promise((resolve) => {
    setTimeout(() => {
      const customers = getCustomersFromStorage();
      const newCustomer: Contact = {
        ...contactData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      customers.push(newCustomer);
      saveCustomersToStorage(customers);
      resolve(newCustomer);
    }, 100);
  });
}

// Update contact
export async function updateContact(id: string, updates: Partial<Contact>): Promise<Contact> {
  // TODO: API Integration - PUT /api/contacts/{id}
  // Expected request: { contact: Partial<Contact> }
  // Expected response: { success: boolean, data: Contact }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const customers = getCustomersFromStorage();
      const index = customers.findIndex(c => c.id === id);

      if (index === -1) {
        reject(new Error('Customer not found'));
        return;
      }

      customers[index] = {
        ...customers[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };

      saveCustomersToStorage(customers);
      resolve(customers[index]);
    }, 100);
  });
}

// Delete contact
export async function deleteContact(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/contacts/{id}
  // Expected response: { success: boolean }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const customers = getCustomersFromStorage();
      const index = customers.findIndex(c => c.id === id);

      if (index === -1) {
        reject(new Error('Customer not found'));
        return;
      }

      customers.splice(index, 1);
      saveCustomersToStorage(customers);
      resolve();
    }, 100);
  });
}

// Add note to contact
export async function addContactNote(contactId: string, content: string): Promise<ContactNote> {
  // TODO: API Integration - POST /api/contacts/{id}/notes
  // Expected request: { content: string }
  // Expected response: { success: boolean, data: ContactNote }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const contacts = getContactsFromStorage();
      const contact = contacts.find(c => c.id === contactId);

      if (!contact) {
        reject(new Error('Contact not found'));
        return;
      }

      const newNote: ContactNote = {
        id: generateId(),
        content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      contact.notes.push(newNote);
      contact.updatedAt = new Date().toISOString();
      saveContactsToStorage(contacts);
      resolve(newNote);
    }, 100);
  });
}

// Update contact note
export async function updateContactNote(contactId: string, noteId: string, content: string): Promise<ContactNote> {
  // TODO: API Integration - PUT /api/contacts/{id}/notes/{noteId}
  // Expected request: { content: string }
  // Expected response: { success: boolean, data: ContactNote }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const contacts = getContactsFromStorage();
      const contact = contacts.find(c => c.id === contactId);

      if (!contact) {
        reject(new Error('Contact not found'));
        return;
      }

      const note = contact.notes.find(n => n.id === noteId);
      if (!note) {
        reject(new Error('Note not found'));
        return;
      }

      note.content = content;
      note.updatedAt = new Date().toISOString();
      contact.updatedAt = new Date().toISOString();
      saveContactsToStorage(contacts);
      resolve(note);
    }, 100);
  });
}

// Delete contact note
export async function deleteContactNote(contactId: string, noteId: string): Promise<void> {
  // TODO: API Integration - DELETE /api/contacts/{id}/notes/{noteId}
  // Expected response: { success: boolean }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const contacts = getContactsFromStorage();
      const contact = contacts.find(c => c.id === contactId);

      if (!contact) {
        reject(new Error('Contact not found'));
        return;
      }

      const noteIndex = contact.notes.findIndex(n => n.id === noteId);
      if (noteIndex === -1) {
        reject(new Error('Note not found'));
        return;
      }

      contact.notes.splice(noteIndex, 1);
      contact.updatedAt = new Date().toISOString();
      saveContactsToStorage(contacts);
      resolve();
    }, 100);
  });
}

// Add checklist to contact
export async function addContactChecklist(contactId: string, title: string): Promise<ContactChecklist> {
  // TODO: API Integration - POST /api/contacts/{id}/checklists
  // Expected request: { title: string }
  // Expected response: { success: boolean, data: ContactChecklist }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const contacts = getContactsFromStorage();
      const contact = contacts.find(c => c.id === contactId);

      if (!contact) {
        reject(new Error('Contact not found'));
        return;
      }

      const newChecklist: ContactChecklist = {
        id: generateId(),
        title,
        items: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      contact.checklists.push(newChecklist);
      contact.updatedAt = new Date().toISOString();
      saveContactsToStorage(contacts);
      resolve(newChecklist);
    }, 100);
  });
}

// Update contact checklist
export async function updateContactChecklist(contactId: string, checklistId: string, updates: Partial<ContactChecklist>): Promise<ContactChecklist> {
  // TODO: API Integration - PUT /api/contacts/{id}/checklists/{checklistId}
  // Expected request: { checklist: Partial<ContactChecklist> }
  // Expected response: { success: boolean, data: ContactChecklist }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const contacts = getContactsFromStorage();
      const contact = contacts.find(c => c.id === contactId);

      if (!contact) {
        reject(new Error('Contact not found'));
        return;
      }

      const checklist = contact.checklists.find(cl => cl.id === checklistId);
      if (!checklist) {
        reject(new Error('Checklist not found'));
        return;
      }

      Object.assign(checklist, updates, { updatedAt: new Date().toISOString() });
      contact.updatedAt = new Date().toISOString();
      saveContactsToStorage(contacts);
      resolve(checklist);
    }, 100);
  });
}

// Delete contact checklist
export async function deleteContactChecklist(contactId: string, checklistId: string): Promise<void> {
  // TODO: API Integration - DELETE /api/contacts/{id}/checklists/{checklistId}
  // Expected response: { success: boolean }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const contacts = getContactsFromStorage();
      const contact = contacts.find(c => c.id === contactId);

      if (!contact) {
        reject(new Error('Contact not found'));
        return;
      }

      const checklistIndex = contact.checklists.findIndex(cl => cl.id === checklistId);
      if (checklistIndex === -1) {
        reject(new Error('Checklist not found'));
        return;
      }

      contact.checklists.splice(checklistIndex, 1);
      contact.updatedAt = new Date().toISOString();
      saveContactsToStorage(contacts);
      resolve();
    }, 100);
  });
}

// Add item to checklist
export async function addChecklistItem(contactId: string, checklistId: string, text: string): Promise<ChecklistItem> {
  // TODO: API Integration - POST /api/contacts/{id}/checklists/{checklistId}/items
  // Expected request: { text: string }
  // Expected response: { success: boolean, data: ChecklistItem }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const contacts = getContactsFromStorage();
      const contact = contacts.find(c => c.id === contactId);

      if (!contact) {
        reject(new Error('Contact not found'));
        return;
      }

      const checklist = contact.checklists.find(cl => cl.id === checklistId);
      if (!checklist) {
        reject(new Error('Checklist not found'));
        return;
      }

      const newItem: ChecklistItem = {
        id: generateId(),
        text,
        completed: false
      };

      checklist.items.push(newItem);
      checklist.updatedAt = new Date().toISOString();
      contact.updatedAt = new Date().toISOString();
      saveContactsToStorage(contacts);
      resolve(newItem);
    }, 100);
  });
}

// Update checklist item
export async function updateChecklistItem(contactId: string, checklistId: string, itemId: string, updates: Partial<ChecklistItem>): Promise<ChecklistItem> {
  // TODO: API Integration - PUT /api/contacts/{id}/checklists/{checklistId}/items/{itemId}
  // Expected request: { item: Partial<ChecklistItem> }
  // Expected response: { success: boolean, data: ChecklistItem }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const contacts = getContactsFromStorage();
      const contact = contacts.find(c => c.id === contactId);

      if (!contact) {
        reject(new Error('Contact not found'));
        return;
      }

      const checklist = contact.checklists.find(cl => cl.id === checklistId);
      if (!checklist) {
        reject(new Error('Checklist not found'));
        return;
      }

      const item = checklist.items.find(i => i.id === itemId);
      if (!item) {
        reject(new Error('Checklist item not found'));
        return;
      }

      Object.assign(item, updates);
      if (updates.completed !== undefined) {
        item.completedAt = updates.completed ? new Date().toISOString() : undefined;
      }

      checklist.updatedAt = new Date().toISOString();
      contact.updatedAt = new Date().toISOString();
      saveContactsToStorage(contacts);
      resolve(item);
    }, 100);
  });
}

// Delete checklist item
export async function deleteChecklistItem(contactId: string, checklistId: string, itemId: string): Promise<void> {
  // TODO: API Integration - DELETE /api/contacts/{id}/checklists/{checklistId}/items/{itemId}
  // Expected response: { success: boolean }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const contacts = getContactsFromStorage();
      const contact = contacts.find(c => c.id === contactId);

      if (!contact) {
        reject(new Error('Contact not found'));
        return;
      }

      const checklist = contact.checklists.find(cl => cl.id === checklistId);
      if (!checklist) {
        reject(new Error('Checklist not found'));
        return;
      }

      const itemIndex = checklist.items.findIndex(i => i.id === itemId);
      if (itemIndex === -1) {
        reject(new Error('Checklist item not found'));
        return;
      }

      checklist.items.splice(itemIndex, 1);
      checklist.updatedAt = new Date().toISOString();
      contact.updatedAt = new Date().toISOString();
      saveContactsToStorage(contacts);
      resolve();
    }, 100);
  });
}

// Add communication entry
export async function addCommunicationEntry(contactId: string, entry: Omit<CommunicationEntry, 'id'>): Promise<CommunicationEntry> {
  // TODO: API Integration - POST /api/contacts/{id}/communications
  // Expected request: { communication: CommunicationEntryData }
  // Expected response: { success: boolean, data: CommunicationEntry }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const contacts = getContactsFromStorage();
      const contact = contacts.find(c => c.id === contactId);

      if (!contact) {
        reject(new Error('Contact not found'));
        return;
      }

      const newEntry: CommunicationEntry = {
        ...entry,
        id: generateId()
      };

      contact.communicationTimeline.push(newEntry);
      contact.communicationTimeline.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      contact.updatedAt = new Date().toISOString();
      saveContactsToStorage(contacts);
      resolve(newEntry);
    }, 100);
  });
}