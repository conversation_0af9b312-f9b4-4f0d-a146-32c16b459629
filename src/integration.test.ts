import { describe, it, expect, beforeEach, vi } from 'vitest';
import { formatCurrency, getCurrentCurrency, setCurrency } from '$lib/config/currency';

describe('Integration Tests', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  describe('Currency Integration', () => {
    it('should use GBP as default currency throughout the application', () => {
      const currency = getCurrentCurrency();
      expect(currency.code).toBe('GBP');
      expect(currency.symbol).toBe('£');

      const formatted = formatCurrency(100);
      expect(formatted).toBe('£100.00');
    });

    it('should format various amounts correctly in GBP', () => {
      const testCases = [
        { amount: 0, expected: '£0.00' },
        { amount: 1.5, expected: '£1.50' },
        { amount: 1000, expected: '£1,000.00' },
        { amount: 1234.56, expected: '£1,234.56' },
        { amount: -50, expected: '-£50.00' }
      ];

      testCases.forEach(({ amount, expected }) => {
        const formatted = formatCurrency(amount);
        expect(formatted).toBe(expected);
      });
    });

    it('should persist currency changes', () => {
      // This would be tested in a real browser environment
      // For now, we just verify the localStorage interaction
      const mockSetItem = vi.spyOn(Storage.prototype, 'setItem');

      // Use setCurrency
      setCurrency('USD');

      expect(mockSetItem).toHaveBeenCalledWith(
        'ejp_currency_config',
        expect.stringContaining('USD')
      );
    });
  });

  describe('Application State', () => {
    it('should handle localStorage gracefully when not available', () => {
      // Mock localStorage to throw an error
      const originalGetItem = localStorage.getItem;
      localStorage.getItem = vi.fn().mockImplementation(() => {
        throw new Error('localStorage not available');
      });

      // Should still return default currency
      const currency = getCurrentCurrency();
      expect(currency.code).toBe('GBP');

      // Restore original implementation
      localStorage.getItem = originalGetItem;
    });

    it('should handle corrupted localStorage data', () => {
      localStorage.setItem('ejp_currency_config', 'invalid json data');

      const currency = getCurrentCurrency();
      expect(currency.code).toBe('GBP'); // Should fallback to default
    });
  });

  describe('Data Consistency', () => {
    it('should maintain consistent currency formatting across components', () => {
      // Test that all currency formatting uses the same configuration
      const testAmount = 1234.56;

      // These should all produce the same result
      const format1 = formatCurrency(testAmount);
      const format2 = formatCurrency(testAmount, getCurrentCurrency());

      expect(format1).toBe(format2);
      expect(format1).toBe('£1,234.56');
    });
  });
});
