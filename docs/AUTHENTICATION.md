# Authentication System

This document explains the authentication system implemented in the EJP Frontend application.

## Overview

The application now uses a real API authentication system with bearer tokens. When users log in, they receive a JWT token that is used for all subsequent API calls.

## Key Components

### 1. Auth Store (`src/lib/stores/auth.ts`)

The auth store manages user authentication state and provides functions for login/logout.

**Key Features:**
- Stores user information and JWT token
- Automatically saves/loads from localStorage
- Handles login with real API endpoint
- Provides authenticated fetch utility
- Automatically logs out on 401 responses

**Usage:**
```typescript
import { login, logout, user, authToken } from '$lib/stores/auth';

// Login
const result = await login('<EMAIL>', 'password');
if (result.success) {
  // Login successful
} else {
  console.error(result.error);
}

// Logout
logout();

// Check if user is logged in
user.subscribe(currentUser => {
  if (currentUser) {
    console.log('User is logged in:', currentUser.email);
  }
});
```

### 2. API Utility (`src/lib/utils/api.ts`)

Provides a centralized way to make authenticated API calls.

**Usage:**
```typescript
import { api, ApiError } from '$lib/utils/api';

try {
  // GET request
  const users = await api.get<User[]>('/api/users');
  
  // POST request
  const newUser = await api.post<User>('/api/users', {
    name: 'John Doe',
    email: '<EMAIL>'
  });
  
  // PUT request
  const updatedUser = await api.put<User>('/api/users/123', {
    name: 'Jane Doe'
  });
  
  // DELETE request
  await api.delete('/api/users/123');
  
} catch (error) {
  if (error instanceof ApiError) {
    console.error('API Error:', error.status, error.message);
  }
}
```

### 3. Login Page (`src/routes/(account)/login/+page.svelte`)

Updated to use the real API endpoint with proper error handling and loading states.

**Features:**
- Real API integration
- Loading states during login
- Proper error messages
- Form validation
- Automatic redirect on success

## API Endpoint

**Login Endpoint:** `https://app-ejp-api.azurewebsites.net/Auth/login`

**Request Format:**
```json
{
  "email": "<EMAIL>",
  "password": "userpassword"
}
```

**Response Format:**
```json
{
  "email": "<EMAIL>",
  "password": null,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## Automatic Token Management

The system automatically:
1. Stores the JWT token in localStorage
2. Includes the token in all API requests as `Authorization: Bearer <token>`
3. Redirects to login page if token is invalid (401 response)
4. Clears stored data on logout

## Updating Existing API Calls

To update existing API calls to use authentication, replace localStorage-based calls with the new API utility:

**Before:**
```typescript
// Old localStorage approach
const data = localStorage.getItem('contacts');
const contacts = data ? JSON.parse(data) : [];
```

**After:**
```typescript
// New authenticated API approach
import { api } from '$lib/utils/api';

const response = await api.get<{ contacts: Contact[] }>('/api/contacts');
const contacts = response.contacts;
```

See `src/lib/api/contacts-api-example.ts` for a complete example of how to update an API module.

## Error Handling

The system provides consistent error handling:

1. **Network Errors:** Caught and wrapped in ApiError
2. **401 Unauthorized:** Automatically logs out user and redirects to login
3. **Other HTTP Errors:** Thrown as ApiError with status code and message

**Example Error Handling:**
```typescript
try {
  const data = await api.get('/api/data');
} catch (error) {
  if (error instanceof ApiError) {
    if (error.status === 404) {
      console.log('Data not found');
    } else {
      console.error('API Error:', error.message);
    }
  } else {
    console.error('Unexpected error:', error);
  }
}
```

## Security Features

1. **Automatic Token Expiry:** Invalid tokens trigger automatic logout
2. **Secure Storage:** Tokens stored in localStorage (consider httpOnly cookies for production)
3. **HTTPS Only:** All API calls use HTTPS
4. **No Token Exposure:** Tokens not logged or exposed in UI

## Testing

Use the `test-login.html` file to test the login API endpoint directly:

1. Open `test-login.html` in a browser
2. Enter credentials
3. Click "Test Login" to verify API connectivity

## Migration Checklist

To fully migrate to the authenticated API system:

1. ✅ Update auth store with real API
2. ✅ Update login page
3. ✅ Create API utility
4. ⏳ Update all API modules (contacts, jobs, quotes, etc.)
5. ⏳ Update all stores to use new API
6. ⏳ Add error handling throughout app
7. ⏳ Test all functionality with real API

## Environment Configuration

For different environments, update the API base URL in `src/lib/utils/api.ts`:

```typescript
// Development
const API_BASE_URL = 'https://app-ejp-api.azurewebsites.net';

// Production
const API_BASE_URL = 'https://your-production-api.com';

// Local development
const API_BASE_URL = 'http://localhost:3000';
``` 